import csv
from pathlib import Path

def create_text_report():
    """Create a plain text report from the cleaned CSV"""
    input_file = 'questions_results_cleaned.csv'
    output_file = 'questions_results_report.txt'
    
    if not Path(input_file).exists():
        print(f"Error: {input_file} not found!")
        return
    
    with open(input_file, 'r', encoding='utf-8') as infile:
        reader = csv.DictReader(infile)
        rows = list(reader)
    
    with open(output_file, 'w', encoding='utf-8') as outfile:
        outfile.write("API AGENT QUESTIONS AND RESPONSES REPORT\n")
        outfile.write("=" * 80 + "\n\n")
        
        for row in rows:
            outfile.write(f"Question {row['index']}: {row['question']}\n")
            outfile.write("-" * 80 + "\n")
            outfile.write(f"Response: {row['cleaned_response']}\n")
            
            if row['followup_questions']:
                outfile.write(f"\nFollow-up Questions:\n")
                for followup in row['followup_questions'].split(' | '):
                    outfile.write(f"  • {followup}\n")
            
            outfile.write(f"\nStatus: {row['status']}\n")
            outfile.write("\n" + "=" * 80 + "\n\n")
        
        # Add summary at the end
        total = len(rows)
        successful = sum(1 for r in rows if r['status'] == 'success')
        with_followups = sum(1 for r in rows if r['followup_questions'])
        
        outfile.write("SUMMARY\n")
        outfile.write("-" * 80 + "\n")
        outfile.write(f"Total Questions: {total}\n")
        outfile.write(f"Successful: {successful}\n")
        outfile.write(f"Failed: {total - successful}\n")
        outfile.write(f"With Follow-up Questions: {with_followups}\n")
    
    print(f"Text report saved to: {output_file}")

if __name__ == "__main__":
    create_text_report()