import json
from pathlib import Path
import sys

sys.path.append(str(Path(__file__).parent.parent))

from app.services.api_query_agent.type import CallStep
from app.core.config import SERVICES_PROPERTIES

def test_url_construction():
    """Test URL construction with different run modes"""
    
    print("URL Construction Test")
    print("=" * 80)
    print(f"\nCurrent run.mode: {SERVICES_PROPERTIES.RUN_MODE}")
    print(f"Base URL from services.properties: {SERVICES_PROPERTIES.BASE_URL}")
    print("\n" + "=" * 80)
    
    # Test cases
    test_cases = [
        {
            "description": "API endpoint",
            "url": "/api/domain/wealthdomain/account/dynamic/query/topAccountsByEndingMarketValue",
            "base_url": "http://app-data-manager:8002" # NOSONAR
        },
        {
            "description": "Workflow endpoint",
            "url": "/workflow/v1/instances/execute/sync/d0a7sue8eadump4qcv0g",
            "base_url": "http://workhorse:8080" # NOSONAR
        },
        {
            "description": "V1 instances endpoint",
            "url": "/v1/instances/execute/sync/domain/wealthdomain/ct000bbavopq04o4a1n0",
            "base_url": "http://workhorse:8080" # NOSONAR
        }
    ]
    
    print("\nTest Cases:")
    print("-" * 80)
    
    for i, test in enumerate(test_cases, 1):
        print(f"\n{i}. {test['description']}")
        print(f"   Endpoint path: {test['url']}")
        print(f"   Expected base URL (PRODUCTION mode): {test['base_url']}")
        
        # Create CallStep object
        call_step = CallStep(
            description=test['description'],
            url=test['url'],
            base_url=test['base_url']
        )
        
        # Simulate URL construction logic from api_agent.py
        if SERVICES_PROPERTIES.RUN_MODE == "TEST":
            constructed_url = SERVICES_PROPERTIES.BASE_URL + call_step.url
            print(f"   Constructed URL (TEST mode): {constructed_url}")
        else:
            base_url = call_step.base_url
            if not base_url:
                # Fallback logic
                if call_step.url.startswith("/api"):
                    base_url = "http://app-data-manager:8002" # NOSONAR
                elif call_step.url.startswith("/workflow"):
                    base_url = "http://workhorse:8080" # NOSONAR
                elif call_step.url.startswith("/v1/instances"):
                    base_url = "http://workhorse:8080" # NOSONAR
                else:
                    base_url = "http://app-data-manager:8002" # NOSONAR
            constructed_url = base_url + call_step.url
            print(f"   Constructed URL (PRODUCTION mode): {constructed_url}")
    
    print("\n" + "=" * 80)
    print("\nTo test different modes:")
    print("1. Set run.mode: \"TEST\" in services.properties to use SERVICES_PROPERTIES.BASE_URL")
    print("2. Set run.mode: \"PRODUCTION\" in services.properties to use base_url from api_context.json")

if __name__ == "__main__":
    test_url_construction()