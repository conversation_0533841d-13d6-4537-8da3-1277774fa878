import asyncio
import json
import csv
import traceback
from datetime import datetime
from pathlib import Path
import sys
import re
from html.parser import HTMLParser

sys.path.append(str(Path(__file__).parent.parent))

from app.services.api_query_agent.api_agent import ApiAgent

class HTMLTextExtractor(HTMLParser):
    """Extract text from HTML, removing all tags."""
    def __init__(self):
        super().__init__()
        self.text = []
        
    def handle_data(self, data):
        self.text.append(data)
        
    def get_text(self):
        return ' '.join(self.text).strip()

def remove_html_tags(text):
    """Remove HTML tags from text."""
    if not isinstance(text, str):
        return text
    
    # First try to extract text using HTMLParser
    parser = HTMLTextExtractor()
    parser.feed(text)
    clean_text = parser.get_text()
    
    # Fallback to regex if needed
    if not clean_text:
        clean_text = re.sub('<.*?>', '', text)
    
    return clean_text.strip()

def clean_text_field(item):
    """Clean HTML from text field in an item."""
    if not isinstance(item, dict):
        return
    
    if 'text' in item:
        item['text'] = remove_html_tags(item['text'])

def clean_structured_data_field(item):
    """Clean HTML from structured_data field in an item."""
    if not isinstance(item, dict):
        return
    
    structured_data = item.get('structured_data', {})
    if isinstance(structured_data, dict) and 'html' in structured_data:
        item['structured_data']['html'] = remove_html_tags(structured_data['html'])

def clean_item_fields(item):
    """Clean all HTML fields in a single item."""
    clean_text_field(item)
    clean_structured_data_field(item)

def clean_response_data(response):
    """Clean HTML tags from response data."""
    if not isinstance(response, dict):
        return response
    
    data = response.get('data', [])
    if not isinstance(data, list):
        return response
    
    for item in data:
        clean_item_fields(item)
    
    return response

# List of all questions to process
QUESTIONS = [
    "show me the top 5 accounts",
    "Get cash value for account number ********",
    "Search account ********",
    'Create a service request for "*********"',
    "Can you please give me a summary of my book of business?",
    "show me the total accounts",
    "Show me a list of my clients",
    "how many total accounts are there",
    "how many total client accounts are there",
    "How many of these 3,626 accounts are active vs. dormant?",
    "what is the total cash balance",
    "Show cash value of all client accounts",
    "tell the total value of the accounts",
    "are there any announcements?",
    "How many clients are there?",
    "Which asset class has the highest allocation?",
    "What is the total ACH in?",
    "can you tell the total ACH out?",
    "can you tell the total Beneficiary update in service requests?",
    "can you tell total service requests - wire out?",
    "What is the total number of open service requests?",
    "Show service requests breakdown by type",
    "Show service requests",
    "Filter service requests by status",
    "Show number of onboarding requests",
    "show asset allocation over the last 6 months",
    "What are the top 5 clients contributing to the margin balance?",
    "What stages are the 104/3567 onboarding requests in?",
    "Can you provide a status report of high-value onboarding clients?",
    "How many active accounts are there",
    "How many inactive accounts are there",
    "What is the current balance of account number ********",
    "What is the current balance of account number ********",
    "What type of account is ******** — IRA, Joint, Individual?",
    "What type of account is ******** — IRA, Joint, Individual?",
    "Has there been any recent activity in account ********?",
    "Has there been any recent activity in account ********?",
    "Can you show asset growth over the last 12 months?",
    "Can you show asset growth for the last quarter of 2024?",
    "Which clients have increased their portfolio value this year?",
    "Which accounts have more than $50,000 in idle cash?",
    "What is the total cash value across all accounts?",
    "Can I get a list of clients with zero market value but positive cash?",
    "show the accounts for which validation failed",
    "which is the registration type having most no. of accounts",
    "give the total margin balance for the top 5 accounts",
    "How many SIMPLE IRA accounts are there?",
    "How many accounts with Registration type as Individual are there?",
    "Can you break down accounts by registration type?",
    "Show account details ********",
    "How many simple ira accounts are there?",
    "Which is the most commonly held security?",
    "Give me top 5 holdings",
    "Show top 5 holdings by market value",
    "How many accounts have cash balances greater than $100,000?",
    "Which accounts have had no activity in the past 6 months?",
    "Which households have the highest total account value?",
    "Give me the top 5 holdings across my book.",
    "What is the average market value per household?",
    "Can I see the transaction history for account ********?",
    "Is this registration code for a retirement or taxable account?",
    "What is the current total value of the account?",
    "How has the account value changed over the past 6 months?",
    "How many accounts do I manage?",
    "Which clients have the highest number of accounts?",
    "Can you show me all accounts under Joe Shmoe Household?",
    "Which clients have no active accounts currently",
    "How many accounts are associated with rep code 065KZ01?",
    "Can I get a performance summary of all accounts under the rep codes assigned to me?",
    "How many buy transactions occurred last week?",
    "What's the total value of all sell transactions this quarter?",
    "What is the total quantity of shares traded this year?",
    "Which accounts traded the largest amounts this week?",
    "Show me trades above $500,000 in value",
    "Which is the most commonly traded security across all accounts?",
    "Give me a list of the top 5 traded symbols by volume",
    "Show me all transactions for CUSIP *********",
    "How many different securities have been traded in the past 6 months?",
    "List the top 10 sell transactions by value"
]

async def run_question(agent: ApiAgent, question: str, index: int) -> dict:
    """Run a single question and return the result."""
    print(f"\n[{index}/{len(QUESTIONS)}] Processing: {question}")
    try:
        start_time = datetime.now()
        result = await agent.run(question)
        
        # Clean HTML tags from the response
        clean_result = clean_response_data(result)
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        return {
            "index": index,
            "question": question,
            "response": json.dumps(clean_result),
            "status": "success",
            "duration": duration,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        print(f"Error processing question {index}: {e}")
        traceback.print_exc()
        return {
            "index": index,
            "question": question,
            "response": str(e),
            "status": "error",
            "duration": 0,
            "timestamp": datetime.now().isoformat()
        }

async def process_batch(agent: ApiAgent, questions: list, start_index: int, batch_name: str) -> list:
    """Process a batch of questions."""
    results = []
    for i, question in enumerate(questions):
        result = await run_question(agent, question, start_index + i)
        results.append(result)
        
        # Save intermediate results after each question
        save_results(results, f"questions_results_{batch_name}_intermediate.csv")
        
        # Add a small delay between questions to avoid overwhelming the API
        await asyncio.sleep(0.5)
    
    return results

async def main():
    """Main function to process all questions in batches."""
    print(f"Starting batch processing of {len(QUESTIONS)} questions...")
    
    # Initialize the agent
    context_data = {}
    agent = ApiAgent(context_data=context_data)
    
    # Process questions in batches
    batch_size = 10
    all_results = []
    
    for batch_start in range(0, len(QUESTIONS), batch_size):
        batch_end = min(batch_start + batch_size, len(QUESTIONS))
        batch_questions = QUESTIONS[batch_start:batch_end]
        batch_num = batch_start // batch_size + 1
        
        print(f"\n=== Processing Batch {batch_num} (Questions {batch_start + 1}-{batch_end}) ===")
        
        try:
            batch_results = await process_batch(
                agent, 
                batch_questions, 
                batch_start + 1, 
                f"batch_{batch_num}"
            )
            all_results.extend(batch_results)
            
            # Save results after each batch
            save_results(all_results, f"questions_results_up_to_batch_{batch_num}.csv")
            
            print(f"\nBatch {batch_num} completed. Waiting before next batch...")
            await asyncio.sleep(2)  # Wait between batches
            
        except Exception as e:
            print(f"Error in batch {batch_num}: {e}")
            traceback.print_exc()
            # Continue with next batch
    
    # Save final results
    save_results(all_results, "questions_results_final_cleaned.csv")
    
    # Also save a version with just question and cleaned text response
    save_simplified_results(all_results, "questions_results_simplified.csv")
    
    # Generate summary report
    generate_summary(all_results)
    
    print("\nBatch processing completed!")
    print(f"Results saved to: questions_results_final_cleaned.csv")
    print(f"Simplified results saved to: questions_results_simplified.csv")
    print(f"Summary saved to: questions_results_summary.txt")

def save_results(results: list, filename: str):
    """Save results to CSV file."""
    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['index', 'question', 'response', 'status', 'duration', 'timestamp']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        
        writer.writeheader()
        for result in results:
            writer.writerow(result)

def extract_text_from_item(item):
    """Extract text from a single data item."""
    if isinstance(item, dict) and 'text' in item:
        return item['text']
    return None

def extract_texts_from_response_data(response_data):
    """Extract all text fields from response data."""
    if not isinstance(response_data, dict):
        return []
    
    data = response_data.get('data', [])
    if not isinstance(data, list):
        return []
    
    return [text for text in (extract_text_from_item(item) for item in data) if text]

def parse_response_text(result):
    """Parse response text from a result."""
    if result['status'] != 'success':
        return f"Error: {result['response']}"
    
    try:
        response_data = json.loads(result['response'])
        texts = extract_texts_from_response_data(response_data)
        return ' | '.join(texts) if texts else result['response']
    except (json.JSONDecodeError, ValueError, TypeError):
        return result['response']

def create_simplified_row(result):
    """Create a simplified row dictionary from a result."""
    return {
        'index': result['index'],
        'question': result['question'],
        'response_text': parse_response_text(result),
        'status': result['status']
    }

def save_simplified_results(results: list, filename: str):
    """Save simplified results with just question and extracted text."""
    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['index', 'question', 'response_text', 'status']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        
        writer.writeheader()
        for result in results:
            writer.writerow(create_simplified_row(result))

def generate_summary(results: list):
    """Generate a summary report of the results."""
    total = len(results)
    successful = sum(1 for r in results if r['status'] == 'success')
    failed = sum(1 for r in results if r['status'] == 'error')
    avg_duration = sum(r['duration'] for r in results if r['status'] == 'success') / max(successful, 1)
    
    summary = f"""
Batch Processing Summary
========================
Total Questions: {total}
Successful: {successful}
Failed: {failed}
Success Rate: {(successful/total)*100:.2f}%
Average Duration: {avg_duration:.2f} seconds

Failed Questions:
"""
    
    for r in results:
        if r['status'] == 'error':
            summary += f"\n- Question {r['index']}: {r['question']}\n  Error: {r['response']}\n"
    
    with open('questions_results_summary.txt', 'w', encoding='utf-8') as f:
        f.write(summary)
    
    print(summary)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except Exception as e:
        print(f"An error occurred during batch processing: {e}")
        traceback.print_exc()