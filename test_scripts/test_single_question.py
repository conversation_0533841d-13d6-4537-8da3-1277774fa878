import asyncio
import json
from pathlib import Path
import sys

sys.path.append(str(Path(__file__).parent.parent))

from app.services.api_query_agent.api_agent import ApiAgent
from run_questions_batch_improved import remove_html_tags, clean_response_data

async def test_single_question():
    """Test a single question to verify HTML cleaning."""
    question = "What is the account status for account ********?"
    
    context_data = {}
    agent = ApiAgent(context_data=context_data)
    
    print(f"Testing question: {question}")
    print("=" * 80)
    
    result = await agent.run(question)
    
    print("\nOriginal Response:")
    print(json.dumps(result, indent=2))
    
    # Clean the response
    clean_result = clean_response_data(result)
    
    print("\nCleaned Response:")
    print(json.dumps(clean_result, indent=2))
    
    # Extract just the text
    if 'data' in clean_result and isinstance(clean_result['data'], list):
        for item in clean_result['data']:
            if isinstance(item, dict) and 'text' in item:
                print(f"\nExtracted text: {item['text']}")

if __name__ == "__main__":
    asyncio.run(test_single_question())