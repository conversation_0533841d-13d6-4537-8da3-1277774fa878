# Test Scripts for AI Assistant

This folder contains essential test scripts and utilities for testing the API Agent functionality.

## Core Scripts

### Testing Scripts
- **`run_questions_batch.py`** - Runs a batch of predefined questions through the API agent with logging
- **`run_questions_batch_improved.py`** - Enhanced version with HTML cleaning, better error handling, and batch processing
- **`test_single_question.py`** - Test a single question through the API agent
- **`test_api_agent.py`** - Basic test script for API agent functionality

### Processing Utilities
- **`process_results_and_clean.py`** - Processes CSV results, removes HTML tags, and extracts follow-up questions
- **`create_text_report.py`** - Creates a plain text report from cleaned CSV results

### Analysis Tools
- **`analyze_api_urls.py`** - Analyzes API endpoints and URL construction from api_context.json
- **`test_url_construction.py`** - Tests URL construction logic for different run modes

## Usage

To run a batch test:
```bash
cd /home/<USER>/code/ai-assistant
python test_scripts/run_questions_batch.py
```

To process and clean the results:
```bash
python test_scripts/process_results_and_clean.py
python test_scripts/create_text_report.py
```

To test a single question:
```bash
python test_scripts/test_single_question.py
```