import csv
import json
import re
from html.parser import HTMLParser
from pathlib import Path

class HTMLStripper(HTMLParser):
    """Simple HTML tag stripper"""
    def __init__(self):
        super().__init__()
        self.reset()
        self.strict = False
        self.convert_charrefs = True
        self.text = []
    
    def handle_data(self, data):
        self.text.append(data)
    
    def get_text(self):
        return ' '.join(self.text)

def strip_html_tags(html_text):
    """Remove HTML tags from text"""
    if not html_text:
        return ""
    
    # First try to use HTMLParser
    s = HTMLStripper()
    s.feed(html_text)
    text = s.get_text()
    
    # Clean up extra whitespace
    text = re.sub(r'\s+', ' ', text).strip()
    
    return text

def parse_response_data(response_data):
    """Parse response data from string to dict if needed"""
    if not isinstance(response_data, str):
        return response_data
    
    try:
        return json.loads(response_data)
    except (json.JSONDecodeError, ValueError):
        # Return original string if it's not valid JSON
        return response_data

def extract_text_field(item):
    """Extract text field from an item"""
    if not isinstance(item, dict):
        return []
    
    text_parts = []
    
    # Extract direct text field
    if 'text' in item:
        text_parts.append(strip_html_tags(item['text']))
    
    # Extract HTML from structured data
    structured_data = item.get('structured_data', {})
    if isinstance(structured_data, dict) and 'html' in structured_data:
        text_parts.append(strip_html_tags(structured_data['html']))
    
    return text_parts

def format_table_data(structured_data):
    """Format table data into readable text"""
    if not isinstance(structured_data, dict):
        return []
    
    text_parts = []
    headers = structured_data.get('headers', [])
    rows = structured_data.get('rows', [])
    
    if headers:
        text_parts.append("Table Headers: " + " | ".join(headers))
    
    # Show first 5 rows
    for i, row in enumerate(rows[:5]):
        text_parts.append(f"Row {i+1}: " + " | ".join(str(cell) for cell in row))
    
    # Add count of remaining rows
    if len(rows) > 5:
        text_parts.append(f"... and {len(rows) - 5} more rows")
    
    return text_parts

def extract_item_text(item):
    """Extract all text from a single item"""
    text_parts = extract_text_field(item)
    
    # Handle table data specially
    if isinstance(item, dict) and item.get('type') == 'table':
        structured_data = item.get('structured_data', {})
        text_parts.extend(format_table_data(structured_data))
    
    return text_parts

def extract_text_from_response(response_data):
    """Extract and clean text from the response JSON structure"""
    # Parse response data if it's a string
    response_data = parse_response_data(response_data)
    
    # Return early if not the expected format
    if not isinstance(response_data, dict) or 'data' not in response_data:
        return str(response_data)
    
    # Extract text from all items
    text_parts = []
    for item in response_data.get('data', []):
        text_parts.extend(extract_item_text(item))
    
    return " ".join(text_parts)

def get_question_from_option(option):
    """Get question text from a single option dict"""
    if not isinstance(option, dict):
        return None
    return option.get('option') or option.get('action') or None

def extract_questions_from_options(item):
    """Extract questions from an options item"""
    if not isinstance(item, dict) or item.get('type') != 'options':
        return []
    
    options = item.get('options', [])
    # Use list comprehension with filter to reduce complexity
    questions = [q for q in (get_question_from_option(opt) for opt in options) if q]
    
    return questions

def extract_followup_questions(response_data):
    """Extract follow-up questions from the response"""
    # Parse response data if it's a string
    parsed_data = parse_response_data(response_data)
    
    # Return early if not the expected format
    if not isinstance(parsed_data, dict) or 'data' not in parsed_data:
        return ""
    
    # Use list comprehension to flatten questions from all items
    all_questions = [
        question 
        for item in parsed_data.get('data', [])
        for question in extract_questions_from_options(item)
    ]
    
    return " | ".join(all_questions)

def process_results():
    """Process the results CSV and create a cleaned version"""
    input_file = 'questions_results_final.csv'
    output_file = 'questions_results_cleaned.csv'
    
    if not Path(input_file).exists():
        print(f"Error: {input_file} not found!")
        return
    
    print(f"Processing {input_file}...")
    
    cleaned_results = []
    
    with open(input_file, 'r', encoding='utf-8') as infile:
        reader = csv.DictReader(infile)
        
        for row in reader:
            question = row['question']
            response = row['response']
            status = row['status']
            
            if status == 'success':
                cleaned_text = extract_text_from_response(response)
                followup_questions = extract_followup_questions(response)
            else:
                cleaned_text = f"Error: {response}"
                followup_questions = ""
            
            cleaned_results.append({
                'index': row['index'],
                'question': question,
                'cleaned_response': cleaned_text,
                'followup_questions': followup_questions,
                'status': status
            })
    
    # Write cleaned results
    with open(output_file, 'w', newline='', encoding='utf-8') as outfile:
        fieldnames = ['index', 'question', 'cleaned_response', 'followup_questions', 'status']
        writer = csv.DictWriter(outfile, fieldnames=fieldnames)
        
        writer.writeheader()
        for result in cleaned_results:
            writer.writerow(result)
    
    print(f"Cleaned results saved to: {output_file}")
    
    # Generate summary statistics
    total = len(cleaned_results)
    successful = sum(1 for r in cleaned_results if r['status'] == 'success')
    with_followups = sum(1 for r in cleaned_results if r['followup_questions'])
    
    print(f"\nSummary:")
    print(f"Total questions: {total}")
    print(f"Successful: {successful}")
    print(f"Failed: {total - successful}")
    print(f"With follow-up questions: {with_followups}")

if __name__ == "__main__":
    process_results()