import asyncio
import json
import csv
import traceback
from datetime import datetime
from pathlib import Path
import sys
import io
from contextlib import redirect_stdout, redirect_stderr

sys.path.append(str(Path(__file__).parent.parent))

from app.services.api_query_agent.api_agent import ApiAgent

# List of all questions to process
QUESTIONS = [
    "show me the top 5 accounts",
    "Get cash value for account number ********",
    "Search account ********",
    'Create a service request for "*********"',
    "Can you please give me a summary of my book of business?",
    "show me the total accounts",
    "Show me a list of my clients",
    "how many total accounts are there",
    "how many total client accounts are there",
    "How many of these 3,626 accounts are active vs. dormant?",
    "what is the total cash balance",
    "Show cash value of all client accounts",
    "tell the total value of the accounts",
    "are there any announcements?",
    "How many clients are there?",
    "Which asset class has the highest allocation?",
    "What is the total ACH in?",
    "can you tell the total ACH out?",
    "can you tell the total Beneficiary update in service requests?",
    "can you tell total service requests - wire out?",
    "What is the total number of open service requests?",
    "Show service requests breakdown by type",
    "Show service requests",
    "Filter service requests by status",
    "Show number of onboarding requests",
    "show asset allocation over the last 6 months",
    "What are the top 5 clients contributing to the margin balance?",
    "What stages are the 104/3567 onboarding requests in?",
    "Can you provide a status report of high-value onboarding clients?",
    "How many active accounts are there",
    "How many inactive accounts are there",
    "What is the current balance of account number ******** / ********",
    "What type of account is ******** / ******** — IRA, Joint, Individual?",
    "Has there been any recent activity in account ******** / ********?",
    "Can you show asset growth over the last 12 months?",
    "Can you show asset growth for the last quarter of 2024?",
    "Which clients have increased their portfolio value this year?",
    "Which accounts have more than $50,000 in idle cash?",
    "What is the total cash value across all accounts?",
    "Can I get a list of clients with zero market value but positive cash?",
    "show the accounts for which validation failed",
    "which is the registration type having most no. of accounts",
    "give the total margin balance for the top 5 accounts",
    "How many SIMPLE IRA accounts are there?",
    "How many accounts with Registration type as Individual are there?",
    "Can you break down accounts by registration type?",
    "How many accounts have cash balances greater than $100,000?",
    "Which accounts have had no activity in the past 6 months?",
    "Which households have the highest total account value?",
    "Give me the top 5 holdings across my book.",
    "What is the average market value per household?",
    "Can I see the transaction history for account ********?",
    "Is this registration code for a retirement or taxable account?",
    "What is the current total value of the accounts?",
    "How has the account value changed over the past 6 months?",
    "How many accounts do I manage?",
    "Which clients have the highest number of accounts?",
    "Can you show me all accounts under Joe Shmoe Household?",
    "Which clients have no active accounts currently",
    "How many accounts are associated with rep code 065KZ01?",
    "Can I get a performance summary of all accounts under the rep codes assigned to me?",
    "How many buy transactions occurred last week?",
    "What's the total value of all sell transactions this quarter?",
    "What is the total quantity of shares traded this year?",
    "Which accounts traded the largest amounts this week?",
    "Show me trades above $500,000 in value",
    "Which is the most commonly traded security across all accounts?",
    "Give me a list of the top 5 traded symbols by volume",
    "Show me all transactions for CUSIP *********",
    "How many different securities have been traded in the past 6 months?",
    "List the top 10 sell transactions by value",
    "How many simple ira accounts are there?",
    "Which is the most commonly held security?",
    "Give me top 5 holdings",
    "Show top 5 holdings by market value"
]

async def run_question(agent: ApiAgent, question: str, index: int) -> dict:
    """Run a single question and return the result."""
    print(f"\n[{index}/{len(QUESTIONS)}] Processing: {question}")
    
    # Create string buffers to capture stdout and stderr
    stdout_buffer = io.StringIO()
    stderr_buffer = io.StringIO()
    
    try:
        start_time = datetime.now()
        
        # Capture all output during execution
        with redirect_stdout(stdout_buffer), redirect_stderr(stderr_buffer):
            result = await agent.run(question)
            
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # Get captured logs
        logs = stdout_buffer.getvalue()
        if stderr_buffer.getvalue():
            logs += "\n--- STDERR ---\n" + stderr_buffer.getvalue()
        
        # Also print to console so we can see progress
        print(f"  Status: success, Duration: {duration:.2f}s")
        
        return {
            "index": index,
            "question": question,
            "response": json.dumps(result),
            "status": "success",
            "duration": duration,
            "timestamp": datetime.now().isoformat(),
            "logs": logs
        }
    except Exception as e:
        # Get captured logs even in error case
        logs = stdout_buffer.getvalue()
        if stderr_buffer.getvalue():
            logs += "\n--- STDERR ---\n" + stderr_buffer.getvalue()
        logs += f"\n--- ERROR ---\n{str(e)}\n{traceback.format_exc()}"
        
        print(f"Error processing question {index}: {e}")
        
        return {
            "index": index,
            "question": question,
            "response": str(e),
            "status": "error",
            "duration": 0,
            "timestamp": datetime.now().isoformat(),
            "logs": logs
        }
    finally:
        stdout_buffer.close()
        stderr_buffer.close()

async def main():
    """Main function to process all questions."""
    print(f"Starting batch processing of {len(QUESTIONS)} questions...")
    
    # Initialize the agent
    context_data = {}
    agent = ApiAgent(context_data=context_data)
    
    # Process all questions
    results = []
    for index, question in enumerate(QUESTIONS, 1):
        result = await run_question(agent, question, index)
        results.append(result)
        
        # Save intermediate results after each question
        save_results(results, "questions_results_intermediate.csv")
    
    # Save final results
    save_results(results, "questions_results_final.csv")
    
    # Generate summary report
    generate_summary(results)
    
    print("\nBatch processing completed!")
    print(f"Results saved to: questions_results_final.csv")
    print(f"Summary saved to: questions_results_summary.txt")

def save_results(results: list, filename: str):
    """Save results to CSV file."""
    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['index', 'question', 'response', 'status', 'duration', 'timestamp', 'logs']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        
        writer.writeheader()
        for result in results:
            writer.writerow(result)

def generate_summary(results: list):
    """Generate a summary report of the results."""
    total = len(results)
    successful = sum(1 for r in results if r['status'] == 'success')
    failed = sum(1 for r in results if r['status'] == 'error')
    avg_duration = sum(r['duration'] for r in results if r['status'] == 'success') / max(successful, 1)
    
    summary = f"""
Batch Processing Summary
========================
Total Questions: {total}
Successful: {successful}
Failed: {failed}
Success Rate: {(successful/total)*100:.2f}%
Average Duration: {avg_duration:.2f} seconds

Failed Questions:
"""
    
    for r in results:
        if r['status'] == 'error':
            summary += f"\n- Question {r['index']}: {r['question']}\n  Error: {r['response']}\n"
    
    with open('questions_results_summary.txt', 'w', encoding='utf-8') as f:
        f.write(summary)
    
    print(summary)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except Exception as e:
        print(f"An error occurred during batch processing: {e}")
        traceback.print_exc()