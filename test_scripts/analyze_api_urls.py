import json
from pathlib import Path
import sys

sys.path.append(str(Path(__file__).parent.parent))

def analyze_api_context():
    """Analyze the api_context.json to understand URL construction"""
    
    # Load api_context.json
    api_context_path = Path(__file__).parent.parent / "app/services/api_query_agent/samples/api_context.json"
    with open(api_context_path, 'r') as f:
        api_context = json.load(f)
    
    # Load services.properties
    services_path = Path(__file__).parent.parent / "config/services.properties"
    base_url = None
    with open(services_path, 'r') as f:
        for line in f:
            if line.startswith("baseenv.url:"):
                base_url = line.split(":", 1)[1].strip().strip('"')
                break
    
    print("API URL Analysis")
    print("=" * 80)
    print(f"\nBase URL from services.properties:")
    print(f"  baseenv.url: {base_url}")
    print("\nHow URLs are constructed in api_agent.py:")
    print("  url = SERVICES_PROPERTIES.BASE_URL + call_step.url")
    print("\nThis means:")
    print(f"  Full URL = {base_url} + [API endpoint from api_context.json]")
    print("\n" + "=" * 80)
    
    # Analyze all endpoints
    print("\nAll API Endpoints:")
    print("-" * 80)
    
    for page in api_context.get("pages", []):
        print(f"\nPage: {page.get('title', 'Unknown')} ({page.get('id', '')})")
        print(f"Page URL: {page.get('url', '')}")
        print(f"Description: {page.get('description', '')}")
        
        endpoints = page.get("endpoints", [])
        if endpoints:
            print(f"\nEndpoints ({len(endpoints)} total):")
            for i, endpoint in enumerate(endpoints, 1):
                print(f"\n  {i}. {endpoint.get('description', 'No description')}")
                print(f"     Method: {endpoint.get('method', 'GET').upper()}")
                print(f"     Path: {endpoint.get('url', '')}")
                print(f"     Full URL: {base_url}{endpoint.get('url', '')}")
                
                if endpoint.get('query_params'):
                    print(f"     Query Params: {json.dumps(endpoint['query_params'], indent=8)}")
                if endpoint.get('path_params'):
                    print(f"     Path Params: {json.dumps(endpoint['path_params'], indent=8)}")
    
    # Check for global endpoints
    if "global" in api_context:
        print("\n" + "=" * 80)
        print("\nGlobal Endpoints:")
        print("-" * 80)
        global_endpoints = api_context["global"].get("endpoints", [])
        for i, endpoint in enumerate(global_endpoints, 1):
            print(f"\n  {i}. {endpoint.get('description', 'No description')}")
            print(f"     Method: {endpoint.get('method', 'GET').upper()}")
            print(f"     Path: {endpoint.get('url', '')}")
            print(f"     Full URL: {base_url}{endpoint.get('url', '')}")

def main():
    analyze_api_context()

if __name__ == "__main__":
    main()