#!/usr/bin/env python3
"""
Simple test script for ApiAgent
"""

import asyncio
import sys
from pathlib import Path

# Add the app directory to the Python path
sys.path.append(str(Path(__file__).parent.parent))

from app.services.api_query_agent.api_agent import ApiAgent


async def test_api_agent():
    """Test the ApiAgent with a simple query"""
    
    # Initialize the agent
    agent = ApiAgent()
    
    # Test queries
    test_queries = [
        "How are you?",
        "What is the weather like?",
        "Get account summary for account number ********",
        "Show me the top 5 accounts"
    ]
    
    for query in test_queries:
        print(f"\n{'='*50}")
        print(f"Testing query: {query}")
        print(f"{'='*50}")
        
        try:
            result = await agent.run(query)
            print(f"Result: {result}")
        except Exception as e:
            print(f"Error: {e}")


if __name__ == "__main__":
    asyncio.run(test_api_agent()) 