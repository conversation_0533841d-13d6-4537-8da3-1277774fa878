import pytest
from app.core.rest_utils import RestUtils
from app.core.type import Header
import respx
from httpx import Response


@pytest.fixture
def rest_utils():
    return RestUtils()


@pytest.fixture
def headers() -> Header:
    return {"Content-Type": "application/json", "Authorization": "Bearer test-token"}


@pytest.mark.asyncio
class TestRestUtils:
    @respx.mock
    async def test_get(self, rest_utils, headers):
        # Mock GET request
        route = respx.get("http://test.com/api").mock(
            return_value=Response(200, json={"data": "test"})
        )

        # Execute
        result = await rest_utils.get("http://test.com/api", headers)

        # Assert
        assert result == {"data": "test"}
        assert route.called
        assert route.calls.last.request.headers["Authorization"] == "Bearer test-token"

    @pytest.mark.asyncio
    @respx.mock
    async def test_post(self, rest_utils, headers):
        # Mock POST request
        route = respx.post("http://test.com/api").mock(
            return_value=Response(200, json={"id": "123"})
        )

        # Execute
        data = {"name": "test"}
        result = await rest_utils.post("http://test.com/api", headers, data)

        # Assert
        assert result == {"id": "123"}
        assert route.called

    @respx.mock
    async def test_error_handling(self, rest_utils, headers):
        # Mock error response
        respx.get("http://test.com/api").mock(
            return_value=Response(404, json={"error": "Not found"})
        )

        # Assert error is raised
        with pytest.raises(Exception):
            await rest_utils.get("http://test.com/api", headers)
