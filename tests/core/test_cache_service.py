import asyncio
import pytest
from app.core.cache_service import CacheService, DiskCacheProvider
from typing import Any


@pytest.mark.asyncio
async def test_cache_basic_operations():
    # Setup
    CacheType = dict[str, Any]

    async def fetch_data(key: str) -> dict[str, Any]:
        await asyncio.sleep(0.1)
        return {"id": "123", "name": "Name1"}

    async def fetch_data_v2(key: str) -> dict[str, Any]:
        await asyncio.sleep(0.1)
        return {"id": "123", "name": "Name2"}

    user_cache = CacheService[CacheType](DiskCacheProvider[CacheType]("tmp"))

    # Test 1: Basic caching
    data = await user_cache.get("user_123", fetch_data, force_refresh=True)
    assert data == {"id": "123", "name": "Name1"}
    # Get from cache
    data = await user_cache.get("user_123", fetch_data_v2)
    assert data == {"id": "123", "name": "Name1"}

    # Test 2: Force refresh
    data = await user_cache.get("user_123", fetch_data_v2, force_refresh=True)
    assert data == {"id": "123", "name": "Name2"}

    # Test 3: Cache invalidation
    await user_cache.invalidate("user_123")
    data = await user_cache.get("user_123", fetch_data)
    assert data == {"id": "123", "name": "Name1"}

    # Test 4: TTL
    data = await user_cache.get("user_123", fetch_data, force_refresh=True, ttl=1)
    assert data == {"id": "123", "name": "Name1"}
    await asyncio.sleep(2)
    data = await user_cache.get("user_123", fetch_data_v2)
    assert data == {"id": "123", "name": "Name2"}

    # Test 5: Clear cache
    data = await user_cache.get("user_123", fetch_data, force_refresh=True)
    assert data == {"id": "123", "name": "Name1"}
    await user_cache.clear()
    data = await user_cache.get("user_123", fetch_data_v2)
    assert data == {"id": "123", "name": "Name2"}

    # Cleanup
    await user_cache.clear()
