import pytest
import os
import json
from unittest.mock import patch, AsyncMock, MagicMock
from fastapi import status
from httpx import AsyncClient, Response

from app.common.type import ModelName
from app.services.data_extraction.data_extractor import DataExtractor
BASEPATH = os.path.dirname(os.path.abspath(__file__))


class TestDataExtractionEndpoint:
    """Test cases for the data extraction endpoint."""

    @pytest.mark.asyncio
    async def test_data_extraction_missing_headers(self, async_client):
        """Test that the endpoint returns 400 when headers are missing."""
        payload = {
            "content": "Invoice #12345 from Amazon for $120.50 dated Jan 15, 2023",
            "boEntityId": "invoice-entity-id",
            "fieldsList": ["invoiceNumber", "amount", "date", "vendor"],
        }

        response = await async_client.post("/data-extraction", json=payload)

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert "Tenant ID and App ID are required in headers" in response.text

    @pytest.mark.asyncio
    async def test_data_extraction_with_text_content(self, async_client, mock_headers):
        """Test data extraction with text content."""

        # Load the BO entity data
        file_path = os.path.join(BASEPATH, "../data/bo_data.json")
        with open(file_path, "r") as f:
            bo_data = json.load(f)

        # Create a mock for AsyncClient.get
        async def mock_get(*args, **kwargs):
            url = args[0]
            bo_entity_id = url.split("/")[-1]
            mock_response = MagicMock(spec=Response)
            mock_response.status_code = 200
            mock_response.raise_for_status = MagicMock()
            mock_response.json.return_value = bo_data[bo_entity_id]
            return mock_response


        with patch("httpx.AsyncClient.get", AsyncMock(side_effect=mock_get)):
            payload = {
                "content": "Invoice #12345 from Amazon for $120.50 dated Jan 15, 2023",
                "boEntityId": "cv2mrdg63oek0bfq4ns0", 
                "fieldsList": [],
            }

            response = await async_client.post(
                "/data-extraction", json=payload, headers=mock_headers
            )

            assert response.status_code == status.HTTP_200_OK
            response_json = response.json()
            
            # Print the response for debugging
            print(f"Response JSON: {response_json}")

            assert response_json["invoiceNumber"] == "12345"
            assert response_json["totalAmount"] == "$120.50"

    @pytest.mark.asyncio
    async def test_data_extraction_with_file_path(self, async_client, mock_headers):
        """Test data extraction with a file path."""
        # Create the file path to return
        pdf_path = os.path.join(BASEPATH, "../data/Amazon1.pdf")

        # Mock the JiffyDrive.download_file_from_jiffydrive method
        mock_download = AsyncMock(return_value=pdf_path)
        
        file_path = os.path.join(BASEPATH, "../data/bo_data.json")
        with open(file_path, "r") as f:
            bo_data = json.load(f)

        # Create a mock for AsyncClient.get
        async def mock_get(*args, **kwargs):
            url = args[0]
            bo_entity_id = url.split("/")[-1]
            mock_response = MagicMock(spec=Response)
            mock_response.status_code = 200
            mock_response.raise_for_status = MagicMock()
            mock_response.json.return_value = bo_data[bo_entity_id]
            return mock_response


        with patch(
            "core.jiffy_drive.JiffyDrive.download_file_from_jiffydrive", mock_download
        ), patch("httpx.AsyncClient.get", AsyncMock(side_effect=mock_get)):

            payload = {
                "content": "private/documents/Amazon1.pdf",
                "boEntityId": "cv2mrdg63oek0bfq4ns0",
                "fieldsList": [],
            }

            response = await async_client.post(
                "/data-extraction", json=payload, headers=mock_headers
            )

            assert response.status_code == status.HTTP_200_OK
            response_json = response.json()

            assert response_json["invoiceNumber"] == "IN-286"

            # Verify the download method was called with the correct path
            mock_download.assert_called_once_with("private/documents/Amazon1.pdf")
