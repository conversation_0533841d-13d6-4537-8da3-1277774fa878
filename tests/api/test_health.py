import pytest
from fastapi import status


class TestHealthEndpoint:
    """Test cases for the health check endpoint."""

    def test_health_check(self, client):
        """Test that the health check endpoint returns a 200 status code."""
        response = client.get("/health")
        
        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {"status": "healthy"}
        
    @pytest.mark.asyncio
    async def test_health_check_async(self, async_client):
        """Test the health check endpoint using an async client."""
        response = await async_client.get("/health")
        
        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {"status": "healthy"} 