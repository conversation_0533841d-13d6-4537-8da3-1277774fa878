"""
Unit tests for the icon generation service
"""

import pytest
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)


def test_icon_generation_endpoint():
    """Test the icon generation endpoint with a sample tool."""
    # Sample tool data
    request_data = {
        "tool": {
            "name": "testTool",
            "schema": {
                "uuid": "test-uuid-123",
                "description": "A test tool for unit testing",
                "input": {
                    "input": {"type": "string"}
                },
                "output": {
                    "result": {"type": "string"}
                }
            }
        },
        "icon_size": 128,
        "style": "flat"
    }
    
    response = client.post("/icon-generation/generate", json=request_data)
    
    assert response.status_code == 200
    
    data = response.json()
    assert data["tool_name"] == "testTool"
    assert data["tool_uuid"] == "test-uuid-123"
    assert data["status"] in ["success", "error"]
    
    if data["status"] == "success":
        assert "generated_description" in data
        assert "icon_data" in data
        assert "icon_metadata" in data
        assert len(data["icon_data"]) > 0  # Should have base64 data



def test_simple_schema_format():
    """Test with a simpler schema format."""
    request_data = {
        "tool": {
            "name": "calculator",
            "schema": {
                "input": {
                    "num1": "number",
                    "num2": "number",
                    "operation": "string"
                },
                "output": {
                    "result": "number"
                },
                "description": "Basic arithmetic calculator"
            }
        }
    }
    
    response = client.post("/icon-generation/generate", json=request_data)
    
    assert response.status_code == 200
    
    data = response.json()
    assert data["tool_name"] == "calculator"
    assert data["status"] in ["success", "error"] 