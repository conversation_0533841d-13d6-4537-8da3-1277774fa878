import pytest
import os
import json
from unittest.mock import patch, AsyncMock, MagicMock
from fastapi import status

from app.common.type import ModelName

BASEPATH = os.path.dirname(os.path.abspath(__file__))


class TestGenericLLMEndpoint:
    """Test cases for the generic LLM endpoint."""

    @pytest.mark.asyncio
    async def test_generic_llm_missing_headers(self, async_client):
        """Test that the endpoint returns 400 when headers are missing."""
        payload = {
            "instruction": "Summarize the following text:",
            "content": "The Internet of Things (IoT) is transforming how we live and work. IoT refers to the network of physical objects embedded with sensors, software, and connectivity that enables these objects to connect and exchange data."
        }

        response = await async_client.post("/gen-ai", json=payload)

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert "Tenant ID and App ID are required in headers" in response.text

    @pytest.mark.asyncio
    async def test_generic_llm_with_file_path(self, async_client, mock_headers):
        """Test generic LLM with a file path."""
        # Create the file path to return
        pdf_path = os.path.join(BASEPATH, "../data/Amazon1.pdf")
        
        # Mock the JiffyDrive.download_file_from_jiffydrive method
        mock_download = AsyncMock(return_value=pdf_path)
        
        with patch(
            "core.jiffy_drive.JiffyDrive.download_file_from_jiffydrive", mock_download
        ):
            payload = {
                "instruction": "Extract the invoice details from this document:",
                "fileList": ["private/documents/Amazon1.pdf"]
            }

            response = await async_client.post(
                "/gen-ai", json=payload, headers=mock_headers
            )

            assert response.status_code == status.HTTP_200_OK
            # Check that file content was processed
            response_text = response.json()
            assert isinstance(response_text, dict)
            assert "invoice" in response_text["output"].lower() or "amazon" in response_text["output"].lower()
            
            # Verify the download method was called with the correct path
            mock_download.assert_called_once_with("private/documents/Amazon1.pdf")

    @pytest.mark.asyncio
    async def test_generic_llm_with_custom_prompt(self, async_client, mock_headers):
        """Test generic LLM with a custom system prompt."""
        custom_prompt = """You are a specialized AI assistant that answers questions about technology.
        Your answers should be very concise and focus on facts only."""
        
        payload = {
            "instruction": "Please provide information about cloud computing technology",
            "systemInstruction": custom_prompt,
            "modelName": ModelName.GEMINI_20_FLASH
        }

        response = await async_client.post(
            "/gen-ai", json=payload, headers=mock_headers
        )

        assert response.status_code == status.HTTP_200_OK
        response_text = response.json()
        
        # Check that response is concise (influenced by our custom prompt)
        assert isinstance(response_text, dict)
        assert "cloud computing" in response_text["output"].lower()
        
    @pytest.mark.asyncio
    async def test_generic_llm_with_simple_json_schema(self, async_client, mock_headers):
        """Test generic LLM with a simple JSON schema for structured output."""
        schema = {
            "type": "object",
            "properties": {
                "sentiment": {
                    "type": "string",
                    "enum": ["positive", "negative", "neutral"]
                },
                "score": {
                    "type": "number",
                    "minimum": 0,
                    "maximum": 1
                }
            },
            "required": ["sentiment", "score"]
        }
        
        payload = {
            "instruction": "Analyze the sentiment of the following text:"
                           "\nThis product is absolutely amazing! I love it so much.",
            "outputSchema": schema,
            "modelName": ModelName.GEMINI_20_FLASH
        }

        response = await async_client.post(
            "/gen-ai", json=payload, headers=mock_headers
        )

        assert response.status_code == status.HTTP_200_OK
        response_json = response.json()
        print("#### - ", response_json)
        # Verify structure of response
        assert isinstance(response_json, dict)
        assert "sentiment" in response_json["output"]
        assert response_json["output"]["sentiment"] in ["positive", "negative", "neutral"]
        assert "score" in response_json["output"]
        assert 0 <= response_json["output"]["score"] <= 1
        
    @pytest.mark.asyncio
    async def test_generic_llm_with_complex_json_schema(self, async_client, mock_headers):
        """Test generic LLM with a more complex JSON schema for structured output."""
        schema = {
            "type": "object",
            "properties": {
                "entities": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "name": {"type": "string"},
                            "type": {"type": "string"},
                            "confidence": {"type": "number"}
                        },
                        "required": ["name", "type"]
                    }
                },
                "summary": {"type": "string"},
                "keyTopics": {
                    "type": "array",
                    "items": {"type": "string"}
                }
            },
            "required": ["entities", "summary", "keyTopics"]
        }
        
        payload = {
            "systemInstruction": "Extract entities and key topics from the following text:",
            "instruction": "Microsoft CEO Satya Nadella announced the company's new AI strategy at the annual conference in Seattle. The initiative focuses on integrating AI into Microsoft Office products.",
            "outputSchema": schema,
            "modelName": ModelName.GEMINI_20_FLASH
        }

        response = await async_client.post(
            "/gen-ai", json=payload, headers=mock_headers
        )

        assert response.status_code == status.HTTP_200_OK
        response_json = response.json()

        # Verify structure of response
        assert isinstance(response_json, dict)
        assert "entities" in response_json["output"]
        assert isinstance(response_json["output"]["entities"], list)
        
        # Check entities
        if response_json["output"]["entities"]:
            entity = response_json["output"]["entities"][0]
            assert "name" in entity
            assert "type" in entity
            
        assert "summary" in response_json["output"]
        assert isinstance(response_json["output"]["summary"], str)
        
        assert "keyTopics" in response_json["output"]
        assert isinstance(response_json["output"]["keyTopics"], list)

    @pytest.mark.asyncio
    async def test_generic_llm_with_invalid_schema(self, async_client, mock_headers):
        """Test generic LLM with an invalid JSON schema."""
        # Invalid schema missing required "type" field
        schema = {
            "properties": {
                "result": {"type": "string"}
            }
        }
        
        payload = {
            "instruction": "Analyze this text: Sample content for analysis",
            "outputSchema": schema,
            "modelName": ModelName.GEMINI_20_FLASH
        }

        response = await async_client.post(
            "/gen-ai", json=payload, headers=mock_headers
        )
        
        # Expecting validation error for invalid schema
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        
    @pytest.mark.asyncio
    async def test_generic_llm_structured_output_with_file(self, async_client, mock_headers):
        """Test generic LLM with structured output from a file input."""
        # Create the file path to return
        pdf_path = os.path.join(BASEPATH, "../data/Amazon1.pdf")
        
        # Define schema for invoice extraction
        schema = {
            "type": "object",
            "properties": {
                "invoiceNumber": {"type": "string"},
                "date": {"type": "string"},
                "totalAmount": {"type": "number"},
                "items": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "description": {"type": "string"},
                            "quantity": {"type": "integer"},
                            "price": {"type": "number"}
                        }
                    }
                }
            },
            "required": ["invoiceNumber", "date", "totalAmount"]
        }
        
        # Mock the JiffyDrive.download_file_from_jiffydrive method
        mock_download = AsyncMock(return_value=pdf_path)
        
        with patch(
            "core.jiffy_drive.JiffyDrive.download_file_from_jiffydrive", mock_download
        ):
            payload = {
                "instruction": "Extract the invoice details from this document in a structured format:",
                "fileList": ["private/documents/Amazon1.pdf"],
                "outputSchema": schema
            }

            response = await async_client.post(
                "/gen-ai", json=payload, headers=mock_headers
            )

            assert response.status_code == status.HTTP_200_OK
            
            # Check that file content was processed into structured format
            response_json = response.json()
            assert isinstance(response_json["output"], dict)
            assert "invoiceNumber" in response_json["output"]
            assert "date" in response_json["output"]
            assert "totalAmount" in response_json["output"]
            
            # Verify the download method was called with the correct path
            mock_download.assert_called_once_with("private/documents/Amazon1.pdf") 