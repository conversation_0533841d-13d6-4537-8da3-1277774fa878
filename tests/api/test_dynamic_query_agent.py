import sys
from pathlib import Path


import pytest
import json
from app.services.dynamic_query.dynamic_query_agent import DynamicQueryAgent
import asyncio
import time

# Constants
FEW_SHOT_DATASET_PATH = (
    Path(__file__).parent.parent.parent
    / "app"
    / "services"
    / "dynamic_query"
    / "resources"
    / "dynamic_query_dataset.json"
)
SAMPLE_BO_SCHEMA_PATH = (
    Path(__file__).parent.parent / "data" / "dynamic_query" / "test_bo_schema.json"
)
DYNAMIC_QUERY_TEST_DATASET_PATH = (
    Path(__file__).parent.parent / "data" / "dynamic_query" / "test_data_set.json"
)


@pytest.fixture
def few_shot_examples():
    with open(FEW_SHOT_DATASET_PATH, "r") as f:
        return json.load(f)


@pytest.fixture
def bo_schema():
    with open(SAMPLE_BO_SCHEMA_PATH, "r") as f:
        return json.load(f)


@pytest.fixture
def root_bo():
    return "account"


@pytest.fixture
def test_data_set():
    with open(DYNAMIC_QUERY_TEST_DATASET_PATH, "r") as f:
        return json.load(f)


@pytest.fixture
def dynamic_query_agent(bo_schema, few_shot_examples):
    agent = DynamicQueryAgent()
    # Override the get_bo_schemas method to return our test schema
    agent.get_bo_schemas = lambda: json.dumps(bo_schema, indent=2)
    # Override the load_example_dataset method to return our test dataset
    agent.load_example_dataset = lambda: json.dumps(few_shot_examples, indent=2)
    return agent


def assert_query_matches_expected(question, actual_query, expected_query):
    """Helper function to compare actual and expected queries"""
    matched = actual_query in expected_query

    if matched:
        print(
            f"\n"
            f"Matched: {matched} \n"
            f"Question: {question} \n"
            f"Expected: {json.dumps(expected_query, indent=2)} \n"
            f"Got: {json.dumps(actual_query, indent=2)}"
            f"\n----------------------------------------------------------"
        )
    else:
        assert False, (
            f"\n"
            f"Matched: {matched} \n"
            f"Question: {question} \n"
            f"Expected: {json.dumps(expected_query, indent=2)} \n"
            f"Got: {json.dumps(actual_query, indent=2)}"
            f"\n----------------------------------------------------------"
        )


@pytest.mark.asyncio
async def test_get_dynamic_query(dynamic_query_agent, test_data_set, root_bo):
    start_time = time.time()
    tasks = [
        dynamic_query_agent.run(
            question=test_case["question"],
            root_bos=test_case.get("context", {}).get("root_bos", ""),
        )
        for test_case in test_data_set
    ]

    results = await asyncio.gather(*tasks)
    print(f"\nRunning {len(test_data_set)} test cases...")

    failures = []
    success_count = 0
    for query_results, expected_data in zip(results, test_data_set):
        try:
            query = query_results["query"]
            assert_query_matches_expected(
                question=expected_data["question"],
                actual_query=query,
                expected_query=[
                    expected_data["answer"],
                    *expected_data.get("other_answers", []),
                ],
            )
            success_count += 1
        except AssertionError as e:
            failures.append(str(e))

    end_time = time.time()
    success_rate = success_count / len(test_data_set) * 100
    print("\nTest Statistics:")
    print(f"Total tests: {len(test_data_set)}")
    print(f"Passed: {success_count}")
    print(f"Failed: {len(failures)}")
    print(f"Time taken: {end_time - start_time:.2f} seconds")
    print(f"Success rate: {success_rate:.2f}%")

    if failures:
        print("\nTest Failures:")
        for failure in failures:
            print(failure)

        if success_rate < 60:
            assert False


if __name__ == "__main__":
    pytest.main([__file__])
