import pytest
import os
from unittest.mock import patch, AsyncMock
from fastapi import status

from app.common.type import ModelName

BASEPATH = os.path.dirname(os.path.abspath(__file__))


class TestSummarizationEndpoint:
    """Test cases for the summarization endpoint."""

    @pytest.mark.asyncio
    async def test_summarization_missing_headers(self, async_client):
        """Test that the endpoint returns 400 when headers are missing."""
        payload = {
            "content": "Sample text to summarize",
            "maxLength": 200
        }

        response = await async_client.post("/summarization", json=payload)

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert "Tenant ID and App ID are required in headers" in response.text

    @pytest.mark.asyncio
    async def test_summarization_with_text(self, async_client, mock_headers):
        """Test summarization with direct text input."""
        text = """
        The Internet of Things (IoT) is transforming how we live and work. IoT refers to the network of physical objects embedded with sensors, software, and connectivity that enables these objects to connect and exchange data. From smart homes to industrial automation, IoT technologies are becoming increasingly prevalent across various domains. Smart thermostats can learn your preferences and optimize energy usage. Connected vehicles can communicate with traffic infrastructure to reduce congestion. In healthcare, wearable devices monitor vital signs and transmit data to healthcare providers. Industrial IoT applications improve operational efficiency through predictive maintenance and real-time monitoring. However, IoT also raises concerns about privacy, security, and dependency on technology. As IoT continues to grow, addressing these challenges while harnessing its benefits will be crucial for sustainable implementation.
        """
        
        payload = {
            "content": text
        }

        response = await async_client.post(
            "/summarization", json=payload, headers=mock_headers
        )

        assert response.status_code == status.HTTP_200_OK
        
        # Check that summarization was processed
        response_json = response.json()
        assert isinstance(response_json, dict)
        assert "summary_text" in response_json
        assert isinstance(response_json["summary_text"], str)
        assert len(response_json["summary_text"]) <= 700
        
        # Check key points if requested
        if "bullet_points" in response_json:
            assert isinstance(response_json["bullet_points"], list)
            assert all(isinstance(point, str) for point in response_json["bullet_points"])

    @pytest.mark.asyncio
    async def test_summarization_with_file(self, async_client, mock_headers):
        """Test summarization with a file input."""
        # Create the file path to return
        pdf_path = os.path.join(BASEPATH, "../data/Amazon1.pdf")
        
        # Mock the JiffyDrive.download_file_from_jiffydrive method
        mock_download = AsyncMock(return_value=pdf_path)
        
        with patch(
            "core.jiffy_drive.JiffyDrive.download_file_from_jiffydrive", mock_download
        ):
            payload = {
                "content": "private/documents/Amazon1.pdf"
            }

            response = await async_client.post(
                "/summarization", json=payload, headers=mock_headers
            )

            assert response.status_code == status.HTTP_200_OK

            # Check that summarization was processed
            response_json = response.json()
            assert isinstance(response_json, dict)
            assert "summary_text" in response_json
            assert isinstance(response_json["summary_text"], str)
            assert len(response_json["summary_text"]) <= 900

            # Check key points if requested
            if "bullet_points" in response_json:
                assert isinstance(response_json["bullet_points"], list)
                assert all(isinstance(point, str) for point in response_json["bullet_points"])
            
            # Verify the download method was called with the correct path
            mock_download.assert_called_once_with("private/documents/Amazon1.pdf") 