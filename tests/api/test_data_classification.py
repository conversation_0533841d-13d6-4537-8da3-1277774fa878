import pytest
import os
from unittest.mock import patch, AsyncMock
from fastapi import status

from app.common.type import ModelName

BASEPATH = os.path.dirname(os.path.abspath(__file__))


class TestDataClassificationEndpoint:
    """Test cases for the data classification endpoint."""

    @pytest.mark.asyncio
    async def test_data_classification_missing_headers(self, async_client):
        """Test that the endpoint returns 400 when headers are missing."""
        payload = {
            "content": "Annual revenue growth of 15% reported in Q4 2023",
            "labels": ["Financial", "Marketing", "Operations", "HR"],
        }

        response = await async_client.post("/data-classification", json=payload)

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert "Tenant ID and App ID are required in headers" in response.text

    @pytest.mark.asyncio
    async def test_data_classification_with_text_content(
        self, async_client, mock_headers
    ):
        """Test data classification with text content."""

        payload = {
            "content": "Annual revenue growth of 15% reported in Q4 2023",
            "labels": ["Financial", "Marketing", "Operations", "HR"],
        }

        response = await async_client.post(
            "/data-classification", json=payload, headers=mock_headers
        )

        assert response.status_code == status.HTTP_200_OK

        # If the response is a string representing JSON, parse it
        response = response.json()
        assert response == "Financial"

    @pytest.mark.asyncio
    async def test_data_classification_with_file_path(self, async_client, mock_headers):
        """Test data classification with a file path."""
        # Create the file path to return
        pdf_path = os.path.join(BASEPATH, "../data/Amazon1.pdf")
        
        # Mock the JiffyDrive.download_file_from_jiffydrive method with AsyncMock since it's an async method
        mock_download = AsyncMock(return_value=pdf_path)
        
        # Use patch to replace the method with our mock
        with patch("core.jiffy_drive.JiffyDrive.download_file_from_jiffydrive", mock_download):
            payload = {
                "content": "private/documents/Amazon1.pdf",
                "labels": ["Invoice", "Passport", "Receipt"],
            }

            response = await async_client.post(
                "/data-classification", json=payload, headers=mock_headers
            )

            assert response.status_code == status.HTTP_200_OK
            assert response.json() == "Invoice"

            # Verify the download method was called with the correct path
            mock_download.assert_called_once_with("private/documents/Amazon1.pdf")


    @pytest.mark.asyncio
    async def test_data_classification_with_custom_inputs(
        self, async_client, mock_headers
    ):
        """Test data classification with a custom output schema."""

        custom_schema = {
            "type": "object",
            "properties": {
                "prediction": {"type": "string"},
                "confidence": {"type": "number"},
            },
        }

        payload = {
            "content": "Employee benefits program updated for 2024",
            "labels": ["Financial", "Marketing", "Operations", "HR"],
            "outputSchema": custom_schema,
            "modelName": ModelName.GEMINI_15_PRO,
            "prompt": "Classify the content into the given labels",
        }

        response = await async_client.post(
            "/data-classification", json=payload, headers=mock_headers
        )

        assert response.status_code == status.HTTP_200_OK

        # Check only for the prediction field which is definitely present
        response_json = response.json()
        assert isinstance(response_json, dict)
        assert "prediction" in response_json
        assert response_json["prediction"] == "HR"
