import pytest
import os
from unittest.mock import patch, AsyncMock
from fastapi import status

from app.common.type import ModelName

BASEPATH = os.path.dirname(os.path.abspath(__file__))


class TestAudioTranscriptionEndpoint:
    """Test cases for the audio transcription endpoint."""

    @pytest.mark.asyncio
    async def test_audio_transcription_missing_headers(self, async_client):
        """Test that the endpoint returns 400 when headers are missing."""
        payload = {
            "fileList": ["private/audio/sample1.mp3"]
        }

        response = await async_client.post("/transcription", json=payload)
        print(response.content)
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert "Tenant ID and App ID are required in headers" in response.text

    @pytest.mark.asyncio
    async def test_audio_transcription_with_file(self, async_client, mock_headers):
        """Test audio transcription with a file input."""
        # Create the file path to return
        audio_path = os.path.join(BASEPATH, "../data/sample1.mp3")
        
        # Mock the JiffyDrive.download_file_from_jiffydrive method
        mock_download = AsyncMock(return_value=audio_path)
        
        with patch(
            "core.jiffy_drive.JiffyDrive.download_file_from_jiffydrive", mock_download
        ):
            payload = {
                "fileList": ["private/audio/sample1.mp3"],
                "language": "en"
            }

            response = await async_client.post(
                "/transcription", json=payload, headers=mock_headers
            )

            print(response.content)
            assert response.status_code == status.HTTP_200_OK
            
            # Check that transcription was processed
            response_json = response.json()
            assert isinstance(response_json, dict)
            assert "text" in response_json
            assert isinstance(response_json["text"], str)
            
            # Verify the download method was called with the correct path
            mock_download.assert_called_once_with("private/audio/sample1.mp3")

    @pytest.mark.asyncio
    async def test_audio_transcription_with_structured_output(self, async_client, mock_headers):
        """Test audio transcription with structured output schema."""
        schema = {
            "type": "object",
            "properties": {
                "segments": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "text": {"type": "string"},
                            "speaker": {"type": "string"}
                        },
                        "required": ["text", "speaker"]
                    }
                }
            },
            "required": ["segments"]
        }
        
        # Create the file path to return
        audio_path = os.path.join(BASEPATH, "../data/sample1.mp3")
        
        # Mock the JiffyDrive.download_file_from_jiffydrive method
        mock_download = AsyncMock(return_value=audio_path)
        
        with patch(
            "core.jiffy_drive.JiffyDrive.download_file_from_jiffydrive", mock_download
        ):
            payload = {
                "fileList": ["private/audio/sample1.mp3"],
                "language": "en",
                "outputSchema": schema
            }

            response = await async_client.post(
                "/transcription", json=payload, headers=mock_headers
            )
            print("#### - ", response.content)

            assert response.status_code == status.HTTP_200_OK
            
            # Check that transcription was processed with structured output
            response_json = response.json()
            assert isinstance(response_json, dict)
            assert "structured_output" in response_json
            assert isinstance(response_json["structured_output"], list)
            
            # Check structure of first segment if available
            if response_json["structured_output"]:
                segment = response_json["structured_output"][0]
                assert "text" in segment
                assert "speaker" in segment
