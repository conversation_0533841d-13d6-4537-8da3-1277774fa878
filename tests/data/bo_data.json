{"cv2mrdg63oek0bfq4ns0": {"structured": {"name": "invoiceNew", "structured": {"fields": [{"type": "Attribute", "targetId": "cv2mrdg63oek0bfq4nr0", "isComposition": true, "name": "id"}, {"type": "Attribute", "targetId": "cv2mreo63oek0bfq4p10", "isComposition": true, "name": "invoiceNumber"}, {"type": "Attribute", "targetId": "cv2mrg063oek0bfq4p20", "isComposition": true, "name": "invoiceDate"}, {"type": "Attribute", "targetId": "cv2mrho63oek0bfq4p30", "isComposition": true, "name": "poNumber"}, {"type": "Attribute", "targetId": "cv2mrj863oek0bfq4p40", "isComposition": true, "name": "billing<PERSON><PERSON>ress"}, {"type": "Attribute", "targetId": "cv2mrkg63oek0bfq4p50", "isComposition": true, "name": "shippingAddress"}, {"type": "Attribute", "targetId": "cv2mrlo63oek0bfq4p60", "isComposition": true, "name": "totalAmount"}, {"type": "Attribute", "targetId": "cv2ms2o63oek0bfq4pd0", "isComposition": true, "name": "lineItems"}]}, "uuid": "cv2mrdg63oek0bfq4ns0", "label": "Invoice New", "uniqueIdentifiers": [{"constraintName": "primary_key", "uniqueFields": ["id"], "isPrimary": true}], "modelInfo": {"role": "ENTITY", "entityType": "REGULAR", "iconPath": "icon_-Tb_table", "position": {"x": 806, "y": -281}, "editorInfo": {"position": {"x": 806, "y": -281}}}, "artifactId": "cus8p93c2ka4glbs7b3g"}, "fields": [{"name": "id", "type": "Singlelinetext", "constraints": [{"predefined": {"constraintType": "MIN_LENGTH", "argument": {"literal": {"value": "0", "type": "int"}}}}, {"predefined": {"constraintType": "MAX_LENGTH", "argument": {"literal": {"value": "255", "type": "int"}}}}], "simple": {"baseType": "string", "sampleValues": [{"literal": {"value": "Sample data for single line text", "type": "string"}}], "allowedConstraints": ["REQUIRED", "READ_ONLY", "MIN_LENGTH", "MAX_LENGTH", "MATCHES"]}, "uuid": "cv2mrdg63oek0bfq4nr0", "label": "id", "artifactId": "cus8p93c2ka4glbs7b3g"}, {"name": "invoiceNumber", "type": "Singlelinetext", "constraints": [{"predefined": {"constraintType": "MIN_LENGTH", "argument": {"literal": {"value": "0", "type": "int"}}}}, {"predefined": {"constraintType": "MAX_LENGTH", "argument": {"literal": {"value": "255", "type": "int"}}}}], "simple": {"baseType": "string", "sampleValues": [{"literal": {"value": "Sample data for single line text", "type": "string"}}], "allowedConstraints": ["REQUIRED", "READ_ONLY", "MIN_LENGTH", "MAX_LENGTH", "MATCHES"]}, "uuid": "cv2mreo63oek0bfq4p10", "label": "Invoice Number", "artifactId": "cus8p93c2ka4glbs7b3g"}, {"name": "invoiceDate", "type": "Singlelinetext", "constraints": [{"predefined": {"constraintType": "MIN_LENGTH", "argument": {"literal": {"value": "0", "type": "int"}}}}, {"predefined": {"constraintType": "MAX_LENGTH", "argument": {"literal": {"value": "255", "type": "int"}}}}], "simple": {"baseType": "string", "sampleValues": [{"literal": {"value": "Sample data for single line text", "type": "string"}}], "allowedConstraints": ["REQUIRED", "READ_ONLY", "MIN_LENGTH", "MAX_LENGTH", "MATCHES"]}, "uuid": "cv2mrg063oek0bfq4p20", "label": "Invoice Date", "artifactId": "cus8p93c2ka4glbs7b3g"}, {"name": "poNumber", "type": "Singlelinetext", "constraints": [{"predefined": {"constraintType": "MIN_LENGTH", "argument": {"literal": {"value": "0", "type": "int"}}}}, {"predefined": {"constraintType": "MAX_LENGTH", "argument": {"literal": {"value": "255", "type": "int"}}}}], "simple": {"baseType": "string", "sampleValues": [{"literal": {"value": "Sample data for single line text", "type": "string"}}], "allowedConstraints": ["REQUIRED", "READ_ONLY", "MIN_LENGTH", "MAX_LENGTH", "MATCHES"]}, "uuid": "cv2mrho63oek0bfq4p30", "label": "PO Number", "artifactId": "cus8p93c2ka4glbs7b3g"}, {"name": "billing<PERSON><PERSON>ress", "type": "Multilinetext", "constraints": [{"predefined": {"constraintType": "MIN_LENGTH", "argument": {"literal": {"value": "0", "type": "int"}}}}, {"predefined": {"constraintType": "MAX_LENGTH", "argument": {"literal": {"value": "2000", "type": "int"}}}}], "simple": {"baseType": "string", "sampleValues": [{"literal": {"value": "Sample data for multi-line text Data extends to multiple lines", "type": "string"}}], "allowedConstraints": ["REQUIRED", "READ_ONLY", "MIN_LENGTH", "MAX_LENGTH", "MATCHES"]}, "uuid": "cv2mrj863oek0bfq4p40", "label": "Billing Address", "artifactId": "cus8p93c2ka4glbs7b3g"}, {"name": "shippingAddress", "type": "Multilinetext", "constraints": [{"predefined": {"constraintType": "MIN_LENGTH", "argument": {"literal": {"value": "0", "type": "int"}}}}, {"predefined": {"constraintType": "MAX_LENGTH", "argument": {"literal": {"value": "2000", "type": "int"}}}}], "simple": {"baseType": "string", "sampleValues": [{"literal": {"value": "Sample data for multi-line text Data extends to multiple lines", "type": "string"}}], "allowedConstraints": ["REQUIRED", "READ_ONLY", "MIN_LENGTH", "MAX_LENGTH", "MATCHES"]}, "uuid": "cv2mrkg63oek0bfq4p50", "label": "Shipping Address", "artifactId": "cus8p93c2ka4glbs7b3g"}, {"name": "totalAmount", "type": "Singlelinetext", "constraints": [{"predefined": {"constraintType": "MIN_LENGTH", "argument": {"literal": {"value": "0", "type": "int"}}}}, {"predefined": {"constraintType": "MAX_LENGTH", "argument": {"literal": {"value": "255", "type": "int"}}}}], "simple": {"baseType": "string", "sampleValues": [{"literal": {"value": "Sample data for single line text", "type": "string"}}], "allowedConstraints": ["REQUIRED", "READ_ONLY", "MIN_LENGTH", "MAX_LENGTH", "MATCHES"]}, "uuid": "cv2mrlo63oek0bfq4p60", "label": "Total Amount", "artifactId": "cus8p93c2ka4glbs7b3g"}, {"name": "lineItems", "type": "link", "association": {"attribute": {"type": "Attribute", "targetId": "cv2mrc063oek0bfq4mm0", "isComposition": true}, "multiplicity": "ONE_TO_MANY", "type": "COMPOSITION", "isOrdered": true, "isSource": true, "service": {"type": "Service", "targetId": "cus8p9bc2ka4glbs7c90"}}, "uuid": "cv2ms2o63oek0bfq4pd0", "label": "Line Items", "artifactId": "cus8p93c2ka4glbs7b3g"}]}, "cv2mrc063oek0bfq4mm0": {"structured": {"name": "lineItemsNew", "structured": {"fields": [{"type": "Attribute", "targetId": "cv2mrc063oek0bfq4ml0", "isComposition": true, "name": "id"}, {"type": "Attribute", "targetId": "cv2mrng63oek0bfq4p70", "isComposition": true, "name": "serialNumber"}, {"type": "Attribute", "targetId": "cv2mroo63oek0bfq4p80", "isComposition": true, "name": "description"}, {"type": "Attribute", "targetId": "cv2mrq863oek0bfq4p90", "isComposition": true, "name": "unitPrice"}, {"type": "Attribute", "targetId": "cv2mrrg63oek0bfq4pa0", "isComposition": true, "name": "quantity"}, {"type": "Attribute", "targetId": "cv2mrso63oek0bfq4pb0", "isComposition": true, "name": "subtotal"}, {"type": "Attribute", "targetId": "cv2mru063oek0bfq4pc0", "isComposition": true, "name": "taxAmount"}]}, "uuid": "cv2mrc063oek0bfq4mm0", "label": "Line Items-New", "uniqueIdentifiers": [{"constraintName": "primary_key", "uniqueFields": ["id"], "isPrimary": true}], "modelInfo": {"role": "ENTITY", "entityType": "REGULAR", "iconPath": "icon_-Tb_table", "position": {"x": 1216, "y": -266}, "editorInfo": {"position": {"x": 1216, "y": -266}}}, "artifactId": "cus8p93c2ka4glbs7b3g"}, "fields": [{"name": "id", "type": "Singlelinetext", "constraints": [{"predefined": {"constraintType": "MIN_LENGTH", "argument": {"literal": {"value": "0", "type": "int"}}}}, {"predefined": {"constraintType": "MAX_LENGTH", "argument": {"literal": {"value": "255", "type": "int"}}}}], "simple": {"baseType": "string", "sampleValues": [{"literal": {"value": "Sample data for single line text", "type": "string"}}], "allowedConstraints": ["REQUIRED", "READ_ONLY", "MIN_LENGTH", "MAX_LENGTH", "MATCHES"]}, "uuid": "cv2mrc063oek0bfq4ml0", "label": "id", "artifactId": "cus8p93c2ka4glbs7b3g"}, {"name": "serialNumber", "type": "Multilinetext", "constraints": [{"predefined": {"constraintType": "MIN_LENGTH", "argument": {"literal": {"value": "0", "type": "int"}}}}, {"predefined": {"constraintType": "MAX_LENGTH", "argument": {"literal": {"value": "2000", "type": "int"}}}}], "simple": {"baseType": "string", "sampleValues": [{"literal": {"value": "Sample data for multi-line text Data extends to multiple lines", "type": "string"}}], "allowedConstraints": ["REQUIRED", "READ_ONLY", "MIN_LENGTH", "MAX_LENGTH", "MATCHES"]}, "uuid": "cv2mrng63oek0bfq4p70", "label": "Serial Number", "artifactId": "cus8p93c2ka4glbs7b3g"}, {"name": "description", "type": "Multilinetext", "constraints": [{"predefined": {"constraintType": "MIN_LENGTH", "argument": {"literal": {"value": "0", "type": "int"}}}}, {"predefined": {"constraintType": "MAX_LENGTH", "argument": {"literal": {"value": "2000", "type": "int"}}}}], "simple": {"baseType": "string", "sampleValues": [{"literal": {"value": "Sample data for multi-line text Data extends to multiple lines", "type": "string"}}], "allowedConstraints": ["REQUIRED", "READ_ONLY", "MIN_LENGTH", "MAX_LENGTH", "MATCHES"]}, "uuid": "cv2mroo63oek0bfq4p80", "label": "Description", "artifactId": "cus8p93c2ka4glbs7b3g"}, {"name": "unitPrice", "type": "Multilinetext", "constraints": [{"predefined": {"constraintType": "MIN_LENGTH", "argument": {"literal": {"value": "0", "type": "int"}}}}, {"predefined": {"constraintType": "MAX_LENGTH", "argument": {"literal": {"value": "2000", "type": "int"}}}}], "simple": {"baseType": "string", "sampleValues": [{"literal": {"value": "Sample data for multi-line text Data extends to multiple lines", "type": "string"}}], "allowedConstraints": ["REQUIRED", "READ_ONLY", "MIN_LENGTH", "MAX_LENGTH", "MATCHES"]}, "uuid": "cv2mrq863oek0bfq4p90", "label": "UnitPrice", "artifactId": "cus8p93c2ka4glbs7b3g"}, {"name": "quantity", "type": "Multilinetext", "constraints": [{"predefined": {"constraintType": "MIN_LENGTH", "argument": {"literal": {"value": "0", "type": "int"}}}}, {"predefined": {"constraintType": "MAX_LENGTH", "argument": {"literal": {"value": "2000", "type": "int"}}}}], "simple": {"baseType": "string", "sampleValues": [{"literal": {"value": "Sample data for multi-line text Data extends to multiple lines", "type": "string"}}], "allowedConstraints": ["REQUIRED", "READ_ONLY", "MIN_LENGTH", "MAX_LENGTH", "MATCHES"]}, "uuid": "cv2mrrg63oek0bfq4pa0", "label": "Quantity", "artifactId": "cus8p93c2ka4glbs7b3g"}, {"name": "subtotal", "type": "Multilinetext", "constraints": [{"predefined": {"constraintType": "MIN_LENGTH", "argument": {"literal": {"value": "0", "type": "int"}}}}, {"predefined": {"constraintType": "MAX_LENGTH", "argument": {"literal": {"value": "2000", "type": "int"}}}}], "simple": {"baseType": "string", "sampleValues": [{"literal": {"value": "Sample data for multi-line text Data extends to multiple lines", "type": "string"}}], "allowedConstraints": ["REQUIRED", "READ_ONLY", "MIN_LENGTH", "MAX_LENGTH", "MATCHES"]}, "uuid": "cv2mrso63oek0bfq4pb0", "label": "Subtotal", "artifactId": "cus8p93c2ka4glbs7b3g"}, {"name": "taxAmount", "type": "Multilinetext", "constraints": [{"predefined": {"constraintType": "MIN_LENGTH", "argument": {"literal": {"value": "0", "type": "int"}}}}, {"predefined": {"constraintType": "MAX_LENGTH", "argument": {"literal": {"value": "2000", "type": "int"}}}}], "simple": {"baseType": "string", "sampleValues": [{"literal": {"value": "Sample data for multi-line text Data extends to multiple lines", "type": "string"}}], "allowedConstraints": ["REQUIRED", "READ_ONLY", "MIN_LENGTH", "MAX_LENGTH", "MATCHES"]}, "uuid": "cv2mru063oek0bfq4pc0", "label": "Tax Amount", "artifactId": "cus8p93c2ka4glbs7b3g"}]}}