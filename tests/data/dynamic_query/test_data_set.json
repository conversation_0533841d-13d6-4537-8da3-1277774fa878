[{"question": "Get account details including id and account number from Account", "context": {"root_bos": "account"}, "answer": {"Account": {"select": {"id": true, "accountNumber": true}}}}, {"question": "Get account details including id and account number, ordered by the primary owner's last name in descending order", "context": {"root_bos": "account"}, "answer": {"Account": {"select": {"id": true, "accountNumber": true}, "orderBy": "desc(primaryOwner.owner.lastName)"}}}, {"question": "Get account details including id, account number, and primary owner's first and last name, filter by account number 1234 and balance greater than or equal to 10000, order by balance descending and account number, skip first result and limit to 10 records", "context": {"root_bos": "account"}, "answer": {"Account": {"select": {"id": true, "accountNumber": true, "primaryOwner": {"select": {"owner": {"select": {"firstName": true, "lastName": true}}}}}, "filter": ["accountNumber == '1234'", "balance >= 10000"], "orderBy": ["desc(balance)", "accountNumber"], "offset": 1, "limit": 10}}}, {"question": "Get account details including id and account number, with primary owner's first and last name only if owner's first name is <PERSON>, filter accounts where account number is either 1234 or 5678 and balance is over 1000, order results by account number in descending order", "context": {"root_bos": "account"}, "answer": {"Account": {"select": {"id": true, "accountNumber": true, "primaryOwner": {"select": {"owner": {"select": {"firstName": true, "lastName": true}}}, "filter": "owner.firstName == 'Alice'"}}, "filter": "(accountNumber == '1234' || accountNumber == '5678') && balance > 1000", "orderBy": "desc(accountNumber)"}}, "other_answers": [{"Account": {"select": {"id": true, "accountNumber": true, "primaryOwner": {"select": {"owner": {"select": {"firstName": true, "lastName": true}}}, "filter": "owner.firstName == 'Alice' "}}, "filter": "(accountNumber == '1234' || accountNumber == '5678') && balance > 1000", "orderBy": "desc(accountNumber)"}}]}, {"question": "Count total accounts and find minimum balance for savings and current accounts with balance over 1000", "context": {"root_bos": "account"}, "answer": {"Account": {"aggregate": {"count": "${count(id)}", "minBalance": "${min(balance)}"}, "filter": "(type == 'Savings' || type == 'Current') && balance > 1000"}}}, {"question": "Group accounts by account type and creation date, showing count and minimum balance for accounts created since 2024 with balance over 100", "context": {"root_bos": "account"}, "answer": {"Account": {"aggregate": {"count": "${count(id)}", "minBalance": "${min(balance)}"}, "filter": "createdAt >= '2024-01-01' && balance > 100", "groupBy": ["type", "createdAt"]}}, "other_answers": [{"Account": {"aggregate": {"count": "${count(id)}", "minBalance": "${min(balance)}"}, "filter": "createdAt >= '2024-01-01T00:00:00.000Z' && balance > 100", "groupBy": ["type", "createdAt"]}}]}, {"question": "Get all fields from match summary", "context": {"root_bos": "matchSummary"}, "answer": {}}, {"question": "Get account details including id and account number, with all fields of primary owner where primary owner's first name is <PERSON> and account number is either 1234 or 5678 with balance over 1000, order results by account number in descending order", "answer": {"Account": {"select": {"id": true, "accountNumber": true, "primaryOwner": {"select": {"*": true}, "filter": "owner.firstName == 'Alice'"}}, "filter": "(accountNumber == '1234' || accountNumber == '5678') && balance > 1000", "orderBy": "desc(accountNumber)"}}, "other_answers": [{"Account": {"select": {"id": true, "accountNumber": true, "primaryOwner": {"select": {"*": true}, "filter": "owner.firstName == 'Alice' "}}, "filter": "(accountNumber == '1234' || accountNumber == '5678') && balance > 1000", "orderBy": "desc(accountNumber)"}}, {"Account": {"select": {"id": true, "accountNumber": true, "primaryOwner": {"select": {"alternateMailingAddress": true, "dueDiligenceOptions": true, "legalAddress": true, "mailingAddress": true, "owner": true, "previousLegalAddress": true, "trustedContact": true, "bulkLoadRecIdJfyApx": true, "bulkLoadRunIdJfyApx": true, "createdAt": true, "createdBy": true, "id": true, "isAddressSameAsPrimaryClient": true, "lastKYCDate": true, "lastModifiedAt": true, "lastModifiedBy": true, "legalAddressStartDate": true, "ownerType": true, "percentage": true, "relationshipToAccount": true, "relationshipToOwner": true, "sendTaxDocumentsToAlternateAddress": true, "spouseIsAJointOwner": true, "trustedContactInfoDeclined": true, "trustedContactRelationship": true}, "filter": "owner.firstName == 'Alice'"}}, "filter": "(accountNumber == '1234' || accountNumber == '5678') && balance > 1000", "orderBy": "desc(accountNumber)"}}]}, {"question": "Get detailed account statistics grouped by account type and creation date, including count, min, max, average, and total balance for accounts since 2024 with balance greater than or equal to 100", "context": {"root_bos": "account"}, "answer": {"Account": {"aggregate": {"count": "${count(id)}", "minBalance": "${min(balance)}", "maxBalance": "${max(balance)}", "avgBalance": "${avg(balance)}", "totalBalance": "${sum(balance)}"}, "filter": "createdAt >= '2024-01-01' && balance >= 100", "groupBy": ["type", "createdAt"]}}, "other_answers": [{"Account": {"aggregate": {"count": "${count(id)}", "minBalance": "${min(balance)}", "maxBalance": "${max(balance)}", "avgBalance": "${avg(balance)}", "totalBalance": "${sum(balance)}"}, "filter": "createdAt >= '2024-01-01T00:00:00.000Z' && balance >= 100", "groupBy": ["type", "createdAt"]}}]}, {"question": "Get distinct account types for accounts with status 'Active'", "context": {"root_bos": "account"}, "answer": {"Account": {"distinct": {"type": true}, "filter": "accountStatus == 'Active'"}}, "other_answers": [{"Account": {"distinct": {"type": true}, "filter": "accountStatus == 'Active' "}}]}, {"question": "Count total number of accounts with status 'Active'", "context": {"root_bos": "account"}, "answer": {"Account": {"aggregate": {"count": "${count(id)}"}, "filter": "accountStatus == 'Active'"}}}, {"question": "Get security IDs and create a new field named 'description' that combines securityDescription1 and securityDescription2 fields", "context": {"root_bos": "security"}, "answer": {"Security": {"select": {"id": true, "description": "${securityDescription1 + securityDescription2}"}}}}, {"question": "Search for securities containing 'BGMO' in their securityDescription or symbol fields", "answer": {"Security": {"textsearch": {"fields": ["securityDescription", "symbol"], "word": "BGMO"}}}}, {"question": "Get account details including id and account number, with list of beneficiaries's first and last names only if owner's first name is <PERSON>, filter accounts where account number is either 1234 or 5678 and balance is over 1000, order results by account number in descending order", "context": {"root_bos": "account"}, "answer": {"Account": {"select": {"id": true, "accountNumber": true, "beneficiaries": {"select": {"beneficiary": {"select": {"firstName": true, "lastName": true}}}, "filter": "beneficiary.firstName == 'Alice'"}}, "filter": "(accountNumber == '1234' || accountNumber == '5678') && balance > 1000", "orderBy": "desc(accountNumber)"}}, "other_answers": [{"Account": {"select": {"id": true, "accountNumber": true, "beneficiaries": {"select": {"beneficiary": {"select": {"firstName": true, "lastName": true}}}, "filter": "beneficiary.firstName == 'Alice' "}}, "filter": "(accountNumber == '1234' || accountNumber == '5678') && balance > 1000", "orderBy": "desc(accountNumber)"}}]}, {"question": "This is not a valid query question xyz123", "answer": {}}, {"question": "This is not a valid query question xyz123", "context": {"root_bos": "account"}, "answer": {}}, {"question": "", "answer": {}}, {"question": "", "context": {"root_bos": "account"}, "answer": {}}, {"question": "get government interest rate for account id 27ddc496-eec9-11ef-9068-bf957ee9cc5b", "context": {"root_bos": "account"}, "answer": {"Account": {"select": {"dailyBalances": {"select": {"governmentInterest": true}}}, "filter": "id == '27ddc496-eec9-11ef-9068-bf957ee9cc5b'"}}}]