import os
import sys
import pytest
from fastapi.testclient import <PERSON><PERSON>lient
from httpx import AsyncClient
import asyncio
from typing import AsyncGenerator, Generator

BASE_PATH = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.join(BASE_PATH, "../"))
from app.main import app


@pytest.fixture(scope="session")
def event_loop() -> Generator:
    """
    Create an instance of the default event loop for each test case.
    """
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="module")
def client() -> Generator:
    """
    Create a FastAPI TestClient that will be used for the tests.
    """
    with TestClient(app) as test_client:
        yield test_client


@pytest.fixture(scope="module")
async def async_client() -> AsyncGenerator:
    """
    Create an async HTTP client that will be used for the tests.
    """
    async with Async<PERSON>lient(app=app, base_url="http://test") as ac:
        yield ac


@pytest.fixture(scope="function")
def mock_headers():
    """
    Mock headers with tenant ID and app ID for authentication.
    """
    return {
        "X-Jiffy-Tenant-ID": "test-tenant",
        "X-Jiffy-App-ID": "test-app",
        "X-Jiffy-User-ID": "test-user"
    } 