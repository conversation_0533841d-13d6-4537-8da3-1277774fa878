# Regression Tests for Chat Agent

Steps to run the regression tests for the chat agent. 

## Prerequisites

Before running the tests, ensure you have the required `google-genai` package installed.

To install the package, run the following command in your terminal:

## Configuration

Proper configuration is crucial for the tests to run correctly. This involves setting up an environment variable and updating the service properties.



### Service Properties

The `services.properties` file contains the URLs that the agent uses to connect to various services. You need to update these URLs to point to the correct environment you specified in the environment variable.

1.  Locate the `services.properties` file within the project structure.
2.  Open the file in a text editor.
3.  Modify the URL values to match the intended environment.
4.  Set run.mode to "TEST"


### Environment Variable

You must set the `API_QUERY_AGENT_ENV` environment variable to specify the environment against which the tests should run.
This can be set in the script


## Running the Regression Tests

Once the prerequisites and configuration are in place, you can execute the regression test script.

The script is located at:

```
tests/data/api_query_agent/chat_agent_regression_tests.py
```

To run the script, navigate to the root directory of the project in your terminal and execute the following command:

```bash
python tests/data/api_query_agent/chat_agent_regression_tests.py
```

## Results

Upon completion, the test script will save the results in the `tests/data/` directory. You can find the output files in this folder to analyze the test run.