import sys
import os

sys.path.append(os.path.join(os.path.dirname(__file__), "../"))

from langchain_google_vertexai import ChatVertexAI, HarmBlockThreshold, HarmCategory

from deepeval.models.base_model import Deep<PERSON><PERSON><PERSON>aseLL<PERSON>

from app.core.config import AUTH_PROPERTIES


class GoogleVertexAI(DeepEvalBaseLLM):
    """Class to implement Vertex AI for DeepEval"""

    def __init__(self, model):
        self.model = model

    def load_model(self):
        return self.model

    def generate(self, prompt: str) -> str:
        chat_model = self.load_model()
        return chat_model.invoke(prompt).content

    async def a_generate(self, prompt: str) -> str:
        chat_model = self.load_model()
        res = await chat_model.ainvoke(prompt)
        return res.content

    def get_model_name(self):
        return "Vertex AI Model"


# Initialize safety filters for vertex model
# This is important to ensure no evaluation responses are blocked
safety_settings = {
    HarmCategory.HARM_CATEGORY_UNSPECIFIED: HarmBlockThreshold.BLOCK_NONE,
    HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_NONE,
    HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_NONE,
    HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_NONE,
    HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_NONE,
}


custom_model_gemini = ChatVertexAI(
    model_name="gemini-2.0-flash",
    safety_settings=safety_settings,
    project=AUTH_PROPERTIES.GOOGLE_CLOUD_PROJECT,
    location=AUTH_PROPERTIES.GOOGLE_CLOUD_LOCATION,
)

# initialize the  wrapper class
vertexai_gemini = GoogleVertexAI(model=custom_model_gemini)
