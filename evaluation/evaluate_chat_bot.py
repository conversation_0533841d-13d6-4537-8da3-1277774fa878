from deepeval import assert_test, login_with_confident_api_key
from deepeval.metrics import GEval
from deepeval.test_case import LLMTestCase, LLMTestCaseParams
from deepeval.dataset import EvaluationDataset
from deepeval import evaluate

login_with_confident_api_key(
    "confident_us_7Xt8mxqAeTgB6lag2zdA5shz5QOzOGKVK2t9kt3abNs="
)
import sys
import os
import asyncio

sys.path.append(os.path.join(os.path.dirname(__file__), "../"))

from evaluation.vertex_ai import vertexai_gemini
from app.services.new_query_agent.api_agent import ApiAgent

dataset = EvaluationDataset()
dataset.pull(alias="Chat questions")


def execute_query(query):
    try:
        context_data = {}
        agent = ApiAgent(
            context_data=context_data,
        )
        res = asyncio.run(agent.run(query))
        return str(res)

    except Exception as e:
        return "An error occurred during the main test execution: {e}"


# Convert goldens to test cases
for golden in dataset.goldens[:2]:
    input = golden.input
    # Replace your_llm_app() with your actual LLM app
    test_case = LLMTestCase(
        input=golden.input,
        actual_output=execute_query(golden.input),
        expected_output=golden.expected_output,
    )
    dataset.add_test_case(test_case)


correctness_metric = GEval(
    name="Correctness",
    criteria="Determine if the 'actual output' is correct based on the 'expected output'.",
    evaluation_params=[
        LLMTestCaseParams.ACTUAL_OUTPUT,
        LLMTestCaseParams.EXPECTED_OUTPUT,
    ],
    threshold=0.5,
    model=vertexai_gemini,
)

evaluate(
    test_cases=dataset.test_cases,
    metrics=[correctness_metric],  # Replace with your metrics
)
