# Run mode configuration - set to "TEST" to use SERVICES_PROPERTIES.BASE_URL, otherwise uses base_url from api_context.json
run.mode: "TEST"
# jiffydrive.url: "https://platform-editor.cluster.jiffy.ai"
# ml.space.url: "https://platform-editor.cluster.jiffy.ai"
# workhorse.url:"https://platform-editor.cluster.jiffy.ai"
# baseenv.url: "https://platform-editor.cluster.jiffy.ai"
# # ********************* I am properties ******************** #
# iam.url: "https://platform-editor.cluster.jiffy.ai/apexiam/v1/auth/token"
# iam.base.url: "https://platform-editor.cluster.jiffy.ai/apexiam/v1"
# iam.grantType: "client_credentials"
# iam.tenantName: "jiffy"
# iam.scope: "openid email profile"
# # ********************* Model Repository ******************** #
# nats.url: "https://platform-editor.cluster.jiffy.ai/model-repository/nats"
# model-repository.url: "https://platform-editor.cluster.jiffy.ai/platform"
# nats.host: "localhost"
# nats.port: "3100"
# app.id: "DocProc"
# nats.subject: "dp-renderer-nats-subject"
# componentlibrary.url: "https://platform-editor.cluster.jiffy.ai"
# # ********************* Internal Urls ******************** #
# jiffydrive.internal.url: "http://jiffydrive:8080/drive/v1/objects/"
# autoclassify.internal.url: "http://auto-classification:8657/auto-classification/predict/"
# workflow.internal.url: "http://workhorse:8080/workflow/v1"
# messenger.internal.url: "http://workhorse-messenger:5000/messenger/v1"
# document-service.internal.url: "http://document-service:8657/docproc/api/v1"
# model-repository.internal.url: "https://platform-editor.cluster.jiffy.ai/model-repo/rest/v1"
# pam.url: "https://platform-editor.cluster.jiffy.ai"
# app-data-manager.url: "https://platform-editor.cluster.jiffy.ai"

#--------------------- AXOS UAT -----------------------------#

jiffydrive.url: "https://uat.jiffy.ai"
ml.space.url: "https://uat.jiffy.ai"
workhorse.url:"https://uat.jiffy.ai"
baseenv.url: "https://uat.jiffy.ai"
# ********************* I am properties ******************** #
iam.url: "https://uat.jiffy.ai/apexiam/v1/auth/token"
iam.base.url: "https://uat.jiffy.ai/apexiam/v1"
iam.grantType: "client_credentials"
iam.tenantName: "jiffy"
iam.scope: "openid email profile"
# ********************* Model Repository ******************** #
nats.url: "https://uat.jiffy.ai/model-repository/nats"
model-repository.url: "https://uat.jiffy.ai/platform"
nats.host: "localhost"
nats.port: "3100"
app.id: "DocProc"
nats.subject: "dp-renderer-nats-subject"
componentlibrary.url: "https://uat.jiffy.ai"
# ********************* Internal Urls ******************** #
jiffydrive.internal.url: "http://jiffydrive:8080/drive/v1/objects/"
autoclassify.internal.url: "http://auto-classification:8657/auto-classification/predict/"
workflow.internal.url: "http://workhorse:8080/workflow/v1"
messenger.internal.url: "http://workhorse-messenger:5000/messenger/v1"
document-service.internal.url: "http://document-service:8657/docproc/api/v1"
model-repository.internal.url: "https://uat.jiffy.ai/model-repo/rest/v1"
pam.url: "https://uat.jiffy.ai"
app-data-manager.url: "https://uat.jiffy.ai"