services:
  ai_assistant:
    image: ai_assistant
    container_name: ai_assistant
    build:
      context: ../
      dockerfile: ./Dockerfile
    ports:
      - "8000:8000"
    tty: true
    read_only: true
    environment:
      - WORKERS=2
      - PORT=8000
    volumes:
      - ../config:/home/<USER>/config
      - ../secrets-store:/home/<USER>/secrets-store
    tmpfs:
      - /tmp
      - /var/run
      - /home/<USER>/workspace
