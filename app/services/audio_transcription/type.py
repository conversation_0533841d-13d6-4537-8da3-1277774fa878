import json

from pydantic import BaseModel, Field, field_validator
from common.type import ModelName
from typing import Union, Dict, Any, List, Optional


# Define SpeakerDetail model
class SpeakerDetail(BaseModel):
    name: Optional[str] = Field(default=None, description="Speaker's name")
    role: Optional[str] = Field(default=None, description="Speaker's role (e.g., Advisor, Client)")


class AudioTranscriptionPayload(BaseModel):
    fileList: List[str] = Field(
        description="List of paths to the audio files to be transcribed. Paths start with 'private/' or 'shared/'."
    )
    language: str = Field(
        default="en", description="Language code for transcription (e.g., 'en', 'es', 'fr')"
    )
    num_speakers: Optional[int] = Field(
        default=None, description="Number of speakers expected in the audio."
    )
    speaker_details: Optional[List[SpeakerDetail]] = Field(
        default=None, description="Details (name, role) for each speaker, if known."
    )
    outputSchema: Union[Dict[str, Any], str] = Field(
        default={},
        description="JSON schema for structured output (if specific format needed beyond standard transcription)"
    )
    
    @field_validator('outputSchema')
    @classmethod
    def validate_output_schema(cls, schema, values):
        if schema:
            try:
                if isinstance(schema, str):
                    schema = json.loads(schema)
                # Basic schema validation
                if not isinstance(schema, dict):
                    raise ValueError("Schema must be a dictionary")
                if "type" not in schema:
                    raise ValueError("Schema must have a 'type' field")
            except Exception as e:
                raise ValueError(f"Invalid output schema: {str(e)}")
        return schema


class TranscriptionResponse(BaseModel):
    text: str = Field(description="Full transcription text")
    structured_output: List[dict] = Field(default=[], description="Time-segmented transcription (if requested)")
