import os
import traceback
import asyncio
import mimetypes
import json

from google import genai
from google.genai import types as genai_types
from pathlib import Path
from typing import List, Optional, Dict, Any, Union

from core.config import AUTH_PROPERTIES
from services.audio_transcription.type import SpeakerDetail
from common.type import ModelName

BASEPATH = os.path.abspath(os.path.dirname(__file__))
SUPPORTED_AUDIO_TYPES = ["audio/wav", "audio/mpeg", "audio/mp3", "audio/mp4", "audio/x-m4a", "audio/ogg"]

# Define a standard diarization schema
DIARIZATION_SCHEMA = {
    "type": "array",
    "items": {
        "type": "object",
        "properties": {
            "speaker": {"type": "string", "description": "Identified speaker label"},
            "text": {"type": "string", "description": "Transcribed text for the segment"}
        },
        "required": ["speaker", "text"]
    }
}

SIMPLE_TRANSCRIPTION_SCHEMA = {
    "type": "object",
    "properties": {
        "text": {
            "type": "string",
            "description": "Full transcription text"
        }
    },
    "required": ["text"]
}


class AudioTranscriber:
    def __init__(
        self,
        language="en",
        output_schema: Optional[Union[Dict[str, Any], str]] = None,
        num_speakers: Optional[int] = None,
        speaker_details: Optional[List[SpeakerDetail]] = None,
    ):
        self.genai_client = genai.Client(
            vertexai=True,
            project=AUTH_PROPERTIES.GOOGLE_CLOUD_PROJECT,
            location=AUTH_PROPERTIES.GOOGLE_CLOUD_LOCATION,
        )
        self.model_name = ModelName.GEMINI_20_FLASH
        self.language = language
        self.output_schema = output_schema
        self.num_speakers = num_speakers
        self.speaker_details = speaker_details

        prompt_path = os.path.join(BASEPATH, "./prompt.md")
        with open(prompt_path, "r") as fp:
            base_prompt = fp.read()

        self.base_system_instruction = genai_types.Content(
            parts=[genai_types.Part(text=base_prompt)],
            role="modal",
        )

        self.needs_diarization = bool(self.num_speakers or self.speaker_details)

        self.config = genai_types.GenerateContentConfig(
            response_mime_type="application/json",
            temperature=0,
        )

        if self.output_schema:
            if isinstance(self.output_schema, str):
                try:
                    self.output_schema = json.loads(self.output_schema)
                except json.JSONDecodeError:
                    print(f"Warning: Invalid custom output schema string provided: {self.output_schema}")
                    self.output_schema = DIARIZATION_SCHEMA if self.needs_diarization else SIMPLE_TRANSCRIPTION_SCHEMA
            self.config.response_schema = self.output_schema
        elif self.needs_diarization:
            self.config.response_schema = DIARIZATION_SCHEMA
        else:
            self.config.response_schema = SIMPLE_TRANSCRIPTION_SCHEMA

    async def __llm_transcribe(self, file_path: str):
        contents = []
        dynamic_prompt_parts = []

        dynamic_prompt_parts.append(f"Transcribe this audio file in {self.language} language.")
        
        if self.needs_diarization:
            if self.speaker_details:
                speaker_info = ", ".join([
                    f"{detail.name or f'Speaker {i+1}'} ({detail.role})"
                    for i, detail in enumerate(self.speaker_details)
                ])
                dynamic_prompt_parts.append(
                    f"Identify the speakers involved using these details: {speaker_info}. "
                    f"Output the transcription with speaker labels matching these roles/names."
                )
            elif self.num_speakers:
                dynamic_prompt_parts.append(
                    f"Identify the {self.num_speakers} speakers and label them as Speaker 1, Speaker 2, etc. "
                    f"Output the transcription with these speaker labels."
                )
            else:
                dynamic_prompt_parts.append("Output the full transcription text.")
        else:
            dynamic_prompt_parts.append("Output the full transcription text.")

        updated_system_text = self.base_system_instruction.parts[0].text + "\n\n" + "\n".join(dynamic_prompt_parts)
        system_instruction = genai_types.Content(
            parts=[genai_types.Part(text=updated_system_text)],
            role="user"
        )
        current_config = self.config
        current_config.system_instruction = system_instruction
        
        if os.path.exists(file_path):
            mime_type, _ = mimetypes.guess_type(file_path)
            
            if mime_type not in SUPPORTED_AUDIO_TYPES:
                raise ValueError(f"Unsupported audio file type: {mime_type}")
                
            data = Path(file_path).read_bytes()
            contents.append(genai_types.Part.from_bytes(data=data, mime_type=mime_type))
            contents.append(genai_types.Part.from_text(text="Trascribe the attached audio"))

        else:
            raise ValueError(f"Audio file not found: {file_path}")

        response = await self.genai_client.aio.models.generate_content(
            model=self.model_name,
            contents=contents,
            config=current_config
        )
        
        return response.parsed

    async def transcribe(self, file_list: List[str]):
        response = {"status": False, "data": None, "error": None}
        
        if not file_list:
            response["error"] = "No input file provided in fileList."
            return response
            
        file_to_process = file_list[0]

        try:
            transcription_result = await self.__llm_transcribe(file_to_process)
            
            output_data = {
                "text": "",
                "structured_output": []
            }


            if self.needs_diarization and isinstance(transcription_result, list):
                output_data["structured_output"] = transcription_result
                text_parts = []
                for segment in transcription_result:
                    speaker_label = segment.get('speaker', 'Unknown Speaker')
                    segment_text = segment.get('text', '')
                    text_parts.append(f"{speaker_label}: {segment_text}")
                output_data["text"] = "\n".join(text_parts)
            elif isinstance(transcription_result, dict) and 'segments' in transcription_result:
                output_data["structured_output"] = transcription_result['segments']
            elif isinstance(transcription_result, dict) and 'text' in transcription_result:
                output_data["text"] = transcription_result['text']
            else:
                raise ValueError(f"Unexpected transcription result format: {transcription_result}")

            response["status"] = True
            response["data"] = output_data

        except Exception as e:
            response["status"] = False
            response["error"] = str(e)
            traceback.print_exc()

        return response


if __name__ == "__main__":
    transcriber = AudioTranscriber()
    # Example test
    audio_file = "samples/conv1.m4a"
    if os.path.exists(audio_file):
        import time
        t1 = time.time()
        res = asyncio.run(transcriber.transcribe([audio_file]))
        t2 = time.time()
        print("Time taken for transcription == ", t2 - t1)
        print("res", res)
    else:
        print(f"Test file {audio_file} not found") 