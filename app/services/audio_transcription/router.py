from fastapi import <PERSON><PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.responses import JSONResponse
from typing import List, Optional
import os # Import os if needed for future path validation

from .audio_transcriber import AudioTranscriber
from .type import AudioTranscriptionPayload
from core.jiffy_drive import JiffyDrive


router = APIRouter(tags=["Audio Transcription"])


async def _get_local_file_path(file_path: str) -> str:
    """
    Determines if a file path is a JiffyDrive path and downloads it,
    otherwise assumes it's a local/accessible path. Returns the accessible local path.
    Raises HTTPException on errors.
    """
    if file_path.startswith("private/") or file_path.startswith("shared/"):
        jiffy_drive = JiffyDrive()
        try:
            print(f"Attempting to download from JiffyDrive: {file_path}")
            downloaded_path = await jiffy_drive.download_file_from_jiffydrive(file_path)
            if downloaded_path and os.path.exists(downloaded_path):
                print(f"Successfully downloaded to: {downloaded_path}")
                return downloaded_path
            else:
                # Log the failure reason if possible from jiffy_drive method
                error_detail = f"Failed to download file from JiffyDrive: {file_path}. Path not returned or does not exist."
                print(error_detail)
                raise HTTPException(status_code=500, detail=error_detail)
        except HTTPException as http_exc: # Re-raise specific HTTP exceptions if needed
             raise http_exc
        except Exception as e:
            # Log exception e
            error_detail = f"Error downloading file {file_path} from JiffyDrive: {str(e)}"
            print(error_detail)
            raise HTTPException(status_code=500, detail=error_detail)
    else:
        # Assume local/accessible path - Add validation if needed
        print(f"Assuming local/accessible path: {file_path}")
        # Example validation (optional, depends on requirements):
        # if not os.path.exists(file_path):
        #     raise HTTPException(status_code=400, detail=f"Local file path does not exist: {file_path}")
        return file_path

@router.post("/transcription")
async def audio_transcription_endpoint(
    request: Request, payload: AudioTranscriptionPayload
):
    tenant_id = request.headers.get("X-Jiffy-Tenant-ID", None)
    app_id = request.headers.get("X-Jiffy-App-ID", None)
    if not tenant_id or not app_id:
        raise HTTPException(
            status_code=400, detail="Tenant ID and App ID are required in headers"
        )

    if not payload.fileList:
        raise HTTPException(status_code=400, detail="fileList cannot be empty.")

    # Process only the first file
    first_file_path_from_payload = payload.fileList[0]
    local_file_path: Optional[str] = None

    try:
        local_file_path = await _get_local_file_path(first_file_path_from_payload)
    except HTTPException as http_exc:
        # Specific HTTP exceptions from the helper are re-raised
        raise http_exc
    except Exception as e:
        # Catch any other unexpected errors during path processing
        print(f"Unexpected error processing file path {first_file_path_from_payload}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Unexpected error processing file path: {str(e)}")

    # If local_file_path is still None or empty after the try block (shouldn't happen if helper raises),
    # it indicates a failure.
    if not local_file_path:
        # This case might be redundant if _get_local_file_path always raises on failure,
        # but serves as a safeguard.
        raise HTTPException(status_code=500, detail="Could not obtain a valid local file path to process.")

    # Use the validated local path
    downloaded_file_paths = [local_file_path]

    # Instantiate transcriber with new parameters
    transcriber = AudioTranscriber(
        language=payload.language,
        output_schema=payload.outputSchema,
        num_speakers=payload.num_speakers,
        speaker_details=payload.speaker_details
    )
    
    # Pass the list containing the single processed local path
    response = await transcriber.transcribe(downloaded_file_paths)
    
    if response["status"]:
        # The response["data"] should now contain {"text": ..., "structured_output": ...}
        return JSONResponse(content=response["data"])
    else:
        raise HTTPException(status_code=500, detail=response["error"]) 