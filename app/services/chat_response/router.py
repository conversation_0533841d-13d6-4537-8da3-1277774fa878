from fastapi import <PERSON><PERSON>out<PERSON>, Request, HTTPException
from fastapi.responses import JSONResponse

from .chat_responder import ChatR<PERSON>ponder
from .type import ChatResponsePayload

router = APIRouter(tags=["Chat Response"])


@router.post("/chat-response")
async def chat_response_endpoint(request: Request, payload: ChatResponsePayload):
    chat_responder = ChatResponder(payload.systemPrompt, payload.modelName)
    response = await chat_responder.respond(
        message=payload.message,
        output_message_type=payload.outputMessageType
    )
    
    if response["status"]:
        return JSONResponse(content=response["data"])
    else:
        raise HTTPException(status_code=500, detail=response["error"]) 