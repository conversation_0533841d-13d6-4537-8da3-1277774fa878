import os
import traceback
import async<PERSON>
import json
import mimetypes
from datetime import datetime
from typing import Dict, List, Optional, Union
from pathlib import Path

from google import genai
from google.genai import types as genai_types

from .constants import (
    DEFAULT_RESPONSE_FORMAT, TEXT_MESSAGE, IMAGE_MESSAGE, 
    PROMPT_MESSAGE, 
    PROMPT_RESPONSE_MESSAGE
)
from .type import (
    TextMessage, ImageMessage,
    PromptMessage, PromptResponseMessage, PromptType,
    MessageType
)
from common.type import ModelName

from core.config import AUTH_PROPERTIES
from core.jiffy_drive import JiffyDrive

BASEPATH = os.path.abspath(os.path.dirname(__file__))


class ChatResponder:
    def __init__(
        self,
        prompt="",
        model_name=ModelName.GEMINI_20_FLASH,
    ):
        self.genai_client = genai.Client(
            vertexai=True,
            project=AUTH_PROPERTIES.GOOGLE_CLOUD_PROJECT,
            location=AUTH_PROPERTIES.GOOGLE_CLOUD_LOCATION,
        )
        self.model_name = model_name
        self.jiffy_drive = JiffyDrive()

        if prompt:
            self.prompt_text = prompt
            return

        with open(os.path.join(BASEPATH, "./prompt.md"), "r") as fp:
            self.prompt_text = fp.read()

    async def _llm_generate_response(self, message: str) -> str:
        """
        Generate a response from the LLM based on the input message
        """
        config = genai_types.GenerateContentConfig(
            system_instruction=genai_types.Content(
                parts=[genai_types.Part(text=self.prompt_text)],
                role="modal",
            ),
            temperature=0.7,
        )

        contents = [genai_types.Part(text=message)]

        response = await self.genai_client.aio.models.generate_content(
            model=self.model_name,
            contents=contents,
            config=config,
        )
        
        return response.text

    def _create_text_message(self, message_content: str) -> TextMessage:
        """
        Create a TextMessage object from message content
        """
        return TextMessage(
            Type=MessageType.TextMessage,
            Message=message_content,
            Attachments=[]
        ).model_dump()

    def _create_image_message(self, image_paths: List[str]) -> ImageMessage:
        """
        Create an ImageMessage object from a list of image paths
        """
        return ImageMessage(
            Type=MessageType.ImageMessage,
            Images=image_paths,
            Attachments=[]
        ).model_dump()


    def _create_prompt_message(
        self, 
        prompt: str, 
        prompt_type: PromptType = PromptType.Text, 
        lov: List[str] = []
    ) -> PromptMessage:
        """
        Create a PromptMessage object
        """
        return PromptMessage(
            Type=PROMPT_MESSAGE,
            Prompt=prompt,
            PromptType=prompt_type,
            LOV=lov,
            Attachments=[]
        ).model_dump()

    def _create_prompt_response_message(
        self, 
        prompt: str, 
        response: List[str]
    ) -> PromptResponseMessage:
        """
        Create a PromptResponseMessage object
        """
        return PromptResponseMessage(
            Type=PROMPT_RESPONSE_MESSAGE,
            Prompt=prompt,
            Response=response,
            Attachments=[]
        ).model_dump()


    async def respond(
        self,
        message: str,
        output_message_type: str = DEFAULT_RESPONSE_FORMAT
    ):
        """
        Generate a response to the user input
        
        Args:
            message: The input message (text or file path)
            output_message_type: The type of message to return
        Returns:
            Dictionary with status and response data
        """
        response = {}
        
        try:
            # Get LLM response
            message_content = await self._llm_generate_response(message)
            
            # Create appropriate message based on type
            if output_message_type == MessageType.TextMessage:
                message = self._create_text_message(message_content)
            elif output_message_type == MessageType.ImageMessage:
                # This is a placeholder, in a real implementation
                # you would process the response to get image paths
                message = self._create_image_message([])
            elif output_message_type == MessageType.PromptMessage:
                # Placeholder for prompt creation
                message = self._create_prompt_message(message_content)
            elif output_message_type == MessageType.PromptResponseMessage:
                # Placeholder for prompt response
                message = self._create_prompt_response_message(message_content, [])
            else:
                # Default to text message
                message = self._create_text_message(message_content)
            
            response["status"] = True
            response["data"] = message
                
        except Exception as e:
            response["status"] = False
            response["error"] = str(e)
            traceback.print_exc()
            
        return response


if __name__ == "__main__":
    # Example usage
    tenant_id = "test-tenant"
    app_id = "test-app"
    
    chat_responder = ChatResponder()
    content = "Hello, how can you help me today?"
    
    result = asyncio.run(chat_responder.respond(content))
    print(result) 