import os
import base64
from datetime import datetime
from uuid import uuid4


def encode_file(file_path: str) -> str:
    """
    Encode a file to base64 string
    """
    with open(file_path, "rb") as file:
        return base64.b64encode(file.read()).decode()


def generate_id() -> str:
    """
    Generate a unique identifier
    """
    return str(uuid4())


def get_current_utc_datetime() -> datetime:
    """
    Get current UTC datetime
    """
    return datetime.utcnow() 