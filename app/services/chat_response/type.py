from typing import List, Literal, Optional, Union
from pydantic import BaseModel, Field, field_validator
from datetime import datetime
from enum import Enum, StrEnum
from common.type import ModelName


class ConversationItemActor(str, Enum):
    Human = "Human"
    Machine = "Machine"


class BaseMessage(BaseModel):
    Type: str
    Attachments: List[str] = Field(default=[]) 


class TextMessage(BaseMessage):
    Type: str = "Text"
    Message: str


class ImageMessage(BaseMessage):
    Type: str = "Image"
    Images: List[str]  # jiffy drive paths


class PromptType(str, Enum):
    Text = "Text"
    Number = "Number"
    Yes = "Yes"
    OK = "OK"
    YesNo = "YesNo"
    IAgree = "IAgree"
    AcceptReject = "AcceptReject"
    ApproveReject = "ApproveReject"
    RadioList = "RadioList"  # select 1
    CheckList = "CheckList"  # select many


class PromptMessage(BaseMessage):
    Type: str = "Prompt"
    PromptType: PromptType
    Prompt: str
    LOV: List[str] = Field(default=[])  # list of values


class PromptResponseMessage(BaseMessage):
    Type: str = "PromptResponse"
    Prompt: str
    Response: List[str]



class MessageType(StrEnum):
    TextMessage = "TextMessage"
    ImageMessage = "ImageMessage"
    PromptMessage = "PromptMessage"
    PromptResponseMessage = "PromptResponseMessage"


class ChatResponsePayload(BaseModel):
    message: str = Field(description="Text content to be processed for chat response")
    systemPrompt: str = Field(default="")
    modelName: ModelName = Field(default=ModelName.GEMINI_20_FLASH, description="Model to use for chat response")
    outputMessageType: MessageType = Field(default=MessageType.TextMessage, description="Type of message to return")
    
    @field_validator('modelName', mode='before')
    @classmethod
    def validate_model_name(cls, value):
        if value == "":
            return ModelName.GEMINI_20_FLASH
        return value 
    
    @field_validator('outputMessageType', mode='before')
    @classmethod
    def validate_output_message_type(cls, value):
        if value == "":
            return MessageType.TextMessage
        return value 