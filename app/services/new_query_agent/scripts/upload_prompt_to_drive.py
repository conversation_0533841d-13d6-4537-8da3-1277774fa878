import requests
import os
import pathlib

PLATFORM_FIRST_PARTY = {
    'base_url': 'https://platform-firstparty.platform-editor.cluster.jiffy.ai',
    'app_id': '********-a706-4124-b8df-67d7a5154eb2',
    'tenant_id': '25b1a56a-3063-48f7-b250-cb19ba3241a8',
    'user_id': '47beb676-0943-4d61-8936-6b8ea9114897',
    'trace_id': '6a2f34eadb48a0dadb6766d3f32655e5',
    'bearer_token': 'eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJNOEpidUpMaW9Ma2lwS2VhQnhxRHhoOE1TUUtvWWdDX2RzLXJYZjlURElJIn0.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.V5NKDNWa1SCX_N_TlGltOui75XMuVWrnZIK4mLiGfHTH1bAbQSc_RyAcW1VRX8rKC_66XJB19hYJZLRbwkuLj5sneVEt47eo0cS6ArdNWeagmDyksnYitMD9HeyCWxgpjIxDA0Ux7GJRrTit_EwAOzHNNtsBbKG63FJOGK965Q0bBXwTEaGjsk_SbcDxV_9ldj9QyNaUloRPZxFhg9OO6Gu2JVo05u8Mo5pTDM8zfp6qgxCvlzOM7yd3yXldnO_83ccYLZ0rg2wpxQH2CVCKGs7xMo1JP_l2DgiTD_tTKjxKqOS4WQIZTtxgN_UT7JPFPVyIeCu-yRDYVrQTQPwQvw'
}

PLATFORM_FIRST_PARTY_UAT = {
    'base_url': 'https://platform-firstparty.platform-editor.cluster.jiffy.ai',
    'app_id': '4c6f40a6-ba54-41a5-b491-59c210bad519',
    'tenant_id': '25b1a56a-3063-48f7-b250-cb19ba3241a8',
    'user_id': '47beb676-0943-4d61-8936-6b8ea9114897',
    'trace_id': '6a2f34eadb48a0dadb6766d3f32655e5',
    'bearer_token': 'eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJNOEpidUpMaW9Ma2lwS2VhQnhxRHhoOE1TUUtvWWdDX2RzLXJYZjlURElJIn0.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.OFXEb6sxYEbjRT-cc-7a3ZOn-IigEbebHHcC8ZTXMFLRPvcd-jhvR5mJTC3s_aOyTmSEd0MZKZhcoJRGS-HyLJjLnWDUVcw7zsGK40itkPDWJwo0d88L7cWT5pyFYJ4C3U2K_01gGNncWGNHYIFxot5i4aKoV805MUWD9L9hRxYpd4QcK_i4Ghl0FbRxwEoQSJdAFKZKshDLA4iO3aGdp2iOofaX0wbhctpSkI9lASOvCITqpjrns2ebGFpKNd8jIwPeDjo6Iigei6pC68eFL1OsEpPFlDmsVGELYuC49a9tOacTuEw2ydVZEn-8HHYtUGsTh2rIhhPu_sAItWB6dA'
}

PLATFORM_TRIAD = {
    'base_url': 'https://triad.platform-editor.cluster.jiffy.ai',
    'app_id': '335a92d3-d71a-40cb-ade8-5b2fbdb6e0fc',
    'tenant_id': 'f586e710-c5a0-47a3-a501-f0003c813315',
    'user_id': '9800f5b7-3f85-4666-931f-fe467d392671',
    'trace_id': '6a2f34eadb48a0dadb6766d3f32655e5',
    'bearer_token': 'eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJNOEpidUpMaW9Ma2lwS2VhQnhxRHhoOE1TUUtvWWdDX2RzLXJYZjlURElJIn0.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.OFXEb6sxYEbjRT-cc-7a3ZOn-IigEbebHHcC8ZTXMFLRPvcd-jhvR5mJTC3s_aOyTmSEd0MZKZhcoJRGS-HyLJjLnWDUVcw7zsGK40itkPDWJwo0d88L7cWT5pyFYJ4C3U2K_01gGNncWGNHYIFxot5i4aKoV805MUWD9L9hRxYpd4QcK_i4Ghl0FbRxwEoQSJdAFKZKshDLA4iO3aGdp2iOofaX0wbhctpSkI9lASOvCITqpjrns2ebGFpKNd8jIwPeDjo6Iigei6pC68eFL1OsEpPFlDmsVGELYuC49a9tOacTuEw2ydVZEn-8HHYtUGsTh2rIhhPu_sAItWB6dA'
}

ENVIRONMENT = PLATFORM_FIRST_PARTY

base_path = pathlib.Path(__file__).parent.parent
prompt_folder_path = base_path / "prompts"
api_context_path = base_path / "samples/api_context.json"

planning_url = ENVIRONMENT['base_url'] + "/platform/drive/v1/objects/private/DOCUMENT_PROCESSING/prompt_for_planning.txt"
answer_url = ENVIRONMENT['base_url'] + "/platform/drive/v1/objects/private/DOCUMENT_PROCESSING/prompt_for_answer.txt"
api_context_url = ENVIRONMENT['base_url'] + "/platform/drive/v1/objects/private/DOCUMENT_PROCESSING/api_context.json"

headers = {
  'authorization': f"Bearer {ENVIRONMENT['bearer_token']}",
  'x-b3-traceid': ENVIRONMENT['trace_id'],
  'x-jiffy-app-id': ENVIRONMENT['app_id'],
  'x-jiffy-target-app-id': ENVIRONMENT['app_id'],
  'x-jiffy-tenant-id': ENVIRONMENT['tenant_id'],
  'x-jiffy-user-id': ENVIRONMENT['user_id']
}

payload = {'config': '{"acl":"PUBLIC_READ"}'}

# Upload planning prompt
print("Uploading planning prompt...")
planning_files = [
  ('object',('planning_prompt.txt',open(os.path.join(prompt_folder_path, 'planning_prompt.txt'),'rb'),'text/plain'))
]
response = requests.request("POST", planning_url, headers=headers, data=payload, files=planning_files)
print(f"Planning prompt upload status: {response.status_code}")
print(f"Planning prompt response: {response.text}")

# Upload answer prompt
print("\nUploading answer prompt...")
answer_files = [
  ('object',('answer_prompt.txt',open(os.path.join(prompt_folder_path, 'answer_prompt.txt'),'rb'),'text/plain'))
]
response = requests.request("POST", answer_url, headers=headers, data=payload, files=answer_files)
print(f"Answer prompt upload status: {response.status_code}")
print(f"Answer prompt response: {response.text}")

# Upload api context
print("\nUploading api context...")
api_context_files = [
  ('object',('api_context.json',open(api_context_path,'rb'),'application/json'))
]
response = requests.request("POST", api_context_url, headers=headers, data=payload, files=api_context_files)
print(f"API context upload status: {response.status_code}")
print(f"API context response: {response.text}")
