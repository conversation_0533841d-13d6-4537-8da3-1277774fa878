- IMPORTANT: For filter condition, use strContainsIgnoreCase for string comparison unless specified otherwise.

For **every query on the `Account` object**, you must include the following default `select` block unless the user question explicitly overrides it.

```json
"select": {
  "id": true,
  "name": true,
  "accountNumber": true,
  "repCode": true,
  "registrationType": {
    "select": {
      "name": true,
      "custodianCode": true
    }
  },
  "investmentObjective": true,
  "riskTolerance": true,
  "accountStatus": true,
  "dailyBalances": {
    "select": {
      "endingMarketValue": true,
      "endingBalance": true,
      "endingCashBalance": true,
      "endingMoneyMarketBalance": true,
      "endingMarginBalance": true,
      "maintenanceCall": true,
      "fedCall": true
    }
  }
}
```