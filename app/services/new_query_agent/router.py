import json
import logging
import os
import sys
from pathlib import Path

from fastapi import APIRouter, HTTPException, Request, Body
from fastapi.responses import JSONResponse
from core.jiffy_drive import Jiffy<PERSON>rive

from .type import ApiAgentPayload
from core.config import SETTINGS

sys.path.append(str(Path(__file__).parent.parent.parent.parent))

from app.services.new_query_agent.api_agent import ApiAgent
from pathlib import Path

logger = logging.getLogger(__name__)

router = APIRouter(tags=["API Query Agent"])


@router.post("/api-query-agent/query")
async def api_query_agent(
    request: Request,
    payload: ApiAgentPayload = Body(...),
):
    """
    Process API agent query with the provided payload.

    Args:
        request: The FastAPI request object
        payload: The API agent payload containing query and configuration
    """
    tenant_id = request.headers.get("X-Jiffy-Tenant-ID", None)
    app_id = request.headers.get("X-Jiffy-App-ID", None)

    if not tenant_id or not app_id:
        raise HTTPException(
            status_code=400, detail="Tenant ID and App ID are required in headers"
        )

    try:
        # Get file paths, downloading from JiffyDrive if needed
        # create cache folder if not exists
        api_context_file_path = await _get_context_file_path(payload.apiContextPath)
        planning_prompt_path = await _get_planning_prompt_path(
            payload.planningPromptPath
        )
        answer_prompt_path = await _get_answer_prompt_path(payload.answerPromptPath)

        agent = ApiAgent(
            api_context_path=api_context_file_path,
            planing_prompt_path=planning_prompt_path,
            answer_prompt_path=answer_prompt_path,
            context_data=payload.contextData,
        )
        result = await agent.run(payload.query)

        return JSONResponse(
            content=result,
            media_type="application/json",
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"An error occurred while generating dynamic query: {str(e)}",
        )


async def _cache_file(drive_file_path: str, local_file_path: str) -> None:
    """
    Download files from JiffyDrive and return the local path if the
    Raises HTTPException on errors.
    """
    jiffy_drive = JiffyDrive()
    try:
        print(f"Attempting to download from JiffyDrive: {drive_file_path}")
        downloaded_path = await jiffy_drive.download_file_from_jiffydrive(
            drive_file_path, local_file_path
        )
        if downloaded_path and os.path.exists(downloaded_path):
            print(f"Successfully downloaded to: {downloaded_path}")
            return
        else:
            error_detail = f"Failed to download file from JiffyDrive: {drive_file_path}. Path not returned or does not exist."
            print(error_detail)
            raise HTTPException(status_code=500, detail=error_detail)
    except Exception as e:
        error_detail = (
            f"Error downloading file {drive_file_path} from JiffyDrive: {str(e)}"
        )
        print(error_detail)
        raise HTTPException(status_code=500, detail=error_detail)


async def _get_context_file_path(context_path: str | None) -> Path:
    api_context_filename = "api_context.json"

    """Get the API context file path, downloading from JiffyDrive if provided."""
    default_path = Path(__file__).parent / "samples" / api_context_filename

    if not context_path:
        return default_path

    try:
        cache_folder = os.path.join(SETTINGS.WORKSPACE_PATH, "api_agent")
        if not os.path.exists(cache_folder):
            os.makedirs(cache_folder)

        await _cache_file(
            context_path,
            os.path.join(cache_folder, api_context_filename),
        )
        return Path(os.path.join(cache_folder, api_context_filename))
    except Exception as e:
        print(
            f"An error occurred while caching the API context file: {str(e)}. Using local file"
        )
        return default_path


async def _get_planning_prompt_path(prompt_path: str | None) -> Path:
    """Get the planning prompt file path, downloading from JiffyDrive if provided."""

    planning_prompt_filename = "planning_prompt.txt"
    default_path = Path(__file__).parent / "prompts" / planning_prompt_filename

    if not prompt_path:
        return default_path

    try:
        cache_folder = os.path.join(SETTINGS.WORKSPACE_PATH, "api_agent")
        if not os.path.exists(cache_folder):
            os.makedirs(cache_folder)

        await _cache_file(
            prompt_path,
            os.path.join(cache_folder, planning_prompt_filename),
        )
        return Path(os.path.join(cache_folder, planning_prompt_filename))
    except Exception as e:
        print(
            f"An error occurred while caching the planning prompt file: {str(e)}. Using local file"
        )
        return default_path


async def _get_answer_prompt_path(prompt_path: str | None) -> Path:
    """Get the answer prompt file path, downloading from JiffyDrive if provided."""
    answer_prompt_filename = "answer_prompt.txt"
    default_path = Path(__file__).parent / "prompts" / answer_prompt_filename

    if not prompt_path:
        return default_path

    try:
        cache_folder = os.path.join(SETTINGS.WORKSPACE_PATH, "api_agent")
        if not os.path.exists(cache_folder):
            os.makedirs(cache_folder)

        await _cache_file(
            prompt_path,
            os.path.join(cache_folder, answer_prompt_filename),
        )
        return Path(os.path.join(cache_folder, answer_prompt_filename))
    except Exception as e:
        print(
            f"An error occurred while caching the answer prompt file: {str(e)}. Using local file"
        )
        return default_path
