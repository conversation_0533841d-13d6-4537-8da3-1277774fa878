from typing import Any
import sys
from pathlib import Path
from datetime import datetime, timedelta, date

sys.path.append(str(Path(__file__).parent.parent.parent.parent.parent))
from app.services.api_query_agent.utils.expression_evaluator import (
    ExpressionEvaluator,
)
from app.services.api_query_agent.type import ExpressionEvaluatorResponse


class DotDict:
    """Wrapper class to allow dot notation access to dictionary items"""

    def __init__(self, dictionary):
        self._dict = dictionary

    def __getattr__(self, name):
        try:
            value = self._dict[name]
            return DotDict(value) if isinstance(value, dict) else value
        except KeyError:
            raise AttributeError(f"'dict' object has no attribute '{name}'")

    def __getitem__(self, key):
        value = self._dict[key]
        return DotDict(value) if isinstance(value, dict) else value


def subtract_months(date_str: str, duration_in_months: int) -> str:
    original_date = datetime.strptime(date_str, "%Y-%m-%d").date()
    year = original_date.year
    month = original_date.month - duration_in_months
    day = original_date.day

    while month <= 0:
        month += 12
        year -= 1

    # Safely calculate last valid day of target month
    if month == 12:
        next_month = date(year + 1, 1, 1)
    else:
        next_month = date(year, month + 1, 1)

    last_day_of_month = (next_month - timedelta(days=1)).day
    day = min(day, last_day_of_month)

    new_date = date(year, month, day)
    return new_date.isoformat()


class PythonExpressionEvaluator(ExpressionEvaluator):

    # safe built-in functions allowed for evaluation
    SAFE_BUILTIN_FUNCTIONS = {
        "__builtins__": None,  # Remove all built-ins first
        "len": len,
        "str": str,
        "int": int,
        "float": float,
        "bool": bool,
        "list": list,
        "dict": dict,
        "sum": sum,
        "min": min,
        "max": max,
        "sorted": sorted,
        "reversed": reversed,
        "range": range,
        "enumerate": enumerate,
        "zip": zip,
        "map": map,
        "filter": filter,
        "any": any,
        "all": all,
        "getattr": getattr,  # Add support for attribute access
        "hasattr": hasattr,  # Useful for checking if attribute exists
        "isinstance": isinstance,  # Useful for type checking,
        "currentDate": lambda: date.today().isoformat(),
        "subMonths": subtract_months,
    }

    def execute(
        self, expression: str, input_data: dict[str, Any]
    ) -> ExpressionEvaluatorResponse:
        try:
            # Wrap input_data with DotDict for dot notation access
            wrapped_input_data = {
                k: DotDict(v) if isinstance(v, (dict, list)) else v
                for k, v in input_data.items()
            }
            result = eval(expression, self.SAFE_BUILTIN_FUNCTIONS, wrapped_input_data)
            return ExpressionEvaluatorResponse(result=result)
        except Exception as e:
            return ExpressionEvaluatorResponse(
                error=f"Error evaluating expression: {expression}. Error: {str(e)}"
            )

    def execute_multiple(
        self,
        expressions: list[str],
        input_data: dict[str, Any],
        skip_error: bool = False,
    ) -> ExpressionEvaluatorResponse:
        results = {}
        for expression in expressions:
            result = self.execute(expression, input_data)
            if result.error is None:
                results[expression] = result.result
            elif not skip_error:
                return ExpressionEvaluatorResponse(error=result.error)
        return ExpressionEvaluatorResponse(result=results)


# testcase
def main():
    evaluator = PythonExpressionEvaluator()

    result = evaluator.execute("subMonths('2025-01-01', 12)", {})
    assert result.result == "2024-01-01"

    # testcase 1
    result = evaluator.execute(
        "searchResults[0].id", {"searchResults": [{"id": 123456}]}
    )
    assert result.result == 123456

    result = evaluator.execute("1 + 1", {})
    assert result.result == 2

    result = evaluator.execute("a  + b", {"a": 1, "b": 2})
    assert result.result == 3
    assert result.error is None

    result = evaluator.execute("a +  b", {"a": 1})
    assert result.error is not None

    results = evaluator.execute_multiple(["a  +  b ", "a - b"], {"a": 1, "b": 2})
    assert results.result == {"a + b": 3, "a - b": -1}

    results = evaluator.execute_multiple(["a", "b"], {"a": 1})
    assert result.error is not None

    results = evaluator.execute_multiple(["a", "b"], {"a": 1}, skip_error=True)
    assert results.result == {"a": 1}
    assert results.error is None


if __name__ == "__main__":
    main()
