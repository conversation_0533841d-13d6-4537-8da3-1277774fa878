from ast import Dict
from typing import Union
from enum import StrEnum


class OperationType(StrEnum):
    """Enum for valid operation types"""
    INSERT = "insert"
    SELECT = "select"
    SELECT_SINGLE = "selectsingle"
    UPDATE = "update"
    DELETE = "delete"
    AGGREGATE = "aggregate"
    COUNT = "count"
    DISTINCT = "distinct"
    FILTER = "filter"
    ORDER_BY = "orderBy"
    GROUP_BY = "groupBy"
    LIMIT = "limit"
    OFFSET = "offset"
    JOIN = "join"


class QueryProcessor:
    def __init__(self, default_limit: int = 10) -> None:
        self.default_limit = default_limit
    
    def _process_operations(self, operations: Dict) -> Dict:
        if not isinstance(operations, dict):
            return 
        
        if OperationType.SELECT in operations:
            if OperationType.LIMIT not in operations:
                operations["limit"] = self.default_limit

    def process(self, query: Dict) -> Dict:
        for table_name, operations in query.items():
            self._process_operations(operations)
        
        return query


if __name__ == "__main__":
    query = {
        "Account": {
            "select": {
                "id": True,
                "accountNumber": True,
                "primaryOwner": {
                    "select": {
                        "firstName": True,
                        "lastName": True
                    }
                }
            }
        }
    }
    
    processor = QueryProcessor()
    processed_query = processor.process(query)
    import json
    print(json.dumps(processed_query, indent=2))
