import sys
from pathlib import Path

sys.path.append(str(Path(__file__).parent.parent.parent.parent.parent))
from typing import Any

from app.services.api_query_agent.utils.python_expression_evaluator import (
    PythonExpressionEvaluator,
)

from app.services.api_query_agent.utils.expression_evaluator import (
    ExpressionEvaluator,
)
from app.services.api_query_agent.type import ExpressionEvaluatorResponse
from app.services.api_query_agent.type import CallStep


class DotDict:
    """Wrapper class to allow dot notation access to dictionary items"""

    def __init__(self, dictionary):
        self._dict = dictionary

    def __getattr__(self, name):
        try:
            value = self._dict[name]
            return DotDict(value) if isinstance(value, dict) else value
        except KeyError:
            raise AttributeError(f"'dict' object has no attribute '{name}'")

    def __getitem__(self, key):
        value = self._dict[key]
        return DotDict(value) if isinstance(value, dict) else value


class ExpressionEvaluationUtil:
    def __init__(self):
        self.expression_evaluator = PythonExpressionEvaluator()

    def evaluate_exp_in_text(
        self,
        template: str,
        input_data: dict[str, Any],
        start_placeholder: str = "{",
        end_placeholder: str = "}",
        skip_error: bool = False,
    ) -> str:
        expressions = self.expression_evaluator.extract_expressions(
            template, start_placeholder, end_placeholder
        )
        result = self.expression_evaluator.execute_multiple(
            expressions, input_data, skip_error=skip_error
        )
        if result.error is not None and not skip_error:
            raise ValueError(
                f"Error in templating the string: {template}. Error: {result.error}"
            )
        template = self.expression_evaluator.replace_expressions(
            template, result.result, start_placeholder, end_placeholder
        )
        return template

    def evaluate_exp_in_json(
        self,
        json_data: Any,
        input_data: dict[str, Any],
        start_placeholder: str = "${",
        end_placeholder: str = "}",
        skip_error: bool = False,
    ) -> Any:
        if isinstance(json_data, dict):
            return {
                key: self.evaluate_exp_in_json(
                    value, input_data, start_placeholder, end_placeholder, skip_error
                )
                for key, value in json_data.items()
            }
        elif isinstance(json_data, list):
            return [
                self.evaluate_exp_in_json(
                    item, input_data, start_placeholder, end_placeholder, skip_error
                )
                for item in json_data
            ]
        elif isinstance(json_data, str):
            expressions = self.expression_evaluator.extract_expressions(
                json_data, start_placeholder, end_placeholder
            )
            if not expressions:
                return json_data

            result = self.expression_evaluator.execute_multiple(
                expressions, input_data, skip_error=skip_error
            )
            if result.error is not None and not skip_error:
                raise ValueError(
                    f"Error evaluating expression in JSON value: {json_data}. Error: {result.error}"
                )
            return self.expression_evaluator.replace_expressions(
                json_data, result.result, start_placeholder, end_placeholder
            )
        else:
            return json_data

    def evaluate_exp_in_call_step(
        self,
        call_step: CallStep,
        input_data: dict[str, Any],
        skip_error: bool = False,
    ) -> CallStep:
        if call_step.path_params:
            call_step.path_params = self.evaluate_exp_in_json(
                call_step.path_params, input_data, skip_error=skip_error
            )
        if call_step.query_params:
            call_step.query_params = self.evaluate_exp_in_json(
                call_step.query_params, input_data, skip_error=skip_error
            )
        if call_step.requestBody:
            call_step.requestBody = self.evaluate_exp_in_json(
                call_step.requestBody, input_data, skip_error=skip_error
            )
        return call_step


# testcase
def main():
    expression_evaluation_util = ExpressionEvaluationUtil()

    # testcase 1
    template = {"id": "${searchResults[0].id}"}
    input_data = {"searchResults": [{"id": 123456}]}
    result = expression_evaluation_util.evaluate_exp_in_json(template, input_data)
    assert result == {"id": 123456}

    base_template = "user/{name}/account/{account_id}"

    # testcase 1
    template = base_template
    input_data = {"name": "John", "account_id": 123456}
    result = expression_evaluation_util.evaluate_exp_in_text(template, input_data)
    assert result == "user/John/account/123456"

    # testcase 2
    template = base_template
    input_data = {"name": "John"}
    try:
        result = expression_evaluation_util.evaluate_exp_in_text(
            template, input_data, skip_error=True
        )
    except Exception as e:
        assert e is not None

    # testcase 3
    template = base_template
    input_data = {"name": "John"}
    result = expression_evaluation_util.evaluate_exp_in_text(
        template, input_data, skip_error=True
    )
    assert result == "user/John/account/{account_id}"

    name_placeholder = "${name}"
    account_id_placeholder = "${account_id}"
    account_type_placeholder = "${account_type}"

    # testcase 4
    json_data = {
        "user": {
            "name": name_placeholder,
            "account_id": account_id_placeholder,
            "account_type": account_type_placeholder,
        }
    }
    input_data = {
        "name": "John",
        "account_id": 123456,
        "account_type": "premium",
    }
    result = expression_evaluation_util.evaluate_exp_in_json(json_data, input_data)
    assert result == {
        "user": {
            "name": "John",
            "account_id": 123456,
            "account_type": "premium",
        }
    }

    # testcase 5
    json_data = {
        "user": {
            "name": name_placeholder,
            "account_id": account_id_placeholder,
            "account_type": account_type_placeholder,
        }
    }

    input_data = {
        "name": "John",
        "account_id": 123456,
    }
    result = expression_evaluation_util.evaluate_exp_in_json(
        json_data, input_data, skip_error=True
    )
    assert result == {
        "user": {
            "name": "John",
            "account_id": 123456,
            "account_type": "${account_type}",
        }
    }

    # testcase 6
    json_data = {
        "user": {
            "name": name_placeholder,
            "account_id": account_id_placeholder,
            "account_type": account_type_placeholder,
        }
    }

    input_data = {
        "name": "John",
        "account_id": 123456,
    }
    try:
        result = expression_evaluation_util.evaluate_exp_in_json(json_data, input_data)
    except Exception as e:
        assert e is not None


if __name__ == "__main__":
    main()
