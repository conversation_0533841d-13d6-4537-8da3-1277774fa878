from abc import ABC, abstractmethod
from typing import Any
import re
import sys
from pathlib import Path

sys.path.append(str(Path(__file__).parent.parent.parent.parent.parent))

from app.services.api_query_agent.type import ExpressionEvaluatorResponse


class ExpressionEvaluator(ABC):

    @abstractmethod
    def execute(
        self, expression: str, input_data: dict[str, Any]
    ) -> ExpressionEvaluatorResponse:
        pass

    @abstractmethod
    def execute_multiple(
        self,
        expressions: list[str],
        input_data: dict[str, Any],
        skip_error: bool = False,
    ) -> ExpressionEvaluatorResponse:
        pass

    def _get_expression_pattern(
        self, start_placeholder: str = "${", end_placeholder: str = "}"
    ) -> str:
        start_escaped = re.escape(start_placeholder)
        end_escaped = re.escape(end_placeholder)
        exp_pattern = f"{start_escaped}([^{end_escaped}]*){end_escaped}"
        return exp_pattern

    def extract_expression(
        self, text: str, start_placeholder: str = "${", end_placeholder: str = "}"
    ) -> str:
        """
        Extract the first expression within given placeholders from the text.
        Examples:
            - With default placeholders: "${expression}"
            - With custom placeholders: "<<expression>>" (if start="<<", end=">>")
        """
        exp_pattern = self._get_expression_pattern(start_placeholder, end_placeholder)
        matches = re.findall(exp_pattern, text)
        return matches[0] if matches else None

    def extract_expressions(
        self, text: str, start_placeholder: str = "${", end_placeholder: str = "}"
    ) -> list[str]:
        """
        Extract all expressions within ${...} from the given text.

        Args:
            text (str): Input text containing expressions

        Returns:
            list[str]: List of extracted expressions without ${...}
        """

        exp_pattern = self._get_expression_pattern(start_placeholder, end_placeholder)
        matches = re.findall(exp_pattern, text)
        return matches

    def replace_expressions(
        self,
        text: str,
        replacements: dict,
        start_placeholder: str = "${",
        end_placeholder: str = "}",
    ) -> str:
        """
        Replace all ${...} expressions in text with their values from replacements dict.

        Args:
            text (str): Input text containing ${...} expressions
            replacements (dict): Dictionary of expression -> value mappings

        Returns:
            str: Text with all expressions replaced
        """
        exp_pattern = self._get_expression_pattern(start_placeholder, end_placeholder)
        return re.sub(
            exp_pattern,
            lambda match: str(replacements.get(match.group(1), match.group(0))),
            text,
        )


# testcase
def main():
    class TestExpEvaluator(ExpressionEvaluator):
        def execute(
            self, expression: str, input_data: dict[str, Any]
        ) -> ExpressionEvaluatorResponse:
            return ExpressionEvaluatorResponse()

        def execute_multiple(
            self, expressions: list[str], input_data: dict[str, Any], skip_error: bool = False
        ) -> dict[str, Any]:
            return {}

    evaluator = TestExpEvaluator()

    # test extract_expression
    expression = evaluator.extract_expression("${a + b}")
    assert expression == "a + b"

    # test _replace_expression
    replaced = evaluator.replace_expressions("${a + b}", {"a + b": 10})
    assert replaced == "10"

    # test extract_expressions
    expressions = evaluator.extract_expressions("sum(${a}, ${b})")
    assert expressions == ["a", "b"]
    replaced = evaluator.replace_expressions("sum(${a}, ${b})", {"a": 10, "b": 10})
    assert replaced == "sum(10, 10)"


if __name__ == "__main__":
    main()
