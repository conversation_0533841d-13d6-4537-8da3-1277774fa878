import uuid
import json

with open("./api_context.json", "r") as f:
    api_context = json.load(f)


for page in api_context["pages"]:
    for endpoint in page["endpoints"]:
        endpoint["id"] = str(uuid.uuid4())

with open("./api_context.json", "w") as f:
    json.dump(api_context, f, indent=2)


# endpoints = []
# for page in api_context["pages"]:
#     for endpoint in page["endpoints"]:
#         endpoints.append(endpoint)

# data = {
#     "endpoints": endpoints,
# }
# with open("./api_context_new.json", "w") as f:
#     json.dump(data, f, indent=2)