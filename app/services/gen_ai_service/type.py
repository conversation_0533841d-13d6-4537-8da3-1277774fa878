import json

from pydantic import BaseModel, Field, field_validator
from typing import Union, Dict, Any, Optional, List

from pydantic import BaseModel, Field, field_validator

from common.type import ModelName


class GenAIPayload(BaseModel):
    systemInstructions: Optional[str] = Field(
        default=None,
        description="Optional system instructions for the LLM."
    )
    instruction: str = Field(
        description="The main instruction or prompt for the LLM (user query)."
    )
    fileList: Optional[List[str]] = Field(
        default=None,
        description="List of file paths from jiffydrive on which the instruction operates."
    )
    outputSchema: Union[Dict[str, Any], str] = Field(
        default={},
        description="JSON schema for structured output. Required if outputType is 'json'"
    )
    modelName: ModelName = Field(
        default=ModelName.GEMINI_20_FLASH,
        description="Model to use for processing"
    )
    temperature: float = Field(
        default=0.0,
        description="Temperature for response generation (0.0 to 1.0)",
        ge=0.0,
        le=1.0
    )
    
    @field_validator('modelName', mode='before')
    @classmethod
    def validate_model_name(cls, value):
        if value == "":
            return ModelName.GEMINI_20_FLASH
        return value
    

    @field_validator('outputSchema')
    @classmethod
    def validate_output_schema(cls, schema, values):
        if schema:
            try:
                if isinstance(schema, str):
                    schema = json.loads(schema)
                # Basic schema validation
                if not isinstance(schema, dict):
                    raise ValueError("Schema must be a dictionary")
                if "type" not in schema:
                    raise ValueError("Schema must have a 'type' field")
            except Exception as e:
                raise ValueError(f"Invalid output schema: {str(e)}")
        return schema


class GenericLLMResponse(BaseModel):
    output: Union[str, Dict[str, Any]] = Field(
        description="The generated output (text or structured data)"
    ) 