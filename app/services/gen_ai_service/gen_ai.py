import os
import traceback
import asyncio
import mimetypes
import json

from google import genai
from google.genai import types as genai_types
from pathlib import Path
from typing import Dict, Union, Any, Optional, List

from app.core.config import AUTH_PROPERTIES
from app.common.type import ModelName

BASEPATH = os.path.abspath(os.path.dirname(__file__))

# Define supported MIME types for direct model processing (similar to summarizer)
# Adjust this list based on the models used and their capabilities
MIMETYPE_APPLICATION_JSON = "application/json"
SUPPORTED_DIRECT_MIME_TYPES = [
    "application/pdf", MIMETYPE_APPLICATION_JSON,
    "audio/wav", "audio/mpeg", "audio/mp3", "audio/mp4", "audio/x-m4a", "audio/ogg",
    "image/jpeg", "image/png", "image/webp",
    "text/plain", "text/csv", "text/html", "text/css", "text/javascript", "text/markdown"
    # Add more as needed
]

TEXT_PLAIN_MIME_TYPE = "text/plain" # Define the constant

class GenAI:
    def __init__(
        self,
        instruction: str,
        system_instructions: Optional[str] = None,
        file_list: Optional[List[str]] = None,
        output_schema: Optional[Union[Dict[str, Any], str]] = None,
        model_name: ModelName = ModelName.GEMINI_20_FLASH,
        temperature: float = 0.0,
    ):
        self.genai_client = genai.Client(
            vertexai=True,
            project=AUTH_PROPERTIES.GOOGLE_CLOUD_PROJECT,
            location=AUTH_PROPERTIES.GOOGLE_CLOUD_LOCATION,
        )
        self.instruction = instruction
        self.system_instructions = system_instructions
        self.file_list = file_list or []
        self.output_schema = output_schema
        self.model_name = model_name
        self.temperature = temperature

        # Prepare System Instruction Content object
        final_system_instruction_text = self.system_instructions
        if not final_system_instruction_text:
            # Load default prompt if no specific system instructions provided
            try:
                prompt_path = os.path.join(BASEPATH, "./prompt.md")
                with open(prompt_path, "r") as fp:
                    final_system_instruction_text = fp.read()
            except FileNotFoundError:
                final_system_instruction_text = "You are a helpful AI assistant." # Fallback
        
        # Create the Content object for system instructions
        self.system_instruction_content = genai_types.Content(
            parts=[genai_types.Part(text=final_system_instruction_text)],
            role="system"
        )

        # Prepare Generation Config object
        self.generation_config = genai_types.GenerateContentConfig(
            temperature=self.temperature,
            system_instruction=self.system_instruction_content
        )

        # Configure response format based on output schema
        if self.output_schema:
            # Validate/parse schema string if necessary
            if isinstance(self.output_schema, str):
                try:
                    self.output_schema = json.loads(self.output_schema)
                except json.JSONDecodeError:
                    # Handle error - maybe raise or log and proceed without schema
                    print(f"Warning: Invalid JSON string provided for outputSchema: {self.output_schema}")
                    self.output_schema = None # Fallback to text output
            
            if isinstance(self.output_schema, dict):
                self.generation_config.response_mime_type = MIMETYPE_APPLICATION_JSON
                self.generation_config.response_schema = self.output_schema
            else:
                # If schema wasn't a dict after parsing/validation, fallback to text
                self.generation_config.response_mime_type = TEXT_PLAIN_MIME_TYPE # Use constant
                self.output_schema = None # Ensure self.output_schema reflects reality
        else:
            self.generation_config.response_mime_type = TEXT_PLAIN_MIME_TYPE # Use constant

    def _prepare_file_part(self, file_path: str) -> Optional[genai_types.Part]:
        """Checks a file, reads it if supported, and returns a Part object or None."""
        if not os.path.exists(file_path):
            print(f"Skipping file as path does not exist: {file_path}")
            return None

        mime_type, _ = mimetypes.guess_type(file_path)
        if not mime_type or mime_type not in SUPPORTED_DIRECT_MIME_TYPES:
            print(f"Skipping file due to unsupported MIME type ({mime_type}): {file_path}")
            return None

        try:

            if mime_type == MIMETYPE_APPLICATION_JSON:
                data = Path(file_path).read_text()
                part = genai_types.Part.from_text(text=data)
                print(f"Added json file to LLM context as text: {file_path} (MIME: {mime_type})")
                return part
            data = Path(file_path).read_bytes()
            part = genai_types.Part.from_bytes(data=data, mime_type=mime_type)
            print(f"Added file to LLM context: {file_path} (MIME: {mime_type})")
            return part
        except Exception as e:
            print(f"Error reading file {file_path} for LLM context: {e}")
            # Optionally raise or just skip the file
            return None

    async def __llm_process(self):
        # Contents list will include the main instruction and any file parts
        contents = []
        
        # Add the main instruction text part
        contents.append(genai_types.Part.from_text(text=self.instruction))
        
        # Process and add file parts from file_list using the helper method
        for file_path in self.file_list:
            file_part = self._prepare_file_part(file_path)
            if file_part:
                contents.append(file_part)
        
        # Check if any content was actually added (instruction + optional files)
        # Note: We now check > 1 because the instruction is always added first.
        # If only the instruction is present, and files were provided but failed,
        # we might still want to proceed. If no files were provided, len will be 1.
        # If the instruction itself is the only intended content, len is 1.
        # Let's refine the check: raise error only if the instruction itself is missing,
        # which shouldn't happen based on the constructor logic.
        # The original check `len(contents) == 0` seems incorrect as instruction is added first.
        # Let's revert to a check ensuring *at least* the instruction is there.
        if not contents: # This state should be unreachable if self.instruction is mandatory
             raise ValueError("No content available to send to the LLM (instruction missing).")

        # Make the API call
        try:
            response = await self.genai_client.aio.models.generate_content(
                model=str(self.model_name),
                contents=contents,
                config=self.generation_config
            )
        
            # Extract response based on expected format
            if self.output_schema: # Indicates JSON was requested
                return response.parsed
            else: # Plain text requested
                return response.text
        except Exception as e:
            print(f"Error during LLM API call: {e}")
            traceback.print_exc()
            raise # Re-raise the exception

    async def process(self):
        # Process method no longer takes content argument
        response_data = {"status": False, "data": None, "error": None}
        try:
            # Call the internal method which now uses instance variables
            result = await self.__llm_process()
            response_data["status"] = True
            response_data["data"] = {"output": result}
        except Exception as e:
            response_data["status"] = False
            response_data["error"] = str(e)
            # traceback is already printed in __llm_process

        return response_data


# Update __main__ block for testing
if __name__ == "__main__":
    # Example test with text output (no files)
    print("--- Testing Text Output --- ")
    llm_text = GenAI(
        instruction="What is the capital of France?",
        model_name=ModelName.GEMINI_20_FLASH, # Use an available model
        temperature=0.2
    )
    res_text = asyncio.run(llm_text.process())
    print("Text output:", json.dumps(res_text, indent=2))

    # Example test with JSON output (no files)
    print("\n--- Testing JSON Output --- ")
    json_schema = {
        "type": "object",
        "properties": {
            "capital": {"type": "string"},
            "population_estimate": {"type": "number"}
        },
        "required": ["capital"]
    }
    llm_json = GenAI(
        instruction="Provide the capital of Italy and its estimated population.",
        output_schema=json_schema,
        model_name=ModelName.GEMINI_20_FLASH # Use a model potentially better at JSON
    )
    res_json = asyncio.run(llm_json.process())
    print(res_json)
    print("JSON output:", json.dumps(res_json, indent=2))
    
    # Example test with file input (replace with actual file path)
    print("\n--- Testing File Input (Text Output) --- ")
    file_to_process = "/home/<USER>/Documents/apex/ai-assistant/app/services/generic_llm/test1.md"
    file_to_process_2 = "/home/<USER>/Documents/apex/ai-assistant/app/services/generic_llm/test2.md"

    files = [file_to_process, file_to_process_2]

    if os.path.exists(file_to_process):
        llm_file = GenAI(
            instruction="Summarize the content of the provided file in one sentence.",
            file_list=files,
            model_name=ModelName.GEMINI_20_FLASH
        )
        res_file = asyncio.run(llm_file.process())
        print("File input result:", json.dumps(res_file, indent=2))
    else:
        print(f"File test skipped: {file_to_process} not found.") 