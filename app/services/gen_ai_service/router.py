from fastapi import <PERSON><PERSON>out<PERSON>, Request, HTTPException
from fastapi.responses import J<PERSON><PERSON>esponse
from typing import List, Optional, Any

from .gen_ai import GenA<PERSON>
from .type import GenAIPayload, GenericLLMResponse
from core.jiffy_drive import JiffyDrive
import asyncio
import os


router = APIRouter(tags=["Generic LLM"])


def _validate_download_result(result: Any, original_path: str) -> str:
    """
    Validates the result of a single download task from asyncio.gather.
    Returns the validated local path string or raises HTTPException.
    """
    if isinstance(result, Exception):
        error_detail = f"Error downloading file '{original_path}' from JiffyDrive: {result}"
        print(error_detail)
        raise HTTPException(status_code=500, detail=error_detail)
    elif result and isinstance(result, str) and os.path.exists(result):
        print(f"Successfully downloaded '{original_path}' to '{result}'")
        return result
    else:
        error_detail = f"Failed to download file '{original_path}' from JiffyDrive (invalid result: {result})."
        print(error_detail)
        raise HTTPException(status_code=500, detail=error_detail)

def _classify_paths_for_processing(file_list: List[Any]) -> tuple[List[str], List[Any], List[str]]:
    """Classifies file paths into local paths and tasks for JiffyDrive downloads."""
    local_paths: List[str] = []
    download_tasks = []
    paths_to_download_info = [] # Original paths for context
    jiffy_drive = None # Instantiate lazily if needed

    for file_path in file_list:
        if not isinstance(file_path, str):
            print(f"Warning: Skipping non-string item in fileList: {file_path}")
            continue

        if file_path.startswith("private/") or file_path.startswith("shared/"):
            # Instantiate JiffyDrive only once, when first needed
            if jiffy_drive is None:
                jiffy_drive = JiffyDrive()
            print(f"Scheduling download for JiffyDrive path: {file_path}")
            # Assuming download_file_from_jiffydrive is a method of the instance
            download_tasks.append(jiffy_drive.download_file_from_jiffydrive(file_path))
            paths_to_download_info.append(file_path)
        else:
            print(f"Assuming local/accessible path: {file_path}")
            local_paths.append(file_path)
            
    return local_paths, download_tasks, paths_to_download_info

async def _download_and_validate_files(download_tasks: List[Any], paths_to_download_info: List[str]) -> List[str]:
    """Executes download tasks concurrently and validates results, returning downloaded paths.
       Assumes download_tasks is not empty.
    """
    downloaded_paths: List[str] = []
    print(f"Attempting to download {len(download_tasks)} file(s) concurrently...")
    try:
        download_results = await asyncio.gather(*download_tasks, return_exceptions=True)

        for i, result in enumerate(download_results):
            original_path = paths_to_download_info[i]
            # Directly call validation; exception will propagate if it fails
            validated_path = _validate_download_result(result, original_path)
            downloaded_paths.append(validated_path)
                
    except HTTPException as http_exc:
        # Re-raise HTTPExceptions that were raised during validation
        print(f"Validation failed for a file, propagating HTTPException: {http_exc.detail}")
        raise http_exc
    except Exception as e:
        # Catch any other unexpected errors (e.g., from asyncio.gather)
        error_detail = f"Unexpected error during concurrent file downloads: {str(e)}"
        print(error_detail)
        # Optionally log the full traceback for e here
        raise HTTPException(status_code=500, detail=error_detail)
        
    return downloaded_paths

async def _process_file_list(file_list: Optional[List[Any]]) -> List[str]:
    """
    Processes a list of file paths by classifying them and handling downloads.
    Returns a combined list of local and successfully downloaded file paths.
    Raises HTTPException on errors during classification or download/validation.
    """
    if not file_list:
        return []

    # --- Stage 1: Classify paths using helper ---    
    local_paths, download_tasks, paths_to_download_info = _classify_paths_for_processing(file_list)
    
    # --- Stage 2: Execute downloads only if needed --- 
    downloaded_paths: List[str] = []
    if download_tasks:
        # Call helper only when there are tasks
        downloaded_paths = await _download_and_validate_files(download_tasks, paths_to_download_info)

    # --- Stage 3: Combine results --- 
    all_processed_paths = local_paths + downloaded_paths
    print(f"Processed file paths: {all_processed_paths}")
    return all_processed_paths


@router.post("/gen-ai", response_model=GenericLLMResponse)
async def generic_llm_endpoint(
    request: Request, payload: GenAIPayload
):
    tenant_id = request.headers.get("X-Jiffy-Tenant-ID", None)
    app_id = request.headers.get("X-Jiffy-App-ID", None)
    if not tenant_id or not app_id:
        raise HTTPException(
            status_code=400, detail="Tenant ID and App ID are required in headers"
        )

    try:
        # Process file list using the helper function
        processed_file_paths = await _process_file_list(payload.fileList)
    except HTTPException as http_exc:
        # Re-raise HTTP exceptions from the helper
        raise http_exc
    except Exception as e:
        # Catch any other unexpected errors during file processing
        print(f"Unexpected error processing file list: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Unexpected error processing file list: {str(e)}")

    # Instantiate GenAI with the processed file paths
    llm = GenAI(
        instruction=payload.instruction,
        system_instructions=payload.systemInstructions,
        file_list=processed_file_paths,
        output_schema=payload.outputSchema,
        model_name=payload.modelName,
        temperature=payload.temperature
    )
    
    try:
        # Call the LLM process
        response = await llm.process()
        
        if response["status"]:
            # Ensure the data matches the GenericLLMResponse model
            # The llm.process already returns data in the format {"output": result}
            return response["data"] 
        else:
            error_detail = response.get("error", "Unknown error from LLM service")
            print(f"LLM processing failed: {error_detail}")
            raise HTTPException(status_code=500, detail=error_detail)
    except Exception as e:
        # Catch errors during the LLM call itself
        print(f"Error during LLM processing: {str(e)}")
        # Consider logging traceback here
        raise HTTPException(status_code=500, detail=f"Error during LLM processing: {str(e)}") 