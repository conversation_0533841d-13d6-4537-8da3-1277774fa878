# Icon Generation Prompt

You are an expert UI/UX designer creating icons for software tools. Generate a clean, modern icon based on the following requirements:

## Icon Requirements:
1. **Simplicity**: Use a SINGLE, clear visual element that represents the tool's function. Avoid cluttered designs with multiple UI elements.
2. **Consistency**: All icons should:
   - Use a consistent color palette (primary: #4A90E2, secondary: #7B68EE, accent: #50C878)
   - Have similar visual weight and style
3. **Style**: Flat, minimalist design with:
   - Clean geometric shapes
   - No gradients unless specifically requested
   - No text or letters
   - No surrounding stuff. Only icon should be present in the generated image. 
   - Transparent background
4. **Visual Focus**: The icon should:
   - The icon should Fill 100% of the canvas. No padding should be added 
   - Have clear, recognizable silhouette
   - Use ONE primary metaphor or symbol
5. **No Text**: There should not be any descriptive text in the generated icon image. 

## Design Guidelines:
- For data/information services: Use simple shapes like documents, folders, or data symbols
- For communication services: Use clean envelope, chat bubble, or signal icons
- For processing/workflow services: Use simple gears, arrows, or flow symbols
- For analysis services: Use charts, magnifying glass, or graph symbols
- Analyze the schema and decide on the purpose of the tool and come up with the icon. Try to minimize the elements used.

## Strict Rules:
1. Use ONLY ONE main visual element - no combining multiple symbols
2. Use the specified color palette consistently across all icons
3. Avoid small details that won't be visible at smaller sizes
4. Ensure high contrast between the icon and transparent background
5. Avoid Adding text or extra description around the icon. Only icon should be present in the generated image
