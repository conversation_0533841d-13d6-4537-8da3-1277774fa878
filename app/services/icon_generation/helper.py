import io

from PIL import Image


def resize_icon(icon_data: bytes, size: int = 128) -> bytes:
    """Resize an icon to the specified size."""
    try:
        # Open the image from bytes
        img = Image.open(io.BytesIO(icon_data))

        # Convert to RGBA if not already
        if img.mode != 'RGBA':
            img = img.convert('RGBA')

        # Resize the image
        img = img.resize((size, size), Image.Resampling.LANCZOS)

        # Save back to bytes
        output = io.BytesIO()
        img.save(output, format='PNG')
        return output.getvalue()
    except Exception as e:
        print(f"Error resizing icon: {e}")
        return icon_data
