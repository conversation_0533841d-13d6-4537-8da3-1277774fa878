from typing import List, Optional, Literal, Dict, Any
from pydantic import BaseModel, Field
from common.type import ModelName


class ToolSchema(BaseModel):
    """Schema definition for a single tool/service"""
    name: str = Field(description="Name of the tool")
    schema: Dict[str, Any] = Field(description="Complete schema containing input, output, and any other metadata")


class IconGenerationRequest(BaseModel):
    """Request model for generating icon for a single tool"""
    tool: ToolSchema = Field(description="The tool to generate icon for")
    full_context: Optional[List[Dict[str, Any]]] = Field(
        default=None, 
        description="Optional full JSON context containing all tools (for better understanding)"
    )
    icon_size: int = Field(default=128, description="Size of the generated icon in pixels")
    style: Literal["flat", "gradient", "outline"] = Field(default="flat", description="Style of the icon")
    text_model: ModelName = Field(default=ModelName.GEMINI_20_FLASH, description="Model to use for description generation")
    image_model: str = Field(default="imagen-3.0-generate-002", description="Model to use for icon generation")


class IconGenerationResponse(BaseModel):
    """Response model for icon generation"""
    tool_name: str = Field(description="Name of the tool")
    tool_uuid: str = Field(description="UUID of the tool")
    generated_description: str = Field(description="Generated user-friendly description")
    icon_data: str = Field(description="Base64 encoded PNG icon data")
    icon_metadata: Dict[str, Any] = Field(
        default={},
        description="Additional metadata about the icon (size, format, etc.)"
    )
    status: Literal["success", "error"] = Field(description="Status of the operation")
    error: Optional[str] = Field(default=None, description="Error message if any") 