from fastapi import APIRouter, Request
from fastapi.responses import JSONResponse

from .icon_generator import IconGenerator
from .type import IconGenerationRequest, IconGenerationResponse

router = APIRouter(tags=["Icon Generation"])


@router.post("/icon-generation/generate")
async def generate_icon_for_tool(request: Request, payload: IconGenerationRequest):
    """
    Generate icon and description for a single tool.
    
    The request should include:
    - tool: The tool schema with name, uuid, description, input/output schemas
    - full_context (optional): The complete list of tools for better context understanding
    - icon_size (optional): Size of the icon (default: 128)
    - style (optional): Icon style - flat, gradient, or outline (default: flat)
    - text_model (optional): Model for description generation
    - image_model (optional): Model for icon generation
    """
    try:
        # Initialize icon generator with specified models
        icon_generator = IconGenerator(
            text_model_name=payload.text_model,
            image_model_name=payload.image_model
        )
        
        # Process the tool
        result = await icon_generator.process_tool(
            tool=payload.tool,
            full_context=payload.full_context,
            icon_size=payload.icon_size,
            style=payload.style
        )
        
        return JSONResponse(content=result.model_dump())
        
    except Exception as e:
        error_response = IconGenerationResponse(
            tool_name=payload.tool.name,
            tool_uuid=payload.tool.uuid or "",
            generated_description="",
            icon_data="",
            icon_metadata={},
            status="error",
            error=str(e)
        )
        return JSONResponse(
            content=error_response.model_dump(),
            status_code=500
        ) 