import os
import base64
import traceback
from typing import Dict, Any, Optional, List
from google import genai
from google.genai import types as genai_types

from .type import ToolSchema, IconGenerationResponse
from .helper import resize_icon
from core.config import AUTH_PROPERTIES
from common.type import ModelName

BASEPATH = os.path.abspath(os.path.dirname(__file__))

# IMAGE_MODEL_NAME = "imagen-3.0-generate-002"
IMAGE_MODEL_NAME = "imagen-4.0-ultra-generate-preview-06-06"


class IconGenerator:
    def __init__(
        self,
        text_model_name: ModelName = ModelName.GEMINI_20_FLASH,
        image_model_name: str = IMAGE_MODEL_NAME,
    ):
        self.text_model_name = text_model_name
        self.image_model_name = image_model_name
        
        self.genai_client = genai.Client(
            vertexai=True,
            project=AUTH_PROPERTIES.GOOGLE_CLOUD_PROJECT,
            location=AUTH_PROPERTIES.GOOGLE_CLOUD_LOCATION,
        )
        
        # Load the icon generation prompt
        with open(os.path.join(BASEPATH, "./prompt.md"), "r") as fp:
            self.icon_prompt_template = fp.read()
    


    async def generate_description(
        self, 
        tool: ToolSchema, 
        full_context: Optional[List[Dict[str, Any]]] = None
    ) -> str:
        """Generate a user-friendly description for a tool."""
        try:
            schema_info = tool.schema
            original_description = schema_info["description"] or ""
            
            prompt = f"""
            Given the tool name "{tool.name}" and its technical details:
            - Original description: "{original_description}"
            - Full schema: {tool.schema}
            
            Create a concise, user-friendly description of what this tool does.
            This description will be shown to users in a UI. Keep it to one or two sentences.
            Focus on the business value and what the tool accomplishes for the user.
            Make it clear and actionable.
            """
            
            response = await self.genai_client.aio.models.generate_content(
                model=self.text_model_name,
                contents=prompt,
            )
            
            return response.text.strip()
        except Exception as e:
            print(f"Error generating description for {tool.name}: {e}")
            return schema_info["description"] or f"Tool for {tool.name}"
    
    async def generate_icon(
        self, 
        tool: ToolSchema,
        description: str,
        icon_size: int = 128,
        style: str = "flat"
    ) -> tuple[Optional[bytes], Dict[str, Any]]:
        """Generate an icon for a tool."""
        try:
            # Extract keywords and categorize
            # Convert tool schema to dict format for keyword extraction

            prompt = f"""
            {self.icon_prompt_template}
            
            The tool for which you need to create an icon is described below:
            Tool Name: {tool.name}
            Tool Schema and metadata: {tool.schema}

            
            Create a simple icon using the Green background, white overlay Flat style. Ensure icon fills the entire canvas
            The icon must represent this tool's specific function with simple visual elements.
            """
            
            response = await self.genai_client.aio.models.generate_images(
                model=self.image_model_name,
                prompt=prompt,
                config=genai_types.GenerateImagesConfig(
                    number_of_images=1,
                    aspect_ratio="1:1",
                    safety_filter_level="block_only_high",
                    person_generation="dont_allow",
                )
            )
            
            if response.generated_images:
                icon_bytes = response.generated_images[0].image.image_bytes
                # Ensure the icon is exactly the requested size
                icon_bytes = resize_icon(icon_bytes, icon_size)
                
                metadata = {
                    "size": icon_size,
                    "format": "PNG",
                    "style": style
                }
                
                return icon_bytes, metadata
            else:
                print(f"No images generated for {tool.name}")
                return None, {}
        except Exception as e:
            print(f"Error generating icon for {tool.name}: {e}")
            traceback.print_exc()
            return None, {}
    
    async def process_tool(
        self,
        tool: ToolSchema,
        full_context: Optional[List[Dict[str, Any]]] = None,
        icon_size: int = 128,
        style: str = "flat"
    ) -> IconGenerationResponse:
        """Process a single tool to generate description and icon."""
        try:
            # Generate description
            generated_description = await self.generate_description(tool, full_context)
            
            # Generate icon
            icon_data, metadata = await self.generate_icon(
                tool,
                generated_description,
                icon_size,
                style
            )
            
            # Extract UUID if available in schema
            tool_uuid = tool.schema.get("uuid", tool.schema.get("id", ""))
            
            if icon_data:
                # Convert to base64
                icon_base64 = base64.b64encode(icon_data).decode('utf-8')
                
                return IconGenerationResponse(
                    tool_name=tool.name,
                    tool_uuid=tool_uuid,
                    generated_description=generated_description,
                    icon_data=icon_base64,
                    icon_metadata=metadata,
                    status="success"
                )
            else:
                return IconGenerationResponse(
                    tool_name=tool.name,
                    tool_uuid=tool_uuid,
                    generated_description=generated_description,
                    icon_data="",
                    icon_metadata={},
                    status="error",
                    error="Failed to generate icon"
                )
            
        except Exception as e:
            error_msg = f"Error processing tool {tool.name}: {str(e)}"
            print(error_msg)
            traceback.print_exc()
            
            return IconGenerationResponse(
                tool_name=tool.name,
                tool_uuid="",
                generated_description="",
                icon_data="",
                icon_metadata={},
                status="error",
                error=error_msg
            ) 