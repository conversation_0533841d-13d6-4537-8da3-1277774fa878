import os
import traceback
import asyncio
import mimetypes


from google import genai
from google.genai import types as genai_types
from pathlib import Path
from typing import List, Union

from common.type import ModelName
from core.config import AUTH_PROPERTIES

BASEPATH = os.path.abspath(os.path.dirname(__file__))


class DataClassifier:
    def __init__(
        self,
        prompt="",
        model_name=ModelName.GEMINI_20_FLASH,
        output_schema: dict = {},
    ):
        self.genai_client = genai.Client(
            vertexai=True,
            project=AUTH_PROPERTIES.GOOGLE_CLOUD_PROJECT,
            location=AUTH_PROPERTIES.GOOGLE_CLOUD_LOCATION,
        )
        self.model_name = model_name
        self.default_output_schema = False if output_schema else True

        if not prompt:
            prompt_path = os.path.join(BASEPATH, "./prompt_single.md")
            with open(prompt_path, "r") as fp:
                prompt = fp.read()

        if not output_schema:
            output_schema = {
                "type": "object",
                "properties": {
                    "prediction": {
                        "type": "string",
                        "description": "The predicted label for the given data",
                    }
                },
            }

        self.config = genai_types.GenerateContentConfig(
            system_instruction=genai_types.Content(
                parts=[genai_types.Part(text=prompt)],
                role="modal",
            ),
            response_mime_type="application/json",
            response_schema=output_schema,
            temperature=0,
        )

    async def __llm_classify(
        self,
        content: str,
        labels: List[str],
    ):
        contents = []
        if os.path.exists(content):
            file_path = content
            mime_type, _ = mimetypes.guess_type(file_path)
            data = Path(content).read_bytes()
            contents.extend(
                [
                    f"Labels: {labels}",
                    genai_types.Part.from_bytes(data=data, mime_type=mime_type),
                ]
            )
        else:
            contents.append(f"Data: {content} \nLabels: {labels}")

        response = await self.genai_client.aio.models.generate_content(
            model=self.model_name,
            contents=contents,
            config=self.config,
        )
        if not self.default_output_schema:
            return response.parsed
        else:
            data = response.parsed
            return data.get("prediction", "")

    async def classify(
        self,
        content: str,
        labels: Union[List[str], str],
    ):
        response = {}
        if isinstance(labels, str):
            labels = labels.split(",")
        try:
            predicted_labels = await self.__llm_classify(content, labels)
            response["status"] = True
            response["data"] = predicted_labels
        except Exception as e:
            response["status"] = False
            response["error"] = str(e)
            traceback.print_exc()

        return response


if __name__ == "__main__":
    classifier = DataClassifier()
    data = "Annual revenue growth of 15% reported in Q4 2023"
    labels = ["Financial", "Marketing", "Operations", "HR"]
    res = asyncio.run(classifier.classify(data, labels))
    print("res", res)
