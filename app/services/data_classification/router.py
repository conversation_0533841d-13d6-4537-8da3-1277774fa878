from fastapi import API<PERSON>out<PERSON>, Request, HTTPException
from fastapi.responses import <PERSON><PERSON><PERSON><PERSON>ponse

from .data_classifier import DataClassifier
from .type import DataClassificationPayload
from core.jiffy_drive import JiffyDrive


router = APIRouter(tags=["Data Classification"])


@router.post("/data-classification")
async def data_classification_endpoint(
    request: Request, payload: DataClassificationPayload
):
    tenant_id = request.headers.get("X-Jiffy-Tenant-ID", None)
    app_id = request.headers.get("X-Jiffy-App-ID", None)
    if not tenant_id or not app_id:
        raise HTTPException(
            status_code=400, detail="Tenant ID and App ID are required in headers"
        )

    if payload.content.startswith("private/") or payload.content.startswith("shared/"):
        jiffy_drive = JiffyDrive()
        payload.content = await jiffy_drive.download_file_from_jiffydrive(
            payload.content
        )
    classifier = DataClassifier(payload.prompt, payload.modelName, payload.outputSchema)
    response = await classifier.classify(payload.content, payload.labels)
    if response["status"]:
        return JSONResponse(content=response["data"])
    else:
        raise HTTPException(status_code=500, detail=response["error"])
