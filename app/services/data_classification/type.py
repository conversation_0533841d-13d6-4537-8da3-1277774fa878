import json

from pydantic import BaseModel, Field, field_validator
from typing import List, Union
from jsonschema import Draft7Validator
from common.type import ModelName


class DataClassificationPayload(BaseModel):
    content: str = Field(
        description="Content to be classified. It can be a text or a file path."
    )
    labels: Union[List[str], str]
    prompt: str = Field(default="")
    modelName: ModelName = Field(
        default=ModelName.GEMINI_20_FLASH, description="Model to use for classification"
    )
    outputSchema: Union[dict, str] = Field(
        default={}, description="Output json schema for the classification"
    )

    @field_validator("outputSchema")
    @classmethod
    def validate_json_schema(cls, schema):
        if schema:
            try:
                if isinstance(schema, str):
                    schema = json.loads(schema)
                Draft7Validator.check_schema(schema)
            except Exception as e:
                raise ValueError(f"Invalid output schema: {str(e)}")
        return schema
    
    @field_validator('modelName', mode='before')
    @classmethod
    def validate_model_name(cls, value):
        if value == "":
            return ModelName.GEMINI_20_FLASH
        return value


class LLMResponse(BaseModel):
    type: str = Field(description="Predicted type for the data")


# class Group(BaseModel):
#     label: str = Field(description="Label name")
#     start_page: int = Field(description="Start page number")
#     end_page: int = Field(description="End page number")


# class LLMResponse(BaseModel):
#     groups: List[Group] = Field(
#         description="List of groups with label name, start page number and end page number"
#     )
