import asyncio
import json
import mimetypes
import os
import traceback
from pathlib import Path
from typing import Optional

from google import genai
from google.genai import types as genai_types

from core.config import AUTH_PROPERTIES
from common.type import ModelName

BASEPATH = os.path.abspath(os.path.dirname(__file__))

# Define the fixed output schema
SUMMARY_OUTPUT_SCHEMA = {
    "type": "object",
    "properties": {
        "summary_text": {
            "type": "string",
            "description": "The main generated summary text."
        },
        "bullet_points": {
            "type": "array",
            "items": {"type": "string"},
            "description": "Important bullet points regarding the content."
        },
        "action_items": {
            "type": "array",
            "items": {"type": "string"},
            "description": "Action items or next steps identified from the content."
        }
    },
    "required": ["summary_text", "bullet_points", "action_items"]
}

# Define supported MIME types for direct model processing
SUPPORTED_DIRECT_MIME_TYPES = [
    "application/pdf",
    "audio/wav", "audio/mpeg", "audio/mp3", "audio/mp4", "audio/x-m4a", "audio/ogg",
    # Add other types Gemini 1.5 Pro supports directly if needed
]


class Summarizer:
    def __init__(
            self,
            prompt: Optional[str] = None,
    ):
        self.genai_client = genai.Client(
            vertexai=True,
            project=AUTH_PROPERTIES.GOOGLE_CLOUD_PROJECT,
            location=AUTH_PROPERTIES.GOOGLE_CLOUD_LOCATION,
        )
        self.model_name = ModelName.GEMINI_20_FLASH
        self.user_prompt = prompt

        prompt_path = os.path.join(BASEPATH, "./prompt.md")
        try:
            with open(prompt_path, "r") as fp:
                self.base_prompt = fp.read()
        except FileNotFoundError:
            self.base_prompt = "You are a helpful assistant tasked with summarizing content."

    async def __llm_summarize(self, content: str):
        contents = []
        system_instruction_parts = [self.base_prompt]

        system_instruction_parts.append(
            "Analyze the provided content and generate the following components:\n"
            "1. summary_text: A concise summary of the main points.\n"
            "2. bullet_points: A list of key highlights or important takeaways.\n"
            "3. action_items: A list of actionable tasks or next steps mentioned or implied."
        )

        if self.user_prompt:
            system_instruction_parts.append(f"\nFollow these additional instructions: {self.user_prompt}")

        is_file = False
        file_path = None
        mime_type = None

        if isinstance(content, str) and os.path.exists(content):
            mime_type, _ = mimetypes.guess_type(content)
            if mime_type and mime_type in SUPPORTED_DIRECT_MIME_TYPES:
                is_file = True
                file_path = content
            # else: Treat as text or handle unsupported file type error? Assuming text for now.

        if is_file and file_path and mime_type:
            system_instruction_parts.append("\nThe following content is provided as a file:")
            data = Path(file_path).read_bytes()
            contents.append(genai_types.Part.from_text(text="The content that needs to be processed is given below:"))
            contents.append(genai_types.Part.from_bytes(data=data, mime_type=mime_type))

        elif isinstance(content, str):
            system_instruction_parts.append("\nThe following content is provided as text:")
            contents.append(genai_types.Part.from_text(text=content))
        else:
            raise ValueError("Invalid content type provided for summarization.")

        final_system_instruction_text = "\n".join(system_instruction_parts)
        system_instruction = genai_types.Content(
            parts=[genai_types.Part.from_text(text=final_system_instruction_text)],
            role="system"
        )

        try:
            config = genai_types.GenerateContentConfig(
                response_mime_type="application/json",
                temperature=0.1,
                response_schema=SUMMARY_OUTPUT_SCHEMA,
                system_instruction=system_instruction
            )

            response = await self.genai_client.aio.models.generate_content(
                model=self.model_name,
                contents=contents,
                config=config
            )

            return response.parsed

        except Exception as e:
            print(
                f"Error during LLM summarization call. System Instruction:\n{final_system_instruction_text}\nError: {e}")
            traceback.print_exc()
            raise

    async def summarize(self, content: str):
        response = {"status": False, "data": None, "error": None}
        try:
            summary_data = await self.__llm_summarize(content)

            validated_data = {
                "summary_text": summary_data.get("summary_text", ""),
                "bullet_points": summary_data.get("bullet_points", []),
                "action_items": summary_data.get("action_items", [])
            }

            response["status"] = True
            response["data"] = validated_data
        except Exception as e:
            response["status"] = False
            response["error"] = f"Summarization failed: {str(e)}"

        return response


if __name__ == "__main__":
    summarizer_text = Summarizer(prompt="Focus on the impact on businesses.")
    text = """
    The Internet of Things (IoT) is transforming how we live and work. IoT refers to the network of physical objects embedded with sensors, software, and connectivity that enables these objects to connect and exchange data. From smart homes to industrial automation, IoT technologies are becoming increasingly prevalent across various domains. Smart thermostats can learn your preferences and optimize energy usage. Connected vehicles can communicate with traffic infrastructure to reduce congestion. In healthcare, wearable devices monitor vital signs and transmit data to healthcare providers. Industrial IoT applications improve operational efficiency through predictive maintenance and real-time monitoring. However, IoT also raises concerns about privacy, security, and dependency on technology. As IoT continues to grow, addressing these challenges while harnessing its benefits will be crucial for sustainable implementation. Key action: Companies should invest in robust security protocols. Next step: Evaluate vendor security practices.
    """
    print("--- Testing Text Summarization ---")
    res_text = asyncio.run(summarizer_text.summarize(text))
    print(json.dumps(res_text, indent=2))

    pdf_file = "samples/Amazon1.pdf"
    abs_pdf_path = os.path.join(BASEPATH, pdf_file)
    if os.path.exists(abs_pdf_path):
        print(f"\n--- Testing PDF Summarization ({abs_pdf_path}) ---")
        summarizer_pdf = Summarizer()
        res_pdf = asyncio.run(summarizer_pdf.summarize(abs_pdf_path))
        print(json.dumps(res_pdf, indent=2))
    else:
        print(f"\n--- PDF Test Skipped: File not found at {abs_pdf_path} ---")

    # TODO: Add test for audio file if needed, ensure sample exists
