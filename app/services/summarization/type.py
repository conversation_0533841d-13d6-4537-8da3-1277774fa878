import json

from pydantic import BaseModel, Field, field_validator
from typing import Union, List, Optional


class SummarizationPayload(BaseModel):
    content: str = Field(
        description="Content to be summarized. Can be freeform text or a file path (PDF, audio). Paths start with 'private/' or 'shared/'."
    )
    prompt: Optional[str] = Field(default=None, description="Optional custom instructions or context for summarization")


class SummaryResponse(BaseModel):
    summary_text: str = Field(description="The main generated summary text")
    bullet_points: List[str] = Field(
        default=[], description="Important bullet points regarding the content"
    )
    action_items: List[str] = Field(
        default=[], description="Action items or next steps identified from the content"
    ) 