from fastapi import APIRout<PERSON>, Request, HTTPException
from fastapi.responses import JSONResponse

from .summarizer import Summarizer
from .type import SummarizationPayload, SummaryResponse
from core.jiffy_drive import JiffyDrive


router = APIRouter(tags=["Summarization"])


@router.post("/summarization", response_model=SummaryResponse)
async def summarization_endpoint(
    request: Request, payload: SummarizationPayload
):
    tenant_id = request.headers.get("X-Jiffy-Tenant-ID", None)
    app_id = request.headers.get("X-Jiffy-App-ID", None)
    if not tenant_id or not app_id:
        raise HTTPException(
            status_code=400, detail="Tenant ID and App ID are required in headers"
        )

    content_to_process = payload.content # Default to original content (text or accessible path)
    
    # Check if content is a JiffyDrive path that needs downloading
    if isinstance(payload.content, str) and (payload.content.startswith("private/") or payload.content.startswith("shared/")):
        jiffy_drive = JiffyDrive()
        try:
            downloaded_path = await jiffy_drive.download_file_from_jiffydrive(
                payload.content
            )
            if downloaded_path:
                content_to_process = downloaded_path # Use the downloaded local path
            else:
                 raise HTTPException(status_code=500, detail=f"Failed to download file from JiffyDrive: {payload.content}")
        except Exception as e:
             raise HTTPException(status_code=500, detail=f"Error downloading file {payload.content}: {str(e)}")
        
    # Instantiate summarizer with only the prompt
    summarizer = Summarizer(prompt=payload.prompt) 
    
    response = await summarizer.summarize(content_to_process)
    
    if response["status"]:
        return response["data"]
    else:
        raise HTTPException(status_code=500, detail=response["error"]) 