import logging
from typing import Dict, Any, <PERSON>, Tuple
from abc import ABC, abstractmethod


class QueryRule(ABC):
    """Abstract base class for query validation and transformation rules"""

    @abstractmethod
    def apply(self, query: Dict[str, Any]) -> Tuple[bool, Dict[str, Any]]:
        """Apply the rule to validate and transform the query

        Args:
            query: The query to process

        Returns:
            Tuple of (is_valid, processed_query)
        """
        pass


class BasicStructureRule(QueryRule):
    """Ensures query has required basic structure"""

    def apply(self, query: Dict[str, Any]) -> Tuple[bool, Dict[str, Any]]:
        if not isinstance(query, dict) or not query:
            return False, {}

        # Check for multiple root level keys
        if len(query.keys()) > 1:
            return False, {}

        # check if all the keys are as per the given schema

        return True, query


class RootKeyCapitalizationRule(QueryRule):
    """Ensures first letter of root key is capitalized"""

    def apply(self, query: Dict[str, Any]) -> <PERSON><PERSON>[bool, Dict[str, Any]]:
        # Transform key and check validity in one pass
        for key, value in query.items():
            if not key[0].isupper():
                new_query = {}
                new_query[key[0].upper() + key[1:]] = value
                return False, new_query
            else:
                break
        return True, query


class DynamicQueryProcessor:
    """Processes dynamic queries by applying validation and transformation rules.

    This class orchestrates the application of multiple rules for:
    1. Validating query structure and content
    2. Transforming queries to meet requirements
    3. Ensuring compliance with business rules
    """

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.rules: List[QueryRule] = [
            BasicStructureRule(),
            RootKeyCapitalizationRule(),
            # Add more rules here
        ]

    def process_query(self, query: Dict[str, Any]) -> Dict[str, Any]:
        """Process and validate the dynamic query by applying all rules.

        Args:
            query: The dynamic query to process

        Returns:
            Modified and validated query
        """
        try:
            self.logger.debug("Processing dynamic query")
            self.logger.debug(f"Input query: {query}")

            processed_query = query.copy()

            # Apply each rule in sequence
            for rule in self.rules:
                rule_name = rule.__class__.__name__
                self.logger.debug(f"Applying {rule_name}")

                is_valid, processed_query = rule.apply(processed_query)

                if not is_valid:
                    self.logger.warning(
                        f"Rule {rule_name} validation failed, "
                        f"updating the query to:\n{processed_query}"
                    )

                if not processed_query:
                    self.logger.warning(
                        f"Rule {rule_name} returned empty query, "
                        f"updating the query to:\n{processed_query}"
                    )
                    break

            self.logger.debug("Query processing completed successfully")
            self.logger.debug(f"Processed query: {processed_query}")

            return processed_query

        except Exception as e:
            self.logger.error(f"Error processing query: {str(e)}", exc_info=True)
            return {}
