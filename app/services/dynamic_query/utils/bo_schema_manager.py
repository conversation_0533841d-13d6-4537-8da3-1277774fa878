from app.core.cache_service import CacheService
from app.core.cache_service import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from typing import Any
import logging
import os
from pathlib import Path
from app.core.auth import get_headers
from app.core.type import Header
from app.core.rest_utils import RestUtils
from app.core.config import SERVICES_PROPERTIES
from app.services.dynamic_query.utils.bo_schema_helper import BOSchemaHelper

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

DEFAULT_DOMAIN_TYPE = "jiffy"
DEFAULT_DOMAIN_NAME = "defaultInternalService"
DEFAULT_DOMAIN_NAME_ALIAS = "default"
DOMAIN_MODEL_DOMAIN_TYPE = "domain"

CACHE_KEY_DELIMITER = "#"


class BOSchemaManager:

    BoSchemaType = dict[str, Any]
    DEFAULT_CACHE_DIR = "dynamic_query"
    DEFAULT_TTL = 7 * 24 * 60 * 60

    def __init__(self):

        self.cache = CacheService[self.BoSchemaType](
            provider=DiskCacheProvider[self.BoSchemaType](
                cache_dir=self.DEFAULT_CACHE_DIR
            )
        )
        self.rest_utils = RestUtils()

    async def get_bo_json_schemas(
        self,
        tenant_id: str,
        app_id: str,
        domain_name: str,
        domain_type: str,
        force_refresh_bo_definitions: bool = False,
    ) -> dict | None:
        logger.info(
            f"Getting BO JSON schemas for tenant_id: {tenant_id}, app_id: {app_id}, domain_type: {domain_type}, domain_name: {domain_name}"
        )
        bo_json_schemas = await self.cache.get(
            cache_key=self.get_bo_json_schema_cache_key(
                tenant_id=tenant_id,
                app_id=app_id,
                domain_type=domain_type,
                domain_name=domain_name,
            ),
            fetch_func=self.fetch_bo_json_schemas,
            ttl=self.DEFAULT_TTL,
            force_refresh=force_refresh_bo_definitions,
        )
        return bo_json_schemas

    def get_bo_json_schema_cache_key(
        self, tenant_id: str, app_id: str, domain_type: str, domain_name: str
    ) -> str:
        cache_key = f"{tenant_id}{CACHE_KEY_DELIMITER}{app_id}{CACHE_KEY_DELIMITER}{domain_type}{CACHE_KEY_DELIMITER}{domain_name}"
        logger.info(f"BO JSON schema cache key: {cache_key}")
        return cache_key

    async def fetch_bo_json_schemas(self, cache_key: str) -> dict | None:
        # Get headers with service account token
        headers = await get_headers()

        # For domain builder app get the corresponding app headers
        #  get domain name from cache key
        domain_name = cache_key.split(CACHE_KEY_DELIMITER)[-1]
        model_app_id = headers["X-Jiffy-App-Id"]

        if domain_name != DEFAULT_DOMAIN_NAME:
            # Get APP details
            app_details = await self.get_app_details(headers)
            # Check domain model BOs
            model_app_id = ""
            for domain in app_details["domainModels"]:
                if domain_name == domain["name"]:
                    model_app_id = domain["app_inst_id"]
                    break
            if model_app_id == "":
                logger.error(
                    f"Domain model app_inst_id not found for domain: {domain_name}"
                )
                return None

            # update headers with domain model app_inst_id
            headers["X-Jiffy-App-Id"] = model_app_id

        logger.info(
            f"Getting BO JSON schemas for: {domain_name} with app_inst_id: {model_app_id}"
        )
        bo_definitions = await self.get_bo_definitions(headers)
        bo_json_schemas = BOSchemaHelper(bo_definitions).to_json_schema()
        return bo_json_schemas

    async def get_app_details(self, headers: Header) -> dict | None:
        tenant_id = headers["X-Jiffy-Tenant-Id"]
        app_id = headers["X-Jiffy-App-Id"]
        url = f"{SERVICES_PROPERTIES.PAM_URL}/pam/tenant/{tenant_id}/inst/{app_id}"
        app_details = await self.rest_utils.get(url, headers)
        return app_details

    async def get_bo_definitions(self, headers: Header) -> list[dict] | None:
        url = f"{SERVICES_PROPERTIES.APP_DATA_MANAGER_URL}/api/v1/schema/representation"
        bo_definitions = await self.rest_utils.get(url, headers)
        return bo_definitions

    async def get_domain_details(self, domain: str) -> tuple[str, str]:
        """
        Get the domain type and name from the domain string.
        If the domain is not valid, return empty string for both domain type and name.
        """
        domain_type: str = ""
        domain_name: str = ""
        # Get domain details form the app
        headers = await get_headers()
        app_details = await self.get_app_details(headers)
        is_domain_builder_app = app_details["features"]["domainbuilderapp"]
        if is_domain_builder_app:
            if domain == app_details["name"]:
                domain_type = DOMAIN_MODEL_DOMAIN_TYPE
                domain_name = domain
        else:
            # starter app can have default internal service models along with domain models

            # Check for default model
            if domain == DEFAULT_DOMAIN_NAME_ALIAS or domain == DEFAULT_DOMAIN_NAME:
                domain_type = DEFAULT_DOMAIN_TYPE
                domain_name = DEFAULT_DOMAIN_NAME
            else:
                # Check if it is matching with any of the domain models other wise return default domain name
                for domain_model in app_details["domainModels"]:
                    if domain == domain_model["name"]:
                        domain_type = DOMAIN_MODEL_DOMAIN_TYPE
                        domain_name = domain

        return domain_type, domain_name
