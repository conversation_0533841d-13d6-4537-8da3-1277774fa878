import logging
from app.core.auth import get_headers
from app.core.type import Header
from app.core.config import SERVICES_PROPERTIES
from app.core.rest_utils import RestUtils

DEFAULT_LIMIT = 10


class DynamicQueryExecutor:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.rest_utils = RestUtils()

    async def run(
        self,
        query: dict,
        domain_name: str = "",
        domain_type: str = "",
        bo_name: str = "",
    ) -> list[dict] | None:

        # Validate query
        if not query or not query.keys() or len(query.keys()) == 0:
            return None

        # Validate given bo_name with the query root bo name
        # Check root query key
        root_query_key: str = list(query.keys())[0]
        # Make first character in the root key lowercase
        root_query_key = root_query_key[0].lower() + root_query_key[1:]
        root_key = bo_name if bo_name and bo_name == root_query_key else root_query_key

        # Validate domain
        if not domain_name or not domain_type:
            return None

        # Execute dynamic query
        try:
            headers = await get_headers()
            results = await self.execute_dynamic_query(
                headers, domain_name, domain_type, root_key, query
            )
            self.logger.info(f"DynamicQueryExecutor | Results size: {len(results)}")
            return results
        except Exception as e:
            if hasattr(e, "response") and e.response is not None:
                error_message = f"API {e.response.url} failed with status code {e.response.status_code}"
                error_message += f"\nResponse content: {e.response.content.decode()}"
                print(error_message)
            self.logger.info(f"DynamicQueryExecutor | Error: {e}")
            return None

    def process_query(self, query: dict):
        """
        Adds a 'select' key with 'id: true' to the inner dictionary of the
        top-level key if 'select' does not already exist.

        Args:
            query (dict): The input dictionary.

        Returns:
            dict: The modified dictionary.
        """
        if not query:
            return {}  # Handle empty dictionary

        top_level_key = list(query.keys())[0]  # Get the first (and assumed to be only) top-level key
        inner_dict = query[top_level_key]

        if "select" not in inner_dict:
            inner_dict["select"] = {"id": True}
        if "limit" not in inner_dict:
            inner_dict["limit"] = DEFAULT_LIMIT
        query[top_level_key] = inner_dict
        return query


    async def execute_dynamic_query(
        self,
        headers: Header,
        domain_name: str,
        domain_type: str,
        bo_name: str,
        query: dict,
    ) -> list[dict] | None:
        url = f"{SERVICES_PROPERTIES.APP_DATA_MANAGER_URL}/api/{domain_type}/{domain_name}/{bo_name}/dynamic/query"
        self.logger.info(f"DynamicQueryExecutor | URL: {url}")
        self.process_query(query)
        self.logger.info(f"DynamicQueryExecutor | Data: {query}")
        return await self.rest_utils.post(url, headers, query)
