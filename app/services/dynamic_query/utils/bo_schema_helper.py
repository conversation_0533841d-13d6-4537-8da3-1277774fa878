# Helper file for RAG implementation
# Contains utility functions and helper methods to support the main RAG functionality

import os
import sys

# Add project root to Python path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "../../.."))
sys.path.append(project_root)

import json


class BOSchemaHelper:
    def __init__(self, bo_schemas: list[dict] = None):
        self.bo_schemas = bo_schemas
        self.SCHEMA_PATH = "resources/schemas/schemas.json"
        self.CLEANED_SCHEMA_PATH = "resources/schemas/cleaned_schemas.json"
        self.JSON_SCHEMA_FILE_PATH = "resources/schemas/json_schema.json"

    def get_bo_schemas(self) -> list[dict]:
        if self.bo_schemas is not None:
            return self.bo_schemas

        with open(self.SCHEMA_PATH, "r") as f:
            return json.load(f)

    def _process_field(self, field_info: dict) -> dict:
        type_info = field_info["Type"]

        field_type = "str"
        constraints = []
        reference = ""
        if "Property" in type_info:
            field_type = (
                type_info["Property"]["type"]["name"].split("::")[1]
                if "::" in type_info["Property"]["type"]["name"]
                else "str"
            )
            constraints = type_info["Property"].get("constraints", [])
        elif "Link" in type_info:
            field_type = "array" if type_info["Link"].get("multi") == True else "object"
            reference = type_info["Link"]["name"]

        return {
            "name": field_info["name"],
            "label": field_info.get("label", field_info["name"]),
            "type": field_type,
            "constraints": constraints,
            "reference": reference,
        }

    def clean_bo_schemas(self) -> list[dict]:
        schemas: list[dict] = self.get_bo_schemas()
        cleaned_schemas = []

        for schema in schemas:
            if not schema:
                continue

            fields = [
                self._process_field(field_info) for field_info in schema.get("fields")
            ]

            cleaned_schemas.append(
                {
                    "name": schema.get("name", ""),
                    "description": schema.get("description", schema.get("name")),
                    "fields": fields,
                    "type": "object",
                }
            )

        return cleaned_schemas

    @staticmethod
    def _convert_type(bo_type: str) -> dict:
        """Convert business object type to JSON Schema type and format"""
        type_mapping = {
            "str": {"type": "string"},
            "int64": {"type": "integer"},
            "float64": {"type": "number"},
            "bool": {"type": "boolean"},
            "datetime": {"type": "string", "format": "date-time"},
            "local_date": {"type": "string", "format": "date"},
            "uuid": {"type": "string", "format": "uuid"},
            "array": {"type": "array"},
            "object": {"type": "object"},
            "duration": {
                "type": "string",
                "pattern": "^([0-1][0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$",
                "minimum": "00:00:00",
                "maximum": "23:59:59",
            },
        }
        return type_mapping.get(bo_type, {"type": bo_type})

    @staticmethod
    def _convert_constraints(constraints: list) -> dict:
        """Convert business object constraints to JSON Schema constraints"""
        json_constraints = {}
        for constraint in constraints:
            if "Constraint" not in constraint:
                continue

            data = constraint["Constraint"]
            if "MaxLength" in data:
                json_constraints["maxLength"] = data["MaxLength"]["value"]
            elif "MinLength" in data:
                json_constraints["minLength"] = 0
            elif "MaxValue" in data:
                json_constraints["maximum"] = float(data["MaxValue"]["value"])
            elif "MinValue" in data:
                json_constraints["minimum"] = float(data["MinValue"]["value"])
            elif "Precision" in data:
                json_constraints["multipleOf"] = 1.0 / (
                    10 ** int(data["Precision"]["value"])
                )
            elif "RegEx" in data:
                json_constraints["pattern"] = data["RegEx"]["pattern"]
            elif "OneOf" in data:
                json_constraints["enum"] = data["OneOf"]["values"]
        return json_constraints

    def _convert_field(self, field: dict) -> dict:
        """Convert business object field to JSON Schema property"""
        property_schema = {"title": field["label"]}
        field_type = field["type"]

        property_schema.update(self._convert_type(field_type))

        if field_type == "array" and field["reference"]:
            property_schema["items"] = {"$ref": f'#/definitions/{field["reference"]}'}
        elif field_type == "object" and field["reference"]:
            property_schema["$ref"] = f'#/definitions/{field["reference"]}'

        if field_type not in ["datetime", "local_date", "duration"]:
            property_schema.update(
                self._convert_constraints(field.get("constraints", []))
            )

        return property_schema

    def _convert_business_object(self, bo: dict) -> dict:
        """Convert business object to JSON Schema object definition"""
        return {
            "type": "object",
            "title": bo["name"],
            "description": bo.get("description", ""),
            "properties": {
                field["name"]: self._convert_field(field) for field in bo["fields"]
            },
        }

    def to_json_schema(self) -> dict:
        """Convert business object schema to JSON Schema format."""
        if self.bo_schemas is None:
            with open(self.CLEANED_SCHEMA_PATH, "r") as f:
                bo_schema = json.load(f)
        else:
            bo_schema = self.clean_bo_schemas()

        definitions = {
            bo["name"]: self._convert_business_object(bo) for bo in bo_schema
        }

        root_schema = {
            "$schema": "http://json-schema.org/draft-07/schema#",
            "definitions": definitions,
        }
        return root_schema


def main():
    schema_helper = BOSchemaHelper()
    schema_helper.clean_bo_schemas()
    schema_helper.to_json_schema()


if __name__ == "__main__":
    main()
