import os
import sys
from pathlib import Path

sys.path.append(str(Path(__file__).parent.parent.parent.parent))

import logging
import re
import shutil
from datetime import datetime
import asyncio
from app.services.dynamic_query.type import DynamicQueryLLMResponse
from langchain_core.output_parsers import JsonOutputParser
from langchain_core.messages import SystemMessage, HumanMessage, AIMessage
from langchain_core.messages.base import BaseMessage
import re

# Add root directory to Python path
root_dir = str(Path(__file__).parent.parent.parent.parent)
sys.path.append(root_dir)

import json
from app.services.dynamic_query.utils.dynamic_query_processor import (
    DynamicQueryProcessor,
)
from app.services.dynamic_query.utils.dynamic_query_executor import DynamicQueryExecutor
from app.core.config import AUTH_PROPERTIES, Settings
from langchain_google_vertexai import ChatVertexAI

from langchain_core.callbacks import BaseCallbackHandler

logger = logging.getLogger(__name__)

# Constants
BO_SCHEMA_PATH = Path(__file__).parent / "resources" / "schemas" / "json_schema.json"
DATASET_PATH = Path(__file__).parent / "resources" / "dynamic_query_dataset.json"
PROMPT_PATH = Path(__file__).parent / "prompt.md"
PROMPT_RAG_PATH = Path(__file__).parent / "prompt_rag.md"
PROMPT_SYSTEM_PATH = Path(__file__).parent / "prompt_system.md"
PROMPT_RESPONSE_FORMAT_PATH = Path(__file__).parent / "prompt_response_format.md"
PROMPT_RESPONSE_VALIDATOR_PATH = Path(__file__).parent / "prompt_response_validator.md"


class DynamicQueryAgent:
    def __init__(self, bo_json_schemas: dict | None = None):
        self.model = ChatVertexAI(
            model_name="gemini-2.0-flash",
            project=AUTH_PROPERTIES.GOOGLE_CLOUD_PROJECT,
            location=AUTH_PROPERTIES.GOOGLE_CLOUD_LOCATION,
            temperature=0,
        )
        self.bo_json_schemas = bo_json_schemas

    def _get_bo_json_schema(self) -> str:
        """Get the json schema for the bo schemas"""
        if self.bo_json_schemas is None:
            logger.info("No BO JSON schemas provided, using default schema")
            with open(BO_SCHEMA_PATH, "r") as f:
                return json.dumps(json.load(f), indent=2)
        else:
            return json.dumps(self.bo_json_schemas, indent=2)

    def get_bo_schemas(self) -> str:
        """Get all the BO schemas"""
        return self._get_bo_json_schema()

    def load_example_dataset(self) -> str:
        """Load the example dataset from the resources directory"""
        with open(DATASET_PATH, "r") as f:
            return json.dumps(json.load(f), indent=2)

    def _extract_json_from_llm_output(self, llm_output: str) -> dict:
        """Extract and validate JSON from LLM output."""
        logger = logging.getLogger(__name__)
        logger.debug(f"Raw LLM output: {llm_output}")

        try:
            # Convert AIMessage to string if needed
            if hasattr(llm_output, "content"):
                llm_output = llm_output.content

            # Try to find JSON content between triple backticks if present
            if "```json" in llm_output:
                start = llm_output.find("```json") + 7
                end = llm_output.find("```", start)
                if end != -1:
                    llm_output = llm_output[start:end]
            elif "```" in llm_output:
                start = llm_output.find("```") + 3
                end = llm_output.find("```", start)
                if end != -1:
                    llm_output = llm_output[start:end]

            # Clean and format the JSON string
            llm_output = llm_output.strip()

            # Only handle unquoted keys
            llm_output = re.sub(
                r"([{,])\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*:", r'\1"\2":', llm_output
            )
            return json.loads(llm_output)
        except json.JSONDecodeError as e:
            logger.error(f"JSON decode error: {e}")
            logger.error(f"Failed JSON string: {llm_output}")
            return {}
        except Exception as e:
            logger.error(f"Unexpected error: {e}")
            return {}

    def get_minimal_bo_json_schema(self, question: str) -> str:
        """Get the minimal json schema for the bo schemas based on the question.

        Args:
            question: The user's question to analyze

        Returns:
            A minimal JSON schema string containing semantically relevant fields
        """
        # Get the full schema
        schema = self._get_bo_json_schema()
        prompt = self._get_rag_prompt(schema, question)

        # Get minimal schema from LLM
        llm_output = self.model.invoke(prompt)

        # Extract and validate the minimal schema
        minimal_schema = self._extract_json_from_llm_output(llm_output)

        # Ensure we have a valid schema
        if not minimal_schema:
            return (
                self._get_bo_json_schema()
            )  # Fall back to full schema if minimization fails

        return json.dumps(minimal_schema, indent=2)

    def _get_rag_prompt(self, schema: str, question: str) -> str:
        with open(PROMPT_RAG_PATH, "r") as f:
            prompt_template = f.read()
        return prompt_template.format(schema=schema, question=question)

    def _get_system_message(self) -> str:
        with open(PROMPT_SYSTEM_PATH, "r") as f:
            prompt = f.read()
        return prompt.format(
            bo_schemas=self.get_bo_schemas(), examples=self.load_example_dataset()
        )

    def _get_response_format(self) -> str:
        # json_parser = JsonOutputParser(pydantic_object=DynamicQueryLLMResponse)
        # return json_parser.get_format_instructions()
        with open(PROMPT_RESPONSE_FORMAT_PATH, "r") as f:
            return f.read()

    def _get_user_message(self, question: str, root_bos: str = "") -> str:
        if root_bos == "":
            return f"Question: {question}."
        else:
            return f"Question: {question}. \nProvided root business objects are : '{root_bos}'"

    async def _correct_response_format_with_llm(
        self, response: str, response_format: str
    ) -> str:
        messages = [
            AIMessage(content=response),
            HumanMessage(content="Correct the response to match the following format:"),
            HumanMessage(content=response_format),
        ]
        llm_output = await self.model.ainvoke(messages)
        return llm_output.content

    def _is_empty_question(self, question: str) -> bool:
        return len(re.sub(r"^[a-zA-Z]", "", question)) == 0

    async def _correct_dynamic_query_response_format_with_llm(
        self, response: str = ""
    ) -> tuple[dict, dict]:
        """Correct the dynamic query response with LLM"""

        default_query_result = DynamicQueryLLMResponse(
            result=[{"query": {}, "explanation": "", "reasoning": ""}]
        )

        response_format = self._get_response_format()

        try:
            llm_output = await self._correct_response_format_with_llm(
                response=response, response_format=response_format
            )
            json_llm_output = self._extract_json_from_llm_output(llm_output)
            query_result = DynamicQueryLLMResponse.model_validate(json_llm_output)
            if len(query_result.result) == 0:
                query_result = default_query_result
        except Exception as e:
            logger.error(f"DynamicQueryAgent | Retry LLM response error: {e}")
            query_result = default_query_result

        return query_result

    def _get_response_validator_prompt(self) -> str:
        with open(PROMPT_RESPONSE_VALIDATOR_PATH, "r") as f:
            return f.read()

    async def _dynamic_query_response_validation_with_llm(
        self,
        response: DynamicQueryLLMResponse = None,
        question: str = "",
        root_bos: str = "",
    ) -> DynamicQueryLLMResponse:
        """Validate and correct the dynamic query response with LLM"""

        if not (
            hasattr(response, "result")
            and response.result
            and len(response.result) > 0
            and response.result[0].query
            and response.result[0].query != {}
        ):
            return response

        try:
            messages = [
                SystemMessage(content=self._get_system_message()),
                # HumanMessage(content=self._get_user_message(question, root_bos)),
                SystemMessage(content=self._get_response_format()),
                AIMessage(content=response.model_dump_json()),
                HumanMessage(content=self._get_response_validator_prompt()),
            ]

            llm_output = await self.model.ainvoke(messages)
            json_llm_output = self._extract_json_from_llm_output(llm_output.content)
            query_result = DynamicQueryLLMResponse.model_validate(json_llm_output)
            if len(query_result.result) == 0:
                query_result = response
        except Exception as e:
            logger.error(f"DynamicQueryAgent | validation LLM response error: {e}")
            query_result = response

        return query_result

    async def _dynamic_query_followup_question_with_llm(
        self,
        response: DynamicQueryLLMResponse = None,
        followup_question: str = "",
        history: list[BaseMessage] = [],
    ) -> DynamicQueryLLMResponse:
        """Ask followup question to the LLM"""

        if self._is_empty_question(followup_question):
            return response

        try:
            llm_output = await self.model.ainvoke(history)
            json_llm_output = self._extract_json_from_llm_output(llm_output.content)
            query_result = DynamicQueryLLMResponse.model_validate(json_llm_output)
            if len(query_result.result) == 0:
                query_result = response
        except Exception as e:
            logger.error(f"DynamicQueryAgent | validation LLM response error: {e}")
            query_result = response

        return query_result

    async def get_dynamic_query(
        self, question: str, root_bos: str = ""
    ) -> tuple[dict, dict]:
        """Get the dynamic query for a given question with structured output"""

        system_message = self._get_system_message()
        response_format = self._get_response_format()
        user_message = self._get_user_message(question, root_bos)

        if self._is_empty_question(question):
            query_result = DynamicQueryLLMResponse(
                result=[{"query": {}, "explanation": "", "reasoning": ""}]
            )
            return query_result.model_dump(), {}

        messages = [
            SystemMessage(content=system_message),
            HumanMessage(content=user_message),
            HumanMessage(content=response_format),
        ]

        try:
            llm_output = await self.model.ainvoke(messages)
            json_llm_output = self._extract_json_from_llm_output(llm_output)
            query_result = DynamicQueryLLMResponse.model_validate(json_llm_output)

            logger.info(
                f"DynamicQueryAgent | Query result after first LLM response: {query_result}"
            )
            if len(query_result.result) == 0:
                query_result = (
                    await self._correct_dynamic_query_response_format_with_llm(
                        response=llm_output.content
                    )
                )
                logger.info(
                    f"DynamicQueryAgent | Query result after query json format correction: {query_result}"
                )
        except Exception as e:
            logger.error(f"DynamicQueryAgent | LLM response error: {e}")
            query_result = DynamicQueryLLMResponse(
                result=[{"query": {}, "explanation": "", "reasoning": ""}]
            )

        # validate the query result with LLM
        # query_result = await self._dynamic_query_response_validation_with_llm(
        #     response=query_result, question=question, root_bos=root_bos
        # )

        # Check if the provided root business object is present in the "Business Object Schema".
        instructions = """
        Check if the provided root business object (if non empty) is present in the "Business Object Schema".
        If yes, return the query as is.
        If not return the empty query. 
        """
        messages = [
            SystemMessage(content=system_message),
            HumanMessage(content=user_message),
            HumanMessage(content=response_format),
            AIMessage(content=query_result.model_dump_json()),
            HumanMessage(content=instructions),
        ]
        query_result = await self._dynamic_query_followup_question_with_llm(
            response=query_result,
            followup_question=question,
            history=messages,
        )

        query_result = query_result.model_dump()

        logger.info(
            f"DynamicQueryAgent | Query result after root bo correction: {query_result}"
        )

        # Extract token usage from usage_metadata in the response
        token_usage = llm_output.usage_metadata

        if not token_usage:  # If not in usage_metadata, try response_metadata
            token_usage = llm_output.response_metadata.get("usage_metadata", {})
            token_usage = {
                "input_tokens": token_usage.get("prompt_token_count", 0),
                "output_tokens": token_usage.get("candidates_token_count", 0),
                "total_tokens": token_usage.get("total_token_count", 0),
            }
        logger.debug(f"DynamicQueryAgent | Token Usage: {token_usage}")

        # Debug dump
        debug_data = {}
        # if Settings.DEBUG:
        if True:
            debug_data = {
                "question": question,
                # "bo_schemas": json.loads(self.get_bo_schemas()),
                # "llm_output": (
                #     llm_output.content
                #     if hasattr(llm_output, "content")
                #     else str(llm_output)
                # ),
                "token_usage": token_usage,
                "query_result": query_result.get(
                    "result", [{"query": {}, "explanation": "", "reasoning": ""}]
                ),
            }

        logger.info(f"DynamicQueryAgent | Query result: {debug_data}")
        return query_result, debug_data

    async def run(
        self,
        question: str,
        skip_data: bool = True,
        root_bos: str = "",
        domain_name: str = "",
        domain_type: str = "",
    ) -> dict:
        """Main entry point for the agent.

        Args:
            question: User's natural language question
            skip_data: Whether to skip data retrieval
            root_bos: List of root business objects

        Returns:
            JSON query string
        """

        # Get the dynamic query
        query_result, debug_data = await self.get_dynamic_query(question, root_bos)

        # Access the query from the dictionary structure
        if not (
            "result" in query_result
            and len(query_result["result"]) > 0
            and "query" in query_result["result"][0]
            and query_result["result"][0]["query"] != {}
        ):
            return {"query": {}, "data": [], "debug_data": debug_data}

        query = query_result["result"][0]["query"]

        # Process the query
        query = DynamicQueryProcessor().process_query(query)
        if query == {}:
            return {"query": {}, "data": [], "debug_data": debug_data}

        # Execute the query
        data = (
            await DynamicQueryExecutor().run(query, domain_name, domain_type, root_bos)
            if not skip_data
            else []
        )
        if data is None:
            return {"query": query, "data": [], "debug_data": debug_data}

        return {"query": query, "data": data, "debug_data": debug_data}


async def main():
    agent = DynamicQueryAgent()

    # question1 = "get government interest rate for account id 27ddc496-eec9-11ef-9068-bf957ee9cc5b"
    # task1 = asyncio.create_task(
    #     agent.run(question1, skip_data=True, root_bos="account")
    # )
    # question2 = "get first 100 account names and ids"
    # task2 = asyncio.create_task(
    #     agent.run(question2, skip_data=True, root_bos="account")
    # )
    # question3 = "Get account details including id and account number, ordered by the primary owner's last name in descending order"
    # task3 = asyncio.create_task(
    #     agent.run(question3, skip_data=True, root_bos="account")
    # )
    # question4 = "Get account details including id and account number, with all fields of primary owner where primary owner's first name is Alice and account number is either 1234 or 5678 with balance over 1000, order results by account number in descending order"
    # task4 = asyncio.create_task(
    #     agent.run(question4, skip_data=True, root_bos="account")
    # )

    question5 = "Get all fields from match summary"
    task5 = asyncio.create_task(
        agent.run(question5, skip_data=True, root_bos="matchSummary")
    )
    # # gather all the tasks
    # results = await asyncio.wait_for(
    #     asyncio.gather(task1, task2, task3, task4, task5), timeout=60
    # )

    # print("question", question1)
    # print("query", results[0].get("query"))
    # print("question", question2)
    # print("query", results[1].get("query"))
    # print("question", question3)
    # print("query", results[2].get("query"))
    # print("question", question4)
    # print("query", results[3].get("query"))
    # print("question", question5)
    # print("query", results[4].get("query"))

    result = await task5
    print("question", question5)
    print("query", result.get("query"))


if __name__ == "__main__":
    asyncio.run(main())
