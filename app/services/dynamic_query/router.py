from fastapi import APIRouter, HTTPException, Request
from .dynamic_query_service import DynamicQueryService
from .type import DynamicQueryParams
import logging
from fastapi import Depends
from fastapi.responses import JSONResponse

logger = logging.getLogger(__name__)

router = APIRouter(tags=["Dynamic Query"])


@router.get("/dynamic-query")
async def dynamic_query(
    request: Request,
    params: DynamicQueryParams = Depends(),
):
    tenant_id = request.headers.get("X-Jiffy-Tenant-ID", None)
    app_id = request.headers.get("X-Jiffy-App-ID", None)

    if not tenant_id or not app_id:
        raise HTTPException(
            status_code=400, detail="Tenant ID and App ID are required in headers"
        )

    try:
        service = DynamicQueryService(
            tenant_id,
            app_id,
            params.query,
            params.skip_data,
            params.root_bo,
            params.domain,
            params.force_refresh_bo_definitions,
        )
        result = await service.get_dynamic_query()
        return JSONResponse(
            content=result.model_dump(),
            media_type="application/json",
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"An error occurred while generating dynamic query: {str(e)}",
        )
