from app.services.dynamic_query.utils.bo_schema_manager import BOSchemaManager
from app.services.dynamic_query.dynamic_query_agent import DynamicQueryAgent
from app.services.dynamic_query.type import DynamicQueryResponse
from pathlib import Path
import logging
import re

logger = logging.getLogger(__name__)


class DynamicQueryService:

    def __init__(
        self,
        tenant_id: str,
        app_id: str,
        query: str,
        skip_data: bool = False,
        root_bo: str = "",
        domain: str = "",
        force_refresh_bo_definitions: bool = False,
    ):
        self.tenant_id = tenant_id
        self.app_id = app_id
        self.bo_schema_manager = BOSchemaManager()
        self.query = query
        self.skip_data = skip_data
        self.root_bo = root_bo
        self.domain = domain
        self.force_refresh_bo_definitions = force_refresh_bo_definitions

    async def get_bo_json_schemas(
        self, domain_name: str, domain_type: str
    ) -> dict | None:
        try:
            logger.info(
                f"Getting BO JSON schemas for tenant_id: {self.tenant_id}, app_id: {self.app_id}, domain: {self.domain}"
            )
            bo_json_schemas = await self.bo_schema_manager.get_bo_json_schemas(
                tenant_id=self.tenant_id,
                app_id=self.app_id,
                domain_name=domain_name,
                domain_type=domain_type,
                force_refresh_bo_definitions=self.force_refresh_bo_definitions,
            )
            return bo_json_schemas
        except Exception as e:
            logger.error(f"Error getting BO JSON schemas: {e}")
            return None

    async def get_dynamic_query(self) -> DynamicQueryResponse:

        # Check query is valid
        if not self.query:
            logger.info("Query is empty, returning empty response")
            return DynamicQueryResponse()

        # Get domain details
        domain_type, domain_name = await self.bo_schema_manager.get_domain_details(
            self.domain
        )
        if domain_type == "" or domain_name == "":
            logger.error(f"Invalid domain: {self.domain}")
            raise ValueError(f"Invalid domain: {self.domain}")

        # Get BO JSON schemas
        bo_json_schemas = await self.get_bo_json_schemas(domain_name, domain_type)

        if bo_json_schemas is None:
            logger.error(f"Failed to get BO definitions for domain: {domain_name}")
            raise ValueError(f"Failed to get BO JSON schemas for domain: {domain_name}")

        # Run dynamic query
        agent = DynamicQueryAgent(bo_json_schemas=bo_json_schemas)
        result = await agent.run(
            question=self.query,
            skip_data=self.skip_data,
            root_bos=self.root_bo,
            domain_name=domain_name,
            domain_type=domain_type,
        )
        result = DynamicQueryResponse(**result)
        return result
