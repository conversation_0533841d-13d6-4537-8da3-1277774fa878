{"$schema": "http://json-schema.org/draft-07/schema#", "definitions": {"AcHCreateResponse": {"type": "object", "title": "AcHCreateResponse", "description": "ACH Create Response", "properties": {"id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "requestId": {"title": "requestId", "type": "string", "maxLength": 255, "minLength": 0}}}, "Account": {"type": "object", "title": "Account", "description": "Account", "properties": {"accountCommunicationPreferences": {"title": "Account Communication Preferences", "type": "object", "$ref": "#/definitions/AccountCommunicationPreference"}, "accountCreationDetail": {"title": "Account Creation Detail", "type": "object", "$ref": "#/definitions/AccountCreationDetail"}, "accountFeatures": {"title": "Account Features", "type": "array", "items": {"$ref": "#/definitions/AccountFeatures"}}, "annuityDetails": {"title": "Annuity Details", "type": "array", "items": {"$ref": "#/definitions/AnnuityDetails"}}, "assets": {"title": "Assets", "type": "object", "$ref": "#/definitions/Assets"}, "associates": {"title": "Associates", "type": "array", "items": {"$ref": "#/definitions/AccountAssociate"}}, "beneficiaries": {"title": "Beneficiaries", "type": "array", "items": {"$ref": "#/definitions/Beneficiary"}}, "branch": {"title": "Branch", "type": "object", "$ref": "#/definitions/Branch"}, "cashFlow": {"title": "Cash Flow", "type": "string"}, "client": {"title": "Client", "type": "object", "$ref": "#/definitions/Client"}, "collateral": {"title": "Collateral", "type": "array", "items": {"$ref": "#/definitions/Collateral"}}, "contingentBeneficiaries": {"title": "Contingent Beneficiaries", "type": "array", "items": {"$ref": "#/definitions/Beneficiary"}}, "custodian": {"title": "<PERSON><PERSON><PERSON><PERSON>", "type": "object", "$ref": "#/definitions/Custodian"}, "custodianForMinor": {"title": "<PERSON><PERSON><PERSON><PERSON> for Minor", "type": "object", "$ref": "#/definitions/AccountInterestedParties"}, "dailyBalances": {"title": "Daily Balances", "type": "object", "$ref": "#/definitions/AccountBalances"}, "documents": {"title": "Documents", "type": "array", "items": {"$ref": "#/definitions/Document"}}, "employerContact": {"title": "Employer Contact", "type": "object", "$ref": "#/definitions/AccountInterestedParties"}, "feeSchedule": {"title": "Fee Schedule", "type": "object", "$ref": "#/definitions/FeeSchedule"}, "governingStateLawForCustodialAccount": {"title": "Governing State Law for Custodial Account", "type": "object", "$ref": "#/definitions/StateOrProvince"}, "holdings": {"title": "Holdings", "type": "object", "$ref": "#/definitions/Holdings"}, "interestedParties": {"title": "Interested Parties", "type": "array", "items": {"$ref": "#/definitions/AccountInterestedParties"}}, "intraDayBalances": {"title": "Intra day balances", "type": "object", "$ref": "#/definitions/AccountBalances"}, "irADistributionInstructions": {"title": "IRA Distribution Instructions", "type": "object", "$ref": "#/definitions/Transfer"}, "irAFinancialInstitution": {"title": "IRA Financial Institution", "type": "object", "$ref": "#/definitions/FinancialInstitution"}, "jointTenancyState": {"title": "Joint Tenancy State", "type": "object", "$ref": "#/definitions/StateOrProvince"}, "liabilities": {"title": "Liabilities", "type": "object", "$ref": "#/definitions/Liabilities"}, "marginRates": {"title": "Margin Rates", "type": "array", "items": {"$ref": "#/definitions/MarginRates"}}, "notices": {"title": "Notices", "type": "array", "items": {"$ref": "#/definitions/AccountNotice"}}, "originalDepositor": {"title": "Original Depositor", "type": "object", "$ref": "#/definitions/AccountInterestedParties"}, "ouLevel0": {"title": "OU Level 0", "type": "object", "$ref": "#/definitions/OrganizationalUnit"}, "ouLevel1": {"title": "OU Level 1", "type": "object", "$ref": "#/definitions/OrganizationalUnit"}, "ouLevel2": {"title": "OU Level 2", "type": "object", "$ref": "#/definitions/OrganizationalUnit"}, "ouLevel3": {"title": "OU Level 3", "type": "object", "$ref": "#/definitions/OrganizationalUnit"}, "ouLevel4": {"title": "OU Level 4", "type": "object", "$ref": "#/definitions/OrganizationalUnit"}, "ouLevel5": {"title": "OU Level 5", "type": "object", "$ref": "#/definitions/OrganizationalUnit"}, "ouLevel6": {"title": "OU Level 6", "type": "object", "$ref": "#/definitions/OrganizationalUnit"}, "positions": {"title": "Positions", "type": "string"}, "precedingOwner": {"title": "Preceding Owner", "type": "object", "$ref": "#/definitions/AccountInterestedParties"}, "primaryOwner": {"title": "Primary Owner", "type": "object", "$ref": "#/definitions/AccountOwnership"}, "product": {"title": "Product", "type": "object", "$ref": "#/definitions/Product"}, "registrationType": {"title": "Registration Type", "type": "object", "$ref": "#/definitions/RegistrationType"}, "regulatoryDisclosures": {"title": "Regulatory Disclosures", "type": "array", "items": {"$ref": "#/definitions/RegulatoryDisclosure"}}, "relatedAccounts": {"title": "Related Accounts", "type": "array", "items": {"$ref": "#/definitions/AccountRelation"}}, "repCodeLink": {"title": "Rep Code Link", "type": "object", "$ref": "#/definitions/RepCode"}, "responsibleIndividualForMaintainingStirp": {"title": "Responsible individual for maintaining Stirpes", "type": "object", "$ref": "#/definitions/AccountInterestedParties"}, "retirementPlanSumamry": {"title": "Retirement Plan Summary", "type": "string"}, "secondaryOwners": {"title": "Secondary Owners", "type": "array", "items": {"$ref": "#/definitions/AccountOwnership"}}, "statementSchedule": {"title": "Statement Schedule", "type": "array", "items": {"$ref": "#/definitions/AccountStatementSchedule"}}, "taxLots": {"title": "TaxLots", "type": "string"}, "transfers": {"title": "Transfers", "type": "array", "items": {"$ref": "#/definitions/Transfer"}}, "accountCustodianStatus": {"title": "Account <PERSON><PERSON><PERSON><PERSON>", "type": "string", "maxLength": 255, "minLength": 0}, "accountIsSetUpForEDelivery": {"title": "Account is set up for eDelivery", "type": "boolean"}, "accountManagementType": {"title": "Account Management Type", "type": "string", "maxLength": 255, "minLength": 0}, "accountNumber": {"title": "Account Number", "type": "string", "maxLength": 255, "minLength": 0}, "accountPrefix": {"title": "Account Prefix", "type": "string", "maxLength": 255, "minLength": 0}, "accountStatus": {"title": "Account Status", "type": "string", "maxLength": 255, "minLength": 0}, "acknowledgeIlliquidInvestment": {"title": "Acknowledge Illiquid Investment", "type": "boolean"}, "advisorTradingDiscretion": {"title": "Advisor Trading Discretion", "type": "string", "maxLength": 255, "minLength": 0}, "ageOfTerminationOfCustody": {"title": "Age of Termination of Custody", "type": "integer"}, "allowedWithdrawalPercentage": {"title": "Allowed Withdrawal Percentage", "type": "number", "maximum": 100.0, "minimum": 0.0}, "allowedYear1WithdrawalPercentage": {"title": "Allowed Year 1 Withdrawal Percentage", "type": "number", "maximum": 100.0, "minimum": 0.0}, "allowsSpeculation": {"title": "Allows Speculation", "type": "boolean"}, "annualExpenses": {"title": "Annual Expenses", "type": "number"}, "annualIncome": {"title": "Annual Income", "type": "string", "maxLength": 30, "minLength": 0}, "annualIncomeExact": {"title": "Annual Income (exact)", "type": "number"}, "availableCredit": {"title": "Available Credit", "type": "number"}, "balance": {"title": "Balance", "type": "number"}, "balanceAsOfDate": {"title": "Balance as of Date", "type": "string", "format": "date"}, "branchOrOfficeID": {"title": "Branch or Office ID", "type": "string", "maxLength": 255, "minLength": 0}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "cashDividendOption": {"title": "Cash Dividend Option", "type": "string", "maxLength": 40, "minLength": 0}, "chargeInterestOnWithdrawal": {"title": "Charge Interest on Withdrawal", "type": "boolean"}, "closureDate": {"title": "Closure Date", "type": "string", "format": "date"}, "createdAt": {"title": "Created At", "type": "string", "format": "date-time"}, "createdBy": {"title": "Created By", "type": "string", "maxLength": 255, "minLength": 0}, "custodialAccountType": {"title": "Custodial Account Type", "type": "string", "maxLength": 255, "minLength": 0}, "dayOfMonthPaymentIsDue": {"title": "Day of month payment is due", "type": "string", "maxLength": 255, "minLength": 0}, "dividendReinvestmentOption": {"title": "Dividend Reinvestment Option", "type": "string", "maxLength": 255, "minLength": 0}, "employeeAffiliationType": {"title": "Employee Affiliation Type", "type": "string", "maxLength": 255, "minLength": 0}, "employerPlanAccountNumber": {"title": "Employer Plan Account Number", "type": "string", "maxLength": 255, "minLength": 0}, "estimatedValueOfInvestments": {"title": "Estimated value of investments", "type": "string", "maxLength": 30, "minLength": 0}, "federalMarginalTaxRate": {"title": "Federal Marginal Tax Rate", "type": "string", "maxLength": 30, "minLength": 0}, "financialInformationIsForEntireHousehold": {"title": "Financial information is for entire household", "type": "boolean"}, "firmID": {"title": "Firm ID", "type": "string", "maxLength": 255, "minLength": 0}, "frequency": {"title": "Frequency", "type": "string", "maxLength": 255, "minLength": 0}, "guaranteedYield": {"title": "<PERSON><PERSON><PERSON><PERSON>", "type": "number", "maximum": 100.0, "minimum": 0.0}, "hsAContributionLimits": {"title": "HSA Contribution Limits", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "includePrimeBrokerage": {"title": "Include Prime Brokerage", "type": "boolean"}, "inheritedIRADistributionOption": {"title": "Inherited IRA Distribution Option", "type": "string", "maxLength": 255, "minLength": 0}, "initialFundingSource": {"title": "Initial Funding Source", "type": "string", "maxLength": 255, "minLength": 0}, "initialFundingSources": {"title": "Initial Funding Sources", "type": "string", "maxLength": 255, "minLength": 0}, "interestAccrualInterval": {"title": "Interest Accrual Interval", "type": "integer"}, "interestAccrualMethod": {"title": "Interest Accrual Method", "type": "string", "maxLength": 255, "minLength": 0}, "interestAccrualMinimumBalance": {"title": "Interest Accrual Minimum Balance", "type": "integer"}, "interestAccrualMinimumBalanceAccountingM": {"title": "Interest Accrual Minimum Balance Accounting Method", "type": "string", "maxLength": 255, "minLength": 0}, "interestAccrualSchedule": {"title": "Interest Accrual Schedule", "type": "string", "maxLength": 255, "minLength": 0}, "interestOnWithdrawalIsCumulative": {"title": "Interest on Withdrawal is Cumulative", "type": "boolean"}, "interestRate": {"title": "Interest Rate", "type": "number", "maximum": 100.0, "minimum": 0.0}, "interestRateBonus": {"title": "Interest Rate Bonus", "type": "number", "maximum": 100.0, "minimum": 0.0}, "interestRateID": {"title": "Interest Rate ID", "type": "string", "maxLength": 255, "minLength": 0}, "investmentExperience": {"title": "Investment Experience", "type": "string", "maxLength": 255, "minLength": 0}, "investmentObjective": {"title": "Investment Objective", "type": "string", "maxLength": 255, "minLength": 0}, "irABeneficiaryType": {"title": "IRA Beneficiary Type", "type": "string", "maxLength": 255, "minLength": 0}, "irAContributionType": {"title": "IRA Contribution Type", "type": "string", "maxLength": 255, "minLength": 0}, "irADirectBeneficiaryIsSpouse": {"title": "IRA: Direct Beneficiary is Spouse", "type": "boolean"}, "irADirectBeneficiaryOrSuccessor": {"title": "IRA: Direct Beneficiary or Successor", "type": "string", "maxLength": 255, "minLength": 0}, "irAEligibleDesignatedBeneficiaryReason": {"title": "IRA Eligible Designated Beneficiary Reason", "type": "string", "maxLength": 255, "minLength": 0}, "irAExistingAccountFees": {"title": "IRA Existing Account <PERSON>", "type": "string", "maxLength": 255, "minLength": 0}, "irAFundingSource": {"title": "IRA Funding Source", "type": "string", "maxLength": 255, "minLength": 0}, "irAOriginalAccountConversionDate": {"title": "IRA Original Account Conversion Date", "type": "string", "format": "date"}, "irAOriginalAccountMostRecentStatement": {"title": "IRA Original Account Most Recent Statement", "type": "string", "maxLength": 2096}, "irAOriginalAccountNumber": {"title": "IRA Original Account Number", "type": "string", "maxLength": 255, "minLength": 0}, "irAOriginalAccountType": {"title": "IRA Original Account Type", "type": "string", "maxLength": 255, "minLength": 0}, "irARecommendedAccountFees": {"title": "IRA Recommended Account <PERSON>", "type": "string", "maxLength": 255, "minLength": 0}, "irARolloverComments": {"title": "IRA Rollover Comments", "type": "string", "maxLength": 2000, "minLength": 0}, "irARolloverDecision": {"title": "IRA Rollover Decision", "type": "string", "maxLength": 255, "minLength": 0}, "irARolloverInvestments": {"title": "IRA Rollover Investments", "type": "string", "maxLength": 255, "minLength": 0}, "irARolloverRationale": {"title": "IRA Rollover Rationale", "type": "string", "maxLength": 255, "minLength": 0}, "irARolloverServices": {"title": "IRA Rollover Services", "type": "string", "maxLength": 255, "minLength": 0}, "irATypeOfOwnership": {"title": "IRA Type of Ownership", "type": "string", "maxLength": 255, "minLength": 0}, "isAccountForAForeignBank": {"title": "Is Account For a Foreign Bank", "type": "boolean"}, "isAccountForPrivateBanking": {"title": "Is Account For Private Banking", "type": "boolean"}, "isEmployeeAccount": {"title": "Is Employee Account", "type": "boolean"}, "isExternal": {"title": "Is External", "type": "boolean"}, "isForeign": {"title": "Is Foreign", "type": "boolean"}, "isInstitutionalAccount": {"title": "Is Institutional Account", "type": "boolean"}, "isManaged": {"title": "Is Managed", "type": "boolean"}, "isMasterAccount": {"title": "Is Master Account", "type": "boolean"}, "lastModifiedAt": {"title": "Last Modified At", "type": "string", "format": "date-time"}, "lastPaymentAmount": {"title": "Last Payment Amount", "type": "number"}, "lastPaymentDate": {"title": "Last Payment Date", "type": "string", "format": "date"}, "lastStatementDate": {"title": "Last Statement Date", "type": "string", "format": "date"}, "liquidAssets": {"title": "Liquid Assets", "type": "string", "maxLength": 30, "minLength": 0}, "liquidAssetsExact": {"title": "Liquid Assets (exact)", "type": "number"}, "liquidityNeeds": {"title": "Liquidity Needs", "type": "string", "maxLength": 255, "minLength": 0}, "liquidityPreference": {"title": "Liquidity Preference", "type": "string", "maxLength": 255, "minLength": 0}, "liquiditySufficientForAYear": {"title": "Liquidity Sufficient for a Year", "type": "boolean"}, "marginInterestDelta": {"title": "Margin Interest Delta", "type": "number", "multipleOf": 0.001, "maximum": 100.0, "minimum": -100.0}, "marginInterestMaximum": {"title": "Margin Interest Maximum", "type": "number", "maximum": 100.0, "minimum": 0.0}, "marginInterestMinimum": {"title": "Margin Interest Minimum", "type": "number", "maximum": 100.0, "minimum": 0.0}, "marginInterestRate": {"title": "Margin Interest Rate", "type": "number", "maximum": 100.0, "minimum": 0.0}, "marginNote": {"title": "Margin Note", "type": "string", "maxLength": 255, "minLength": 0}, "maturityDate": {"title": "Maturity Date", "type": "string", "format": "date"}, "moneyFundSweepOptIn": {"title": "Money Fund Sweep opt-in", "type": "boolean"}, "name": {"title": "Name", "type": "string", "maxLength": 255, "minLength": 0}, "netWorthExcludingHome": {"title": "Net Worth excluding Home", "type": "string", "maxLength": 30, "minLength": 0}, "netWorthExcludingHomeExact": {"title": "Net Worth excluding Home (exact)", "type": "number"}, "nextAmountDue": {"title": "Next Amount Due", "type": "number"}, "nextDueDate": {"title": "Next Due Date", "type": "string", "format": "date"}, "nextMinimumAmountDue": {"title": "Next Minimum Amount Due", "type": "number"}, "nickName": {"title": "<PERSON>", "type": "string", "maxLength": 255, "minLength": 0}, "optionsRiskLevel": {"title": "Options Risk Level", "type": "string", "maxLength": 255, "minLength": 0}, "otherInitialFundingSource": {"title": "Other Initial Funding Source", "type": "string", "maxLength": 255, "minLength": 0}, "premium": {"title": "Premium", "type": "number"}, "primaryOwnerName": {"title": "Primary Owner Name", "type": "string"}, "rateGuaranteedYears": {"title": "Rate Guaranteed Years", "type": "integer"}, "recordSource": {"title": "Record Source", "type": "string", "maxLength": 255, "minLength": 0}, "recordSourceApplication": {"title": "Record Source Application", "type": "string", "maxLength": 255, "minLength": 0}, "reinvestDividendsFromAllEligibleSecuriti": {"title": "Reinvest Dividends from all eligible securities", "type": "boolean"}, "reinvestDividendsFromMutualFunds": {"title": "Reinvest Dividends from Mutual Funds", "type": "boolean"}, "reinvestDividendsFromNone": {"title": "Reinvest Dividends from none", "type": "boolean"}, "reinvestDividendsFromSecuritiesOnly": {"title": "Reinvest Dividends from Securities Only", "type": "boolean"}, "repCode": {"title": "Rep Code", "type": "string", "maxLength": 255, "minLength": 0}, "riskTolerance": {"title": "Risk Tolerance", "type": "string", "maxLength": 255, "minLength": 0}, "saleCostBasisMethod": {"title": "Sale Cost Basis Method", "type": "string", "maxLength": 255, "minLength": 0}, "salesProceedsDistribution": {"title": "Sales Proceeds Distribution", "type": "string", "maxLength": 255, "minLength": 0}, "sePIRAContributor": {"title": "SEP IRA: Contributor", "type": "string", "maxLength": 255, "minLength": 0}, "sePIRAEmployeeMinimumAge": {"title": "SEP IRA: Employee minimum age", "type": "integer"}, "sePIRAEmployeeMinimumEmploymentYears": {"title": "SEP IRA: Employee minimum employment years", "type": "integer"}, "sePIRAIncludeCertainNonResidentAliens": {"title": "SEP IRA: Include certain non-resident aliens", "type": "boolean"}, "sePIRAIncludeCollectiveBargaining": {"title": "SEP IRA: Include collective bargaining", "type": "boolean"}, "sePIRAIncludeEmployeesUnder450": {"title": "SEP IRA: Include employees under $450", "type": "boolean"}, "securityRestrictions": {"title": "Security Restrictions", "type": "string"}, "shareOwnerInformationWithOwnedCorporatio": {"title": "Share owner information with owned corporations", "type": "boolean"}, "siMPLEIRAAdditionalSalaryReductionElecti": {"title": "SIMPLE IRA: Additional Salary reduction elections available", "type": "boolean"}, "siMPLEIRACurrentMinimumCompensation": {"title": "SIMPLE IRA: Current minimum compensation", "type": "number"}, "siMPLEIRAEmployeeMayTerminateSalaryReduc": {"title": "SIMPLE IRA: Employee may terminate Salary reduction", "type": "boolean"}, "siMPLEIRAEmployerContributionPercentage": {"title": "SIMPLE IRA: Employer contribution percentage", "type": "number", "maximum": 100.0, "minimum": 0.0}, "siMPLEIRAEmployerContributionYear": {"title": "SIMPLE IRA: Employer contribution year", "type": "string", "maxLength": 4, "minLength": 0, "pattern": "\\d{4}"}, "siMPLEIRAEmployerContributions": {"title": "SIMPLE IRA: Employer contributions", "type": "string", "maxLength": 255, "minLength": 0}, "siMPLEIRAEmployerNonElectiveContribution": {"title": "SIMPLE IRA: Employer non-elective contribution minimum compensation", "type": "number"}, "siMPLEIRAExcludeEmployeesCoveredByCollec": {"title": "SIMPLE IRA: Exclude employees covered by collective bargaining agreement", "type": "boolean"}, "siMPLEIRAGeneralEligibilityRequirement": {"title": "SIMPLE IRA: General eligibility requirement", "type": "string", "maxLength": 255, "minLength": 0}, "siMPLEIRAPriorCompensationYears": {"title": "SIMPLE IRA: Prior compensation years", "type": "string", "maxLength": 255, "minLength": 0}, "siMPLEIRAPriorMinimumCompensation": {"title": "SIMPLE IRA: Prior minimum compensation", "type": "number"}, "siMPLEIRASalaryReductionAgreementReceM7iqk": {"title": "SIMPLE IRA: Salary Reduction agreement receive by date", "type": "string", "format": "date"}, "siMPLEIRASalaryReductionAgreementReceive": {"title": "SIMPLE IRA: Salary Reduction agreement receiver", "type": "string", "maxLength": 255, "minLength": 0}, "siMPLEIRASalaryReductionAmount": {"title": "SIMPLE IRA: Salary reduction amount", "type": "number"}, "siMPLEIRASalaryReductionAmountChoice": {"title": "SIMPLE IRA: Salary reduction amount choice", "type": "string", "maxLength": 255, "minLength": 0}, "siMPLEIRASalaryReductionElectionFrequenc": {"title": "SIMPLE IRA: Salary reduction election frequency", "type": "string", "maxLength": 255, "minLength": 0}, "siMPLEIRASalaryReductionPercentage": {"title": "SIMPLE IRA: Salary reduction percentage", "type": "number", "maximum": 100.0, "minimum": 0.0}, "siMPLEIRASalaryReductionStartDate": {"title": "SIMPLE IRA: Salary reduction start date", "type": "string", "format": "date"}, "specialExpenses": {"title": "Special Expenses", "type": "number"}, "specialExpensesTimeframe": {"title": "Special Expenses Timeframe", "type": "string", "maxLength": 255, "minLength": 0}, "startDate": {"title": "Start Date", "type": "string", "format": "date"}, "subType": {"title": "Sub Type", "type": "string", "maxLength": 255, "minLength": 0}, "suitabilityLetterDate": {"title": "Suitability Letter Date", "type": "string", "format": "date"}, "termInMonths": {"title": "Term in Months", "type": "integer"}, "timeHorizon": {"title": "Time Horizon", "type": "string", "maxLength": 30, "minLength": 0}, "totalAssets": {"title": "Total Assets", "type": "number"}, "totalLiabilities": {"title": "Total Liabilities", "type": "number"}, "tradingPrivilege": {"title": "Trading Privilege", "type": "string", "maxLength": 255, "minLength": 0}, "tradingPrivileges": {"title": "Trading Privileges", "type": "string", "maxLength": 255, "minLength": 0}, "type": {"title": "Type", "type": "string", "maxLength": 255, "minLength": 0}, "wantBeneficiaries": {"title": "Want Beneficiaries", "type": "boolean"}}}, "AccountActivitiesResponse": {"type": "object", "title": "AccountActivitiesResponse", "description": "Account Activities Response", "properties": {"accountNumber": {"title": "accountNumber", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "insertTime": {"title": "insertTime", "type": "string", "format": "date-time"}, "messages": {"title": "messages", "type": "string", "maxLength": 2000, "minLength": 0}, "requestId": {"title": "requestId", "type": "string", "maxLength": 255, "minLength": 0}, "sequence": {"title": "sequence", "type": "integer"}, "status": {"title": "status", "type": "string", "maxLength": 255, "minLength": 0}}}, "AccountAssociate": {"type": "object", "title": "AccountAssociate", "description": "Account Associate", "properties": {"associate": {"title": "Associate", "type": "object", "$ref": "#/definitions/Associate"}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "createdAt": {"title": "Created At", "type": "string", "format": "date-time"}, "createdBy": {"title": "Created By", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "lastModifiedAt": {"title": "Last Modified At", "type": "string", "format": "date-time"}, "lastModifiedBy": {"title": "Last Modified By", "type": "string", "maxLength": 255, "minLength": 0}, "relationshipCode": {"title": "Relationship Code", "type": "string", "maxLength": 255, "minLength": 0}, "relationshipType": {"title": "Relationship Type", "type": "string", "maxLength": 255, "minLength": 0}}}, "AccountBalances": {"type": "object", "title": "AccountBalances", "description": "Account Balances", "properties": {"account": {"title": "Account", "type": "object", "$ref": "#/definitions/Account"}, "cashAccountSymbol": {"title": "Cash Account Symbol", "type": "object", "$ref": "#/definitions/Security"}, "accruedDividends": {"title": "Accrued Dividends", "type": "number"}, "accruedInterestPurchases": {"title": "Accrued Interest Purchases", "type": "number"}, "accruedInterestSales": {"title": "Accrued Interest Sales", "type": "number"}, "accumulatedFedCall": {"title": "Accumulated Fed Call", "type": "number"}, "availableFundsToWithdraw": {"title": "Available funds to withdraw", "type": "number"}, "beginningBalance": {"title": "Beginning Balance", "type": "number"}, "beginningBuyingPower": {"title": "Beginning Buying Power", "type": "number"}, "beginningCashBalance": {"title": "Beginning Cash Balance", "type": "number"}, "beginningMarginBalance": {"title": "Beginning Margin Balance", "type": "number"}, "beginningMarketValue": {"title": "Beginning Market Value", "type": "number"}, "beginningMoneyMarketBalance": {"title": "Beginning Money Market Balance", "type": "number"}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "cashAccountCashAvailable": {"title": "Cash account cash available", "type": "number"}, "cashAccountMarginValue": {"title": "Cash account margin value", "type": "number"}, "cashAccountMarketValue": {"title": "Cash account market value", "type": "number"}, "cashManagementDDANumber": {"title": "Cash Management DDA number", "type": "string", "maxLength": 255, "minLength": 0}, "commission": {"title": "Commission", "type": "number"}, "contributions": {"title": "Contributions", "type": "number"}, "corporateInterest": {"title": "Corporate Interest", "type": "number"}, "creditInterest": {"title": "Credit Interest", "type": "number"}, "dayTradeBuyingPower": {"title": "Day-trade Buying Power", "type": "number"}, "endingBalance": {"title": "Ending Balance", "type": "number"}, "endingBuyingPower": {"title": "Ending Buying Power", "type": "number"}, "endingCashBalance": {"title": "Ending Cash Balance", "type": "number"}, "endingMarginBalance": {"title": "Ending <PERSON><PERSON>", "type": "number"}, "endingMarketValue": {"title": "Ending Market Value", "type": "number"}, "endingMoneyMarketBalance": {"title": "Ending Money Market Balance", "type": "number"}, "fedCall": {"title": "Fed Call", "type": "number"}, "fundsFrozenForChecks": {"title": "Funds Frozen for Checks", "type": "number"}, "governmentInterest": {"title": "Government Interest", "type": "number"}, "houseCall": {"title": "House Call", "type": "number"}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "intraDayTimestamp": {"title": "IntraDay Timestamp", "type": "string", "format": "date-time"}, "liquidationValue": {"title": "Liquidation Value", "type": "number"}, "longMarketValue": {"title": "Long market value", "type": "number"}, "longTermCapitalGains": {"title": "LongTerm Capital Gains", "type": "number"}, "maintenanceCall": {"title": "Maintenance Call", "type": "number"}, "marginAccountCashAvailable": {"title": "Margin account cash available", "type": "number"}, "marginEquityAmount": {"title": "Margin Equity Amount", "type": "number"}, "marginEquityPercent": {"title": "Margin Equity Percent", "type": "number", "maximum": 100.0, "minimum": 0.0}, "marketAppreciation": {"title": "Market Appreciation", "type": "number"}, "miscellaneousCreditOrDebit": {"title": "Miscellaneous Credit or Debit", "type": "number"}, "moneyMarketInterest": {"title": "Money Market Interest", "type": "number"}, "municipalInterestTax": {"title": "Municipal Interest Tax", "type": "number"}, "nonQualifiedDividends": {"title": "Non-Qualified Dividends", "type": "number"}, "otherIncome": {"title": "Other Income", "type": "number"}, "partnershipDistributions": {"title": "Partnership Distributions", "type": "number"}, "periodEndDate": {"title": "Period End Date", "type": "string", "format": "date"}, "periodStartDate": {"title": "Period Start Date", "type": "string", "format": "date"}, "periodType": {"title": "Period Type", "type": "string", "maxLength": 255, "minLength": 0}, "previousAuthorizationLimit": {"title": "Previous Authorization Limit", "type": "number"}, "principalPayments": {"title": "Principal Payments", "type": "number"}, "qualifiedDividends": {"title": "Qualified Dividends", "type": "number"}, "recentDeposits": {"title": "Recent Deposits", "type": "number"}, "recordSource": {"title": "Record Source", "type": "string", "maxLength": 255, "minLength": 0}, "regulationTBuyingPower": {"title": "Regulation-T Buying Power", "type": "number"}, "repurchaseInterest": {"title": "Repurchase Interest", "type": "number"}, "returnOfCapital": {"title": "Return of Capital", "type": "number"}, "royaltyPayments": {"title": "Royalty Payments", "type": "number"}, "settlementDateBalance": {"title": "Settlement date balance", "type": "number"}, "settlementDateCashBalance": {"title": "Settlement Date Cash Balance", "type": "number"}, "settlementDateFeeBalance": {"title": "Settlement Date fee balance", "type": "number"}, "settlementDateMarginBalance": {"title": "Settlement Date Margin Balance", "type": "number"}, "settlementDateShortBalance": {"title": "Settlement Date Short Balance", "type": "number"}, "shortMarketValue": {"title": "Short market value", "type": "number"}, "shortTermCapitalGains": {"title": "Short Term Capital Gains", "type": "number"}, "smABalance": {"title": "SMA balance", "type": "number"}, "substitutePayments": {"title": "Substitute Payments", "type": "number"}, "tradeDateBalance": {"title": "Trade date balance", "type": "number"}, "tradeDateCashBalance": {"title": "Trade Date Cash Balance", "type": "number"}, "tradeDateMarginBalance": {"title": "Trade Date Margin Balance", "type": "number"}, "tradeDateShortBalance": {"title": "Trade Date Short Balance", "type": "number"}, "withdrawals": {"title": "<PERSON><PERSON><PERSON><PERSON>", "type": "number"}}}, "AccountBuilder": {"type": "object", "title": "AccountBuilder", "description": "Account Builder", "properties": {"accountType": {"title": "Account Type", "type": "object", "$ref": "#/definitions/Category"}, "documents": {"title": "Documents", "type": "array", "items": {"$ref": "#/definitions/Document"}}, "primaryOwner": {"title": "Primary Owner", "type": "object", "$ref": "#/definitions/Person"}, "productCategory": {"title": "Product Category", "type": "object", "$ref": "#/definitions/ProductCategory"}, "registrationType": {"title": "Registration Type", "type": "object", "$ref": "#/definitions/RegistrationType"}, "repCode": {"title": "Rep Code", "type": "object", "$ref": "#/definitions/RepCode"}, "secondaryOwners": {"title": "Secondary Owners", "type": "array", "items": {"$ref": "#/definitions/Person"}}, "secondaryOwnersV2": {"title": "Secondary Owners V2", "type": "array", "items": {"$ref": "#/definitions/Person"}}, "accountManagementType": {"title": "Account Management Type", "type": "string", "maxLength": 255, "minLength": 0, "enum": ["Brokerage", "Managed"]}, "accountManagementTypeV2": {"title": "Account Management Type V2", "type": "string", "maxLength": 255, "minLength": 0, "enum": ["Brokerage"]}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "isAccountForiegn": {"title": "Is Account <PERSON>gn", "type": "boolean"}, "isInstitutionalAccount": {"title": "Is Institutional Account", "type": "boolean"}, "isManaged": {"title": "Is Managed", "type": "boolean"}, "wantToAddBeneficiaries": {"title": "Want to add beneficiaries", "type": "boolean"}}}, "AccountCommunicationPreference": {"type": "object", "title": "AccountCommunicationPreference", "description": "Account Communication Preference", "properties": {"additionalMailings": {"title": "Additional Mailings", "type": "string", "maxLength": 255, "minLength": 0}, "annualReports": {"title": "Annual Reports", "type": "string", "maxLength": 255, "minLength": 0}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "communicationsTypes": {"title": "Communications Types", "type": "string", "maxLength": 255, "minLength": 0}, "corporateActions": {"title": "Corporate Actions", "type": "string", "maxLength": 255, "minLength": 0}, "deliveryOptions": {"title": "Delivery Options", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "proxies": {"title": "Proxies", "type": "string", "maxLength": 255, "minLength": 0}}}, "AccountCreation": {"type": "object", "title": "AccountCreation", "description": "Account Creation", "properties": {"category": {"title": "Category", "type": "object", "$ref": "#/definitions/Category"}, "primaryOwner": {"title": "Primary Owner", "type": "object", "$ref": "#/definitions/Person"}, "registrationType": {"title": "Registration Type", "type": "object", "$ref": "#/definitions/RegistrationType"}, "secondaryOwners": {"title": "Secondary Owners", "type": "array", "items": {"$ref": "#/definitions/Person"}}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "isManaged": {"title": "Is Managed", "type": "boolean"}}}, "AccountCreationDetail": {"type": "object", "title": "AccountCreationDetail", "description": "Account Creation Detail", "properties": {"documents": {"title": "Documents", "type": "array", "items": {"$ref": "#/definitions/Document"}}, "initialFundingCurrency": {"title": "Initial Funding Currency", "type": "object", "$ref": "#/definitions/Currency"}, "onlineAccountSetup": {"title": "Online Account Setup", "type": "array", "items": {"$ref": "#/definitions/OnlineAccountSetup"}}, "signatures": {"title": "Signatures", "type": "array", "items": {"$ref": "#/definitions/Signature"}}, "sourceOfFunds": {"title": "Source of funds", "type": "array", "items": {"$ref": "#/definitions/AccountSourceOfFunds"}}, "accountNumberNotAvailable": {"title": "Account Number Not Available", "type": "boolean"}, "applicationDate": {"title": "Application Date", "type": "string", "format": "date"}, "applicationNumber": {"title": "Application Number", "type": "string"}, "attestationDate": {"title": "Attestation Date", "type": "string", "format": "date"}, "beneficiarysAccountNumber": {"title": "Beneficiary's Account Number", "type": "string", "maxLength": 255, "minLength": 0}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "esignatureEnvelopeID": {"title": "e-Signature Envelope ID", "type": "string", "maxLength": 255, "minLength": 0}, "ibDToInitiateTransfer": {"title": "IBD to Initiate Transfer", "type": "boolean"}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "initialFunding": {"title": "Initial Funding", "type": "number"}, "offerExpiresSoon": {"title": "Offer expires soon", "type": "boolean"}, "rateChangeExpectedSoon": {"title": "Rate change expected soon", "type": "boolean"}, "setupOnlineOrEDeliveryOfStatements": {"title": "Setup Online or eDelivery of Statements", "type": "boolean"}, "statusScore": {"title": "Status Score", "type": "integer"}, "statusScoreMaximum": {"title": "Status Score Maximum", "type": "integer"}, "statusScorePercentage": {"title": "Status Score Percentage", "type": "string"}, "transferEtsToIRA": {"title": "Transfer Assets to IRA", "type": "boolean"}, "transferToBDAInIRAApplication": {"title": "Transfer To BDA in IRA Application", "type": "boolean"}, "useBeneficiarysAccount": {"title": "Use Beneficiary's Account", "type": "boolean"}, "welcomeLetterDate": {"title": "Welcome Letter Date", "type": "string", "format": "date"}}}, "AccountCreationStatusResponse": {"type": "object", "title": "AccountCreationStatusResponse", "description": "Account Creation Status Response", "properties": {"accountNumber": {"title": "accountNumber", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "messages": {"title": "messages", "type": "string", "maxLength": 2000, "minLength": 0}, "requestId": {"title": "requestId", "type": "string", "maxLength": 255, "minLength": 0}, "sequence": {"title": "sequence", "type": "integer"}, "status": {"title": "status", "type": "string", "maxLength": 255, "minLength": 0}, "timestamp": {"title": "timestamp", "type": "string", "format": "date-time"}}}, "AccountCreationStep": {"type": "object", "title": "AccountCreationStep", "description": "Account Creation Step", "properties": {"id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "name": {"title": "Name", "type": "string", "maxLength": 255, "minLength": 0}}}, "AccountDataMapper": {"type": "object", "title": "AccountDataMapper", "description": "Account Data Mapper", "properties": {"steps": {"title": "steps", "type": "array", "items": {"$ref": "#/definitions/AccountCreationStep"}}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "stepNames": {"title": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "maxLength": 255, "minLength": 0}}}, "AccountDueDiligenceOptions": {"type": "object", "title": "AccountDueDiligenceOptions", "description": "Account Due-Diligence Options", "properties": {"expectedFundingMethod": {"title": "Expected Funding Method", "type": "object", "$ref": "#/definitions/FundingMethodTypes"}, "expectInternationalTravel": {"title": "Expect International Travel", "type": "boolean"}, "expectInternationalWires": {"title": "Expect International Wires", "type": "boolean"}, "expectMobileDeposit": {"title": "Expect Mobile Deposit", "type": "boolean"}, "expectSafeDepositBox": {"title": "Expect Safe Deposit Box", "type": "boolean"}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}}}, "AccountFeatures": {"type": "object", "title": "AccountFeatures", "description": "Account Features", "properties": {"owner": {"title": "Owner", "type": "object", "$ref": "#/definitions/Person"}, "product": {"title": "Product", "type": "object", "$ref": "#/definitions/Product"}, "accountPrefix": {"title": "Account Prefix", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "shortName": {"title": "Short Name", "type": "string", "maxLength": 255, "minLength": 0}, "title": {"title": "Title", "type": "string", "maxLength": 255, "minLength": 0}}}, "AccountInterestedParties": {"type": "object", "title": "AccountInterestedParties", "description": "Account Interested Parties", "properties": {"party": {"title": "Party", "type": "object", "$ref": "#/definitions/Person"}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "documentDelivery": {"title": "Document Delivery", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "relationshipToPrimary": {"title": "Relationship to primary", "type": "string", "maxLength": 255, "minLength": 0}, "relationshipType": {"title": "Relationship type", "type": "string", "maxLength": 255, "minLength": 0}}}, "AccountMessages": {"type": "object", "title": "AccountMessages", "description": "Account Messages", "properties": {"parentId": {"title": "parentId", "type": "object", "$ref": "#/definitions/Account"}, "additionalParameters": {"title": "additionalParameters", "type": "string"}, "id": {"title": "id", "type": "string", "format": "uuid"}, "message": {"title": "message", "type": "string"}, "messageType": {"title": "messageType", "type": "string"}, "parentMessageId": {"title": "parentMessageId", "type": "integer"}}}, "AccountNotice": {"type": "object", "title": "AccountNotice", "description": "Account Notice", "properties": {"id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "noticeType": {"title": "Notice Type", "type": "string", "maxLength": 255, "minLength": 0}, "noticeValue": {"title": "Notice Value", "type": "string", "maxLength": 255, "minLength": 0}}}, "AccountOwnership": {"type": "object", "title": "AccountOwnership", "description": "Account Ownership", "properties": {"alternateMailingAddress": {"title": "Alternate Mailing Address", "type": "object", "$ref": "#/definitions/Address"}, "dueDiligenceOptions": {"title": "Due-Diligence Options", "type": "object", "$ref": "#/definitions/AccountDueDiligenceOptions"}, "legalAddress": {"title": "Legal Address", "type": "object", "$ref": "#/definitions/Address"}, "mailingAddress": {"title": "Mailing Address", "type": "object", "$ref": "#/definitions/Address"}, "owner": {"title": "Owner", "type": "object", "$ref": "#/definitions/Person"}, "previousLegalAddress": {"title": "Previous Legal Address", "type": "object", "$ref": "#/definitions/Address"}, "trustedContact": {"title": "Trusted Contact", "type": "object", "$ref": "#/definitions/Person"}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "createdAt": {"title": "Created At", "type": "string", "format": "date-time"}, "createdBy": {"title": "Created By", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "isAddressSameAsPrimaryClient": {"title": "Is Address Same As Primary Client", "type": "boolean"}, "lastKYCDate": {"title": "Last KYC Date", "type": "string", "format": "date"}, "lastModifiedAt": {"title": "Last Modified At", "type": "string", "format": "date-time"}, "lastModifiedBy": {"title": "Last Modified By", "type": "string", "maxLength": 255, "minLength": 0}, "legalAddressStartDate": {"title": "Legal Address Start Date", "type": "string", "format": "date"}, "ownerType": {"title": "Owner Type", "type": "string", "maxLength": 255, "minLength": 0}, "percentage": {"title": "Percentage", "type": "number", "maximum": 100.0, "minimum": 0.0}, "relationshipToAccount": {"title": "Relationship to Account", "type": "string", "maxLength": 255, "minLength": 0}, "relationshipToOwner": {"title": "Relationship to Owner", "type": "string", "maxLength": 255, "minLength": 0}, "sendTaxDocumentsToAlternateAddress": {"title": "Send Tax Documents to Alternate Address", "type": "boolean"}, "spouseIsAJointOwner": {"title": "Spouse is a Joint Owner", "type": "boolean"}, "trustedContactInfoDeclined": {"title": "Trusted Contact Info Declined", "type": "boolean"}, "trustedContactRelationship": {"title": "Trusted Contact Relationship", "type": "string", "maxLength": 255, "minLength": 0}}}, "AccountPrefixSettings": {"type": "object", "title": "AccountPrefixSettings", "description": "Account <PERSON><PERSON><PERSON>", "properties": {"accountType": {"title": "Account Type", "type": "string", "maxLength": 255, "minLength": 0}, "brokeragePrefix": {"title": "Brokerage Prefix", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "managedAccountPrefix": {"title": "Managed Account Prefix", "type": "string", "maxLength": 255, "minLength": 0}, "state": {"title": "State", "type": "string", "maxLength": 255, "minLength": 0}}}, "AccountRelation": {"type": "object", "title": "AccountRelation", "description": "Account Relation", "properties": {"linkedAccounts": {"title": "Linked Accounts", "type": "object", "$ref": "#/definitions/Account"}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "relationship": {"title": "Relationship", "type": "string", "maxLength": 255, "minLength": 0}}}, "AccountSourceOfFunds": {"type": "object", "title": "AccountSourceOfFunds", "description": "Account Source of Funds", "properties": {"bankAddress": {"title": "Bank Address", "type": "object", "$ref": "#/definitions/Address"}, "fundingMethod": {"title": "Funding Method", "type": "object", "$ref": "#/definitions/FundingMethodTypes"}, "intermediaryBankAddress": {"title": "Intermediary Bank Address", "type": "object", "$ref": "#/definitions/Address"}, "payeeAddress": {"title": "Payee Address", "type": "object", "$ref": "#/definitions/Address"}, "transfer": {"title": "Transfer", "type": "object", "$ref": "#/definitions/Transfer"}, "additionalInformation": {"title": "Additional Information", "type": "string", "maxLength": 255, "minLength": 0}, "administratorName": {"title": "Administrator Name", "type": "string", "maxLength": 255, "minLength": 0}, "administratorPhoneNumber": {"title": "Administrator Phone Number", "type": "string", "maxLength": 50, "minLength": 0}, "amount": {"title": "Amount", "type": "number"}, "bankAccountHasOtherOwners": {"title": "Bank Account Has Other Owners", "type": "boolean"}, "bankAccountNickname": {"title": "Bank Account Nickname", "type": "string", "maxLength": 255, "minLength": 0}, "bankAccountNumber": {"title": "Bank Account Number", "type": "string", "maxLength": 255, "minLength": 0}, "bankAccountNumbers": {"title": "Bank Account Numbers", "type": "string", "maxLength": 255, "minLength": 0}, "bankAccountTitle": {"title": "Bank Account Title", "type": "string", "maxLength": 255, "minLength": 0}, "bankAccountTransferType": {"title": "Bank Account Transfer Type", "type": "string", "maxLength": 255, "minLength": 0}, "bankAccountType": {"title": "Bank Account Type", "type": "string", "maxLength": 255, "minLength": 0}, "bankBranch": {"title": "Bank Branch", "type": "string", "maxLength": 255, "minLength": 0}, "bankName": {"title": "Bank Name", "type": "string", "maxLength": 255, "minLength": 0}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "cdMaturityDate": {"title": "CD Maturity Date", "type": "string", "format": "date"}, "checkIncluded": {"title": "Check Included", "type": "boolean"}, "checkNumber": {"title": "Check Number", "type": "string", "maxLength": 255, "minLength": 0, "pattern": "^\\d*$"}, "closeAccount": {"title": "Close Account", "type": "boolean"}, "costBasisStepUp": {"title": "Cost Basis Step Up", "type": "boolean"}, "createdAt": {"title": "Created At", "type": "string", "format": "date-time"}, "createdBy": {"title": "Created By", "type": "string", "maxLength": 255, "minLength": 0}, "firmName": {"title": "Firm Name", "type": "string", "maxLength": 255, "minLength": 0}, "ibANCode": {"title": "IBAN Code", "type": "string", "maxLength": 255, "minLength": 0, "pattern": "^[A-Z0-9]*$"}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "intermediaryBank": {"title": "Intermediary Bank", "type": "string", "maxLength": 255, "minLength": 0}, "intermediaryBankAccountNumber": {"title": "Intermediary Bank Account Number", "type": "string", "maxLength": 255, "minLength": 0}, "isForeignBank": {"title": "Is Foreign Bank", "type": "boolean"}, "isSolicitedRollover": {"title": "Is Solicited Rollover", "type": "boolean"}, "lastModifiedAt": {"title": "Last Modified At", "type": "string", "format": "date-time"}, "lastModifiedBy": {"title": "Last Modified By", "type": "string", "maxLength": 255, "minLength": 0}, "otherOwnerEmail": {"title": "Other Owner Email", "type": "string", "maxLength": 50, "minLength": 0, "pattern": "^[a-zA-Z0-9.!#$%&'*+\\=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\\.[a-zA-Z0-9-]+)*$"}, "otherOwnerName": {"title": "Other Owner Name", "type": "string", "maxLength": 255, "minLength": 0}, "otherSecuritiesTransferAccountType": {"title": "Other Securities Transfer Account Type", "type": "string", "maxLength": 255, "minLength": 0}, "partialTransferAmount": {"title": "Partial Transfer Amount", "type": "number"}, "payeePhoneNumber": {"title": "Payee Phone Number", "type": "string", "maxLength": 50, "minLength": 0}, "replaceExistingInstruction": {"title": "Replace Existing Instruction", "type": "boolean"}, "rmDStatus": {"title": "RMD Status", "type": "string", "maxLength": 255, "minLength": 0}, "routingNumber": {"title": "Routing Number", "type": "string", "maxLength": 255, "minLength": 0, "pattern": "\\d{9}"}, "securitiesTransferAccountType": {"title": "Securities Transfer Account Type", "type": "string", "maxLength": 255, "minLength": 0}, "securitiesTransferType": {"title": "Securities Transfer Type", "type": "string", "maxLength": 255, "minLength": 0}, "sourceIRAAccountType": {"title": "Source IRA Account Type", "type": "string", "maxLength": 255, "minLength": 0}, "sourceOfFunds": {"title": "Source of funds", "type": "string", "maxLength": 255, "minLength": 0, "enum": ["Transfer from Another Account", "Gift", "Income", "Investment", "IRA Rollover", "Qualified Plan Rollover", "Business", "Settlement", "Other", "Business Revenue", "Compensation", "Donation", "Inheritance", "Insurance Payments", "Legal Settlement", "Lottery / Gaming", "Retirement Assets", "Sale of Business or Property", "Social Security Benefits", "Spouse / Parents"]}, "swIFTCode": {"title": "SWIFT Code", "type": "string", "maxLength": 255, "minLength": 0, "pattern": "^[A-Z0-9]*$"}}}, "AccountStatementSchedule": {"type": "object", "title": "AccountStatementSchedule", "description": "Account Statement Schedule", "properties": {"alternateStatementOptions": {"title": "Alternate Statement Options", "type": "string"}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "isAlternateSchedule": {"title": "Is Alternate Schedule", "type": "boolean"}, "recurrenceInterval": {"title": "Recurrence Interval", "type": "integer"}, "recurrenceType": {"title": "Recurrence Type", "type": "string", "maxLength": 255, "minLength": 0}}}, "Address": {"type": "object", "title": "Address", "description": "Address", "properties": {"country": {"title": "Country", "type": "object", "$ref": "#/definitions/Country"}, "state": {"title": "State", "type": "object", "$ref": "#/definitions/StateOrProvince"}, "addressProof": {"title": "Address Proof", "type": "string", "maxLength": 2096}, "addressType": {"title": "Address Type", "type": "string", "maxLength": 255, "minLength": 0}, "addressUsage": {"title": "Address Usage", "type": "string", "maxLength": 255, "minLength": 0}, "attentionLine": {"title": "Attention Line", "type": "string", "maxLength": 255, "minLength": 0}, "attentionLinePrefix": {"title": "Attention Line Prefix", "type": "string", "maxLength": 255, "minLength": 0, "enum": ["ATTN", "C/O"]}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "city": {"title": "City", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "isUSAddress": {"title": "Is US Address", "type": "boolean"}, "line1": {"title": "Line 1", "type": "string", "maxLength": 255, "minLength": 0}, "line2": {"title": "Line 2", "type": "string", "maxLength": 255, "minLength": 0}, "line3": {"title": "Line 3", "type": "string", "maxLength": 255, "minLength": 0}, "line4": {"title": "Line 4", "type": "string", "maxLength": 255, "minLength": 0}, "postalCode": {"title": "Postal Code", "type": "string", "maxLength": 255, "minLength": 0, "pattern": "[0-9A-Z -]"}, "providerAssignedID": {"title": "Provider Assigned ID", "type": "string", "maxLength": 255, "minLength": 0}, "requiresAdditionalPostage": {"title": "Requires Additional Postage", "type": "boolean"}, "stateOrProvince": {"title": "State or Province", "type": "string", "maxLength": 255, "minLength": 0}, "useEndDate": {"title": "Use End Date", "type": "string", "format": "date"}, "useStartDate": {"title": "Use Start Date", "type": "string", "format": "date"}}}, "AdvisorAccountBalances": {"type": "object", "title": "AdvisorAccountBalances", "description": "Advisor Account <PERSON>s", "properties": {"advisor": {"title": "Advisor", "type": "object", "$ref": "#/definitions/Associate"}, "beginningAccounts": {"title": "Beginning Accounts", "type": "integer"}, "beginningBuyingPower": {"title": "Beginning Buying Power", "type": "number"}, "beginningCashBalance": {"title": "Beginning Cash Balance", "type": "number"}, "beginningEts": {"title": "Beginning Assets", "type": "number"}, "beginningMarginBalance": {"title": "Beginning Margin Balance", "type": "number"}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "closedAccounts": {"title": "Closed Accounts", "type": "integer"}, "contributions": {"title": "Contributions", "type": "number"}, "endingAccounts": {"title": "Ending Accounts", "type": "integer"}, "endingBuyingPower": {"title": "Ending Buying Power", "type": "number"}, "endingCashBalance": {"title": "Ending Cash Balance", "type": "number"}, "endingEts": {"title": "Ending Assets", "type": "number"}, "endingMarginBalance": {"title": "Ending <PERSON><PERSON>", "type": "number"}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "marketAppreciation": {"title": "Market Appreciation", "type": "number"}, "newAccounts": {"title": "New Accounts", "type": "integer"}, "periodEndDate": {"title": "Period End Date", "type": "string", "format": "date"}, "periodStartDate": {"title": "Period Start Date", "type": "string", "format": "date"}, "periodType": {"title": "Period Type", "type": "string", "maxLength": 255, "minLength": 0}, "withdrawals": {"title": "<PERSON><PERSON><PERSON><PERSON>", "type": "number"}}}, "AdvisorPositions": {"type": "object", "title": "AdvisorPositions", "description": "Advisor Positions", "properties": {"advisor": {"title": "Advisor", "type": "object", "$ref": "#/definitions/Associate"}, "security": {"title": "Security", "type": "object", "$ref": "#/definitions/Security"}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "marketValue": {"title": "Market Value", "type": "number"}, "numberOfAccounts": {"title": "Number of accounts", "type": "integer"}, "numberOfShares": {"title": "Number of shares", "type": "number", "multipleOf": 1e-05}, "periodEndDate": {"title": "Period End Date", "type": "string", "format": "date"}, "securityCategory": {"title": "Security Category", "type": "string"}}}, "AggregatePositions": {"type": "object", "title": "AggregatePositions", "description": "Aggregate Positions", "properties": {"advisor": {"title": "Advisor", "type": "object", "$ref": "#/definitions/Associate"}, "client": {"title": "Client", "type": "object", "$ref": "#/definitions/Client"}, "orgUnit": {"title": "Org Unit", "type": "object", "$ref": "#/definitions/OrganizationalUnit"}, "security": {"title": "Security", "type": "object", "$ref": "#/definitions/Security"}, "aggregateType": {"title": "Aggregate Type", "type": "string", "maxLength": 255, "minLength": 0}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "marketValue": {"title": "Market Value", "type": "number"}, "numberOfAccounts": {"title": "Number of accounts", "type": "integer"}, "numberOfShares": {"title": "Number of shares", "type": "number", "multipleOf": 1e-05}, "periodEndDate": {"title": "Period End Date", "type": "string", "format": "date"}}}, "Announcement": {"type": "object", "title": "Announcement", "description": "Announcement", "properties": {"files": {"title": "Files", "type": "array", "items": {"$ref": "#/definitions/Document"}}, "orgUnit": {"title": "Org Unit", "type": "object", "$ref": "#/definitions/OrganizationalUnit"}, "audience": {"title": "Audience", "type": "string", "maxLength": 255, "minLength": 0}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "display": {"title": "Display", "type": "string", "maxLength": 255, "minLength": 0}, "displayEndDate": {"title": "Display End Date", "type": "string", "format": "date"}, "displayStartDate": {"title": "Display Start Date", "type": "string", "format": "date"}, "effectiveDate": {"title": "Effective Date", "type": "string", "format": "date"}, "expiryDate": {"title": "Expiry Date", "type": "string", "format": "date"}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "message": {"title": "Message", "type": "string", "maxLength": 2000, "minLength": 0}, "noticeNumber": {"title": "Notice Number", "type": "string"}, "subject": {"title": "Subject", "type": "string", "maxLength": 255, "minLength": 0}}}, "AnnuityDetails": {"type": "object", "title": "AnnuityDetails", "description": "Annuity Details", "properties": {"annuityFunding": {"title": "Annuity Funding", "type": "array", "items": {"$ref": "#/definitions/AnnuityFunding"}}, "creditingStrategies": {"title": "Crediting Strategies", "type": "array", "items": {"$ref": "#/definitions/IndexedAnnuityCreditingStrategy"}}, "product": {"title": "Product", "type": "object", "$ref": "#/definitions/Product"}, "productSelection": {"title": "Product Selection", "type": "array", "items": {"$ref": "#/definitions/AnnuityProductSelection"}}, "replacedAnnuities": {"title": "Replaced <PERSON><PERSON><PERSON>", "type": "array", "items": {"$ref": "#/definitions/ReplacedAnnuityDetails"}}, "riders": {"title": "Riders", "type": "array", "items": {"$ref": "#/definitions/Product"}}, "uiHackReplacedAnnuities1": {"title": "UI Hack Replaced Annuities 1", "type": "object", "$ref": "#/definitions/ReplacedAnnuityDetails"}, "uiHackReplacedAnnuities2": {"title": "UI Hack Replaced Annuities 2", "type": "object", "$ref": "#/definitions/ReplacedAnnuityDetails"}, "uiHackReplacedAnnuities3": {"title": "UI Hack Replaced Annuities 3", "type": "object", "$ref": "#/definitions/ReplacedAnnuityDetails"}, "acknowledgeIlliquidInvestment": {"title": "Acknowledge Illiquid Investment", "type": "boolean"}, "additionalDeathBenefitFeesPercent": {"title": "Additional Death Benefit Fe<PERSON>", "type": "number", "maximum": 100.0, "minimum": 0.0}, "administrationFeesPercent": {"title": "Administration Fees <PERSON>", "type": "number", "maximum": 100.0, "minimum": 0.0}, "allowedWithdrawalPercentage": {"title": "Allowed Withdrawal Percentage", "type": "number", "maximum": 100.0, "minimum": 0.0}, "allowedYear1WithdrawalPercentage": {"title": "Allowed Year 1 Withdrawal Percentage", "type": "number", "maximum": 100.0, "minimum": 0.0}, "baseFees": {"title": "Base Fees", "type": "number"}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "chargeInterestOnWithdrawal": {"title": "Charge Interest on Withdrawal", "type": "boolean"}, "contractNumber": {"title": "Contract Number", "type": "string", "maxLength": 255, "minLength": 0}, "disclosureRequirementsMet": {"title": "Disclosure Requirements Met", "type": "boolean"}, "effectiveDate": {"title": "Effective Date", "type": "string", "format": "date"}, "enhancedDeathBenefit": {"title": "Enhanced Death Benefit", "type": "string", "maxLength": 255, "minLength": 0}, "enhancedDeathBenefitFeesPercent": {"title": "Enhanced Death Benefit Fees Percent", "type": "number", "maximum": 100.0, "minimum": 0.0}, "fundExpensesPercent": {"title": "Fund Expenses Percent", "type": "number", "maximum": 100.0, "minimum": 0.0}, "guaranteedDeathBenefitFees": {"title": "Guaranteed Death Benefit Fees", "type": "number"}, "guaranteedYield": {"title": "<PERSON><PERSON><PERSON><PERSON>", "type": "number", "maximum": 100.0, "minimum": 0.0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "intendedUseOfFunds": {"title": "Intended Use of Funds", "type": "string", "maxLength": 255, "minLength": 0}, "interestOnWithdrawalIsCumulative": {"title": "Interest on Withdrawal is Cumulative", "type": "boolean"}, "interestRate": {"title": "Interest Rate", "type": "number", "maximum": 100.0, "minimum": 0.0}, "interestRateBonus": {"title": "Interest Rate Bonus", "type": "number", "maximum": 100.0, "minimum": 0.0}, "interestRateTotal": {"title": "Interest Rate Total", "type": "number", "maximum": 100.0, "minimum": 0.0}, "isPartOfSeries": {"title": "Is part of Series", "type": "boolean"}, "isReplacement": {"title": "Is Replacement", "type": "boolean"}, "liquiditySufficientForAYear": {"title": "Liquidity Sufficient for a Year", "type": "boolean"}, "livingBenefitFeesPercent": {"title": "Living Benefit Fees Percent", "type": "number", "maximum": 100.0, "minimum": 0.0}, "otherFeesDetails": {"title": "Other Fees Details", "type": "string", "maxLength": 255, "minLength": 0}, "otherFeesPercent": {"title": "Other Fees Percent", "type": "number", "maximum": 100.0, "minimum": 0.0}, "otherIntendedUseOfFunds": {"title": "Other Intended Use of Funds", "type": "string", "maxLength": 255, "minLength": 0}, "premium": {"title": "Premium", "type": "number"}, "provider": {"title": "Provider", "type": "string", "maxLength": 255, "minLength": 0}, "rateGuaranteedYears": {"title": "Rate Guaranteed Years", "type": "integer"}, "recentAnnuityExchange": {"title": "Recent Annuity Exchange", "type": "boolean"}, "recentInvestmentActivity": {"title": "Recent Investment Activity", "type": "boolean"}, "recommendationRationale": {"title": "Recommendation Rationale", "type": "string", "maxLength": 2000, "minLength": 0}, "surrenderChargesApplicableFrom": {"title": "Surrender Charges Applicable From", "type": "string", "maxLength": 255, "minLength": 0}, "surrenderChargesByYear": {"title": "Surrender Charges by Year", "type": "number", "maximum": 100.0, "minimum": 0.0}, "surrenderYears": {"title": "Surrender Years", "type": "integer"}, "timeHorizon": {"title": "Time Horizon", "type": "string", "maxLength": 255, "minLength": 0}, "totalAnnualTransactionCosts": {"title": "Total Annual Transaction Costs", "type": "number"}, "type": {"title": "Type", "type": "string", "maxLength": 255, "minLength": 0}, "withdrawalWindowDays": {"title": "<PERSON><PERSON>wal Window Days", "type": "integer"}, "withdrawalWindowYear": {"title": "Withdrawal Window Year", "type": "integer"}}}, "AnnuityFunding": {"type": "object", "title": "AnnuityFunding", "description": "Annuity Funding", "properties": {"fundingSource": {"title": "Funding Source", "type": "object", "$ref": "#/definitions/Transfer"}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "fundingSourceAmountUsed": {"title": "Funding Source Amount Used", "type": "number"}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}}}, "AnnuityProductSelection": {"type": "object", "title": "AnnuityProductSelection", "description": "Annuity Product Selection", "properties": {"product": {"title": "Product", "type": "object", "$ref": "#/definitions/Product"}, "annuitantAge": {"title": "Annuitant Age", "type": "integer"}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "newIndication": {"title": "New Indication", "type": "string", "maxLength": 255, "minLength": 0}, "ownerAge": {"title": "Owner Age", "type": "integer"}}}, "Assets": {"type": "object", "title": "Assets", "description": "Assets", "properties": {"homeValue": {"title": "Home Value", "type": "number"}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "otherAssets": {"title": "Other Assets", "type": "string", "maxLength": 255, "minLength": 0}, "otherAssetsValue": {"title": "Other Assets Value", "type": "number"}, "personalPropertyValue": {"title": "Personal Property Value", "type": "number"}, "realEstateValue": {"title": "Real Estate Value", "type": "number"}, "type": {"title": "Type", "type": "string", "maxLength": 255, "minLength": 0}, "value": {"title": "Value", "type": "number"}, "vehicleValue": {"title": "Vehicle Value", "type": "number"}}}, "Associate": {"type": "object", "title": "Associate", "description": "Associate", "properties": {"address": {"title": "Address", "type": "object", "$ref": "#/definitions/Address"}, "canAccessOrgUnits": {"title": "Can Access Org Units", "type": "array", "items": {"$ref": "#/definitions/OrganizationalUnit"}}, "canAccessOrgUnitsAtOrBelow": {"title": "Can Access Org Units at or Below", "type": "array", "items": {"$ref": "#/definitions/OrganizationalUnit"}}, "orgUnit": {"title": "Org Unit", "type": "object", "$ref": "#/definitions/OrganizationalUnit"}, "ouLevel0": {"title": "OU Level 0", "type": "object", "$ref": "#/definitions/OrganizationalUnit"}, "ouLevel1": {"title": "OU Level 1", "type": "object", "$ref": "#/definitions/OrganizationalUnit"}, "ouLevel2": {"title": "OU Level 2", "type": "object", "$ref": "#/definitions/OrganizationalUnit"}, "ouLevel3": {"title": "OU Level 3", "type": "object", "$ref": "#/definitions/OrganizationalUnit"}, "ouLevel4": {"title": "OU Level 4", "type": "object", "$ref": "#/definitions/OrganizationalUnit"}, "ouLevel5": {"title": "OU Level 5", "type": "object", "$ref": "#/definitions/OrganizationalUnit"}, "ouLevel6": {"title": "OU Level 6", "type": "object", "$ref": "#/definitions/OrganizationalUnit"}, "repCodes": {"title": "RepCodes", "type": "array", "items": {"$ref": "#/definitions/RepCode"}}, "alternateEmail": {"title": "Alternate Email", "type": "string", "maxLength": 50, "minLength": 0, "pattern": "^[a-zA-Z0-9.!#$%&'*+\\=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\\.[a-zA-Z0-9-]+)*$"}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "employer": {"title": "Employer", "type": "string", "maxLength": 255, "minLength": 0}, "firstName": {"title": "First Name", "type": "string", "maxLength": 255, "minLength": 0, "pattern": "^[a-zA-Z'.-]*$"}, "fullDisplayName": {"title": "Full Display Name", "type": "string"}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "idCode": {"title": "ID Code", "type": "string", "maxLength": 255, "minLength": 0}, "lastName": {"title": "Last Name", "type": "string", "maxLength": 255, "minLength": 0, "pattern": "^[a-zA-Z'.-]*$"}, "middleName": {"title": "Middle Name", "type": "string", "maxLength": 255, "minLength": 0, "pattern": "^[a-zA-Z'.-]*$"}, "primaryEmail": {"title": "Primary Email", "type": "string", "maxLength": 50, "minLength": 0, "pattern": "^[a-zA-Z0-9.!#$%&'*+\\=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\\.[a-zA-Z0-9-]+)*$"}, "primaryPhoneNumber": {"title": "Primary Phone Number", "type": "string", "maxLength": 50, "minLength": 0}, "recordSource": {"title": "Record Source", "type": "string", "maxLength": 255, "minLength": 0}, "secondaryPhoneNumber": {"title": "Secondary Phone Number", "type": "string", "maxLength": 50, "minLength": 0}, "userID": {"title": "User ID", "type": "string", "maxLength": 255, "minLength": 0}, "userName": {"title": "User Name", "type": "string", "maxLength": 255, "minLength": 0}}}, "AuditErrorLogs": {"type": "object", "title": "AuditErrorLogs", "description": "<PERSON>t <PERSON>", "properties": {"createdDate": {"title": "Created Date", "type": "string", "format": "date-time"}, "createdUser": {"title": "Created User", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "lastModifiedDate": {"title": "Last Modified Date", "type": "string", "format": "date-time"}, "logStatus": {"title": "Log Status", "type": "string", "maxLength": 255, "minLength": 0}, "recordStatus": {"title": "Record Status", "type": "string", "maxLength": 255, "minLength": 0}, "recordUUID": {"title": "Record UUID", "type": "string", "maxLength": 255, "minLength": 0}}}, "BalancesResponse": {"type": "object", "title": "BalancesResponse", "description": "BalancesResponse", "properties": {"balances": {"title": "balances", "type": "array", "items": {"$ref": "#/definitions/AccountBalances"}}, "positions": {"title": "positions", "type": "array", "items": {"$ref": "#/definitions/Position"}}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}}}, "Bank": {"type": "object", "title": "Bank", "description": "Bank", "properties": {"bankCode": {"title": "Bank Code", "type": "string", "maxLength": 255, "minLength": 0}, "domesticWireRoutingNumber": {"title": "Domestic Wire Routing Number", "type": "string", "maxLength": 255, "minLength": 0}, "fax": {"title": "Fax", "type": "string", "maxLength": 50, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "idBIGA": {"title": "id", "type": "string", "maxLength": 255, "minLength": 0}, "internationalWireRoutingNumber": {"title": "International Wire Routing Number", "type": "integer"}, "primaryPhoneNumber": {"title": "Primary Phone Number", "type": "string", "maxLength": 50, "minLength": 0}, "secondaryPhoneNumber": {"title": "Secondary Phone Number", "type": "string", "maxLength": 50, "minLength": 0}, "swiftCode": {"title": "Swift code", "type": "integer"}}}, "Beneficiary": {"type": "object", "title": "Beneficiary", "description": "Beneficiary", "properties": {"beneficiary": {"title": "Beneficiary", "type": "object", "$ref": "#/definitions/Person"}, "trustedContact": {"title": "Trusted Contact", "type": "object", "$ref": "#/definitions/Person"}, "beneficiaryType": {"title": "Beneficiary Type", "type": "string", "maxLength": 255, "minLength": 0}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "createdAt": {"title": "Created At", "type": "string", "format": "date-time"}, "createdBy": {"title": "Created By", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "isContingentBeneficiary": {"title": "Is Contingent Beneficiary", "type": "boolean"}, "lastModifiedAt": {"title": "Last Modified At", "type": "string", "format": "date-time"}, "lastModifiedBy": {"title": "Last Modified By", "type": "string", "maxLength": 255, "minLength": 0}, "orderOfThisBeneficiary": {"title": "Order of this Beneficiary", "type": "integer"}, "perStirM7iqk": {"title": "<PERSON>", "type": "boolean"}, "perStirpes": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "boolean"}, "percentage": {"title": "Percentage", "type": "number", "maximum": 100.0, "minimum": 0.0}, "relationship": {"title": "Relationship", "type": "string", "maxLength": 255, "minLength": 0}, "rmDOption": {"title": "RMD Option", "type": "string", "maxLength": 255, "minLength": 0}}}, "Branch": {"type": "object", "title": "Branch", "description": "Branch", "properties": {"address": {"title": "Address", "type": "object", "$ref": "#/definitions/Address"}, "branchManager": {"title": "Branch Manager", "type": "object", "$ref": "#/definitions/Associate"}, "organizationalUnit": {"title": "OrganizationalUnit", "type": "object", "$ref": "#/definitions/OrganizationalUnit"}, "parent": {"title": "Parent", "type": "object", "$ref": "#/definitions/FinancialInstitution"}, "branchBillingCode": {"title": "branchBillingCode", "type": "string", "maxLength": 255, "minLength": 0}, "branchCode": {"title": "branchCode", "type": "string", "maxLength": 255, "minLength": 0}, "branchName": {"title": "Branch Name", "type": "string", "maxLength": 255, "minLength": 0}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "fax": {"title": "Fax", "type": "string", "maxLength": 50, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "primaryPhoneNumber": {"title": "Primary Phone Number", "type": "string", "maxLength": 50, "minLength": 0}, "region": {"title": "Region", "type": "string", "maxLength": 255, "minLength": 0}, "regionCode": {"title": "Region Code", "type": "string", "maxLength": 255, "minLength": 0}, "secondaryPhoneNumber": {"title": "Secondary Phone Number", "type": "string", "maxLength": 50, "minLength": 0}}}, "CashFlow": {"type": "object", "title": "CashFlow", "description": "Cash Flow", "properties": {"account": {"title": "Account", "type": "object", "$ref": "#/definitions/Account"}, "accruedInterestPurchaseMTD": {"title": "Accrued Interest Purchase - MTD", "type": "number"}, "accruedInterestPurchaseYTD": {"title": "Accrued Interest Purchase - YTD", "type": "number"}, "accruedInterestSalesMTD": {"title": "Accrued Interest Sales - MTD", "type": "number"}, "accruedInterestSalesYTD": {"title": "Accrued Interest Sales - YTD", "type": "number"}, "asOfDate": {"title": "As of Date", "type": "string", "format": "date"}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "corporateInterestMTD": {"title": "Corporate Interest - MTD", "type": "number"}, "corporateInterestYTD": {"title": "Corporate Interest - YTD", "type": "number"}, "creditInterestMTD": {"title": "Credit Interest - MTD", "type": "number"}, "creditInterestYTD": {"title": "Credit Interest - YTD", "type": "number"}, "governmentInterestMTD": {"title": "Government Interest - MTD", "type": "number"}, "governmentInterestYTD": {"title": "Government Interest - YTD", "type": "number"}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "liquidationsMTD": {"title": "Liquidations -MTD", "type": "number"}, "liquidationsYTD": {"title": "Liquidations -YTD", "type": "number"}, "longTermCapitalGainJe4tn2juge": {"title": "Long-Term Capital Gains - YTD", "type": "number"}, "longTermCapitalGainsM7iqk": {"title": "Long-Term Capital Gains - MTD", "type": "number"}, "longTermCapitalGainsMTD": {"title": "Long-Term Capital Gains -MTD", "type": "number"}, "longTermCapitalGainsYTD": {"title": "Long-Term Capital Gains-YTD", "type": "number"}, "moneyMarketMTD": {"title": "Money Market - MTD", "type": "number"}, "moneyMarketYTD": {"title": "Money Market - YTD", "type": "number"}, "municipalInterestTaxMTD": {"title": "Municipal Interest (Tax) - MTD", "type": "number"}, "municipalInterestTaxYTD": {"title": "Municipal Interest (Tax) - YTD", "type": "number"}, "nonQualifiedDividendsMTD": {"title": "Non-Qualified Dividends - MTD", "type": "number"}, "nonQualifiedDividendsYTD": {"title": "Non-Qualified Dividends - YTD", "type": "number"}, "otherIncomeMTD": {"title": "Other Income - MTD", "type": "number"}, "otherIncomeYTD": {"title": "Other Income - YTD", "type": "number"}, "partnershipDistributionsJe4tm": {"title": "Partnership Distributions - MTD", "type": "number"}, "partnershipDistributionsMTD": {"title": "Partnership Distributions -MTD", "type": "number"}, "partnershipDistributionsY7sc6": {"title": "Partnership Distributions - YTD", "type": "number"}, "partnershipDistributionsYTD": {"title": "Partnership Distributions -YTD", "type": "number"}, "principalPaymentsMTD": {"title": "Principal Payments - MTD", "type": "number"}, "principalPaymentsYTD": {"title": "Principal Payments - YTD", "type": "number"}, "qualifiedDividendsMTD": {"title": "Qualified Dividends - MTD", "type": "number"}, "qualifiedDividendsYTD": {"title": "Qualified Dividends - YTD", "type": "number"}, "repurchaseInterestMTD": {"title": "Repurchase Interest - MTD", "type": "number"}, "repurchaseInterestYTD": {"title": "Repurchase Interest - YTD", "type": "number"}, "returnOfCapitalMTD": {"title": "Return of Capital - MTD", "type": "number"}, "returnOfCapitalROCMTD": {"title": "Return of Capital (ROC)-MTD", "type": "number"}, "returnOfCapitalROCYTD": {"title": "Return of Capital (ROC)-YTD", "type": "number"}, "returnOfCapitalYTD": {"title": "Return of Capital - YTD", "type": "number"}, "royaltyPaymentsMTD": {"title": "Royalty Payments - MTD", "type": "number"}, "royaltyPaymentsYTD": {"title": "Royalty Payments - YTD", "type": "number"}, "shortTermCapitalGainsMTD": {"title": "Short-Term Capital Gains -MTD", "type": "number"}, "shortTermCapitalGainsYTD": {"title": "Short-Term Capital Gains -YTD", "type": "number"}, "substitutePaymentsMTD": {"title": "Substitute Payments - MTD", "type": "number"}, "substitutePaymentsYTD": {"title": "Substitute Payments - YTD", "type": "number"}, "totalIncomeMTD": {"title": "Total Income - MTD", "type": "string"}, "totalIncomeYTD": {"title": "Total Income - YTD", "type": "string"}}}, "Category": {"type": "object", "title": "Category", "description": "Category", "properties": {"id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "name": {"title": "name", "type": "string", "maxLength": 255, "minLength": 0}}}, "Client": {"type": "object", "title": "Client", "description": "Client", "properties": {"accountClient": {"title": "account Client", "type": "string"}, "additionalMembers": {"title": "Additional Members", "type": "array", "items": {"$ref": "#/definitions/Person"}}, "financialGoals": {"title": "Financial Goals", "type": "array", "items": {"$ref": "#/definitions/FinancialGoal"}}, "investmentGoals": {"title": "Investment Goals", "type": "array", "items": {"$ref": "#/definitions/FinancialGoal"}}, "primaryMember": {"title": "Primary Member", "type": "object", "$ref": "#/definitions/Person"}, "annualIncome": {"title": "Annual Income", "type": "string", "maxLength": 30, "minLength": 0}, "annualIncomeExact": {"title": "Annual Income (exact)", "type": "number"}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "clientName": {"title": "Client Name", "type": "string", "maxLength": 255, "minLength": 0}, "createdAt": {"title": "Created At", "type": "string", "format": "date-time"}, "createdBy": {"title": "Created By", "type": "string", "maxLength": 255, "minLength": 0}, "federalMarginalTaxRate": {"title": "Federal Marginal Tax Rate", "type": "string", "maxLength": 30, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "lastModifiedAt": {"title": "Last Modified At", "type": "string", "format": "date-time"}, "lastModifiedBy": {"title": "Last Modified By", "type": "string", "maxLength": 255, "minLength": 0}, "liquidAssets": {"title": "Liquid Assets", "type": "string", "maxLength": 30, "minLength": 0}, "liquidAssetsExact": {"title": "Liquid Assets (exact)", "type": "number"}, "netWorthExcludingHome": {"title": "Net Worth excluding Home", "type": "string", "maxLength": 255, "minLength": 0}, "netWorthExcludingHomeExact": {"title": "Net Worth excluding Home (exact)", "type": "number"}, "rating": {"title": "Rating", "type": "integer"}, "repCode": {"title": "Rep Code", "type": "string", "maxLength": 255, "minLength": 0}, "totalAssets": {"title": "Total Assets", "type": "number"}, "totalLiabilities": {"title": "Total Liabilities", "type": "number"}, "totalNetWorth": {"title": "Total Net Worth", "type": "string"}, "type": {"title": "Type", "type": "string", "maxLength": 255, "minLength": 0}}}, "ClientAccountBalances": {"type": "object", "title": "ClientAccountBalances", "description": "Client Account <PERSON><PERSON>s", "properties": {"advisor": {"title": "Advisor", "type": "object", "$ref": "#/definitions/Associate"}, "client": {"title": "Client", "type": "object", "$ref": "#/definitions/Client"}, "beginningAccounts": {"title": "Beginning Accounts", "type": "integer"}, "beginningBuyingPower": {"title": "Beginning Buying Power", "type": "number"}, "beginningCashBalance": {"title": "Beginning Cash Balance", "type": "number"}, "beginningEts": {"title": "Beginning Assets", "type": "number"}, "beginningMarginBalance": {"title": "Beginning Margin Balance", "type": "number"}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "closedAccounts": {"title": "Closed Accounts", "type": "integer"}, "contributions": {"title": "Contributions", "type": "number"}, "endingAccounts": {"title": "Ending Accounts", "type": "integer"}, "endingBuyingPower": {"title": "Ending Buying Power", "type": "number"}, "endingCashBalance": {"title": "Ending Cash Balance", "type": "number"}, "endingEts": {"title": "Ending Assets", "type": "number"}, "endingMarginBalance": {"title": "Ending <PERSON><PERSON>", "type": "number"}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "marketAppreciation": {"title": "Market Appreciation", "type": "number"}, "newAccounts": {"title": "New Accounts", "type": "integer"}, "periodEndDate": {"title": "Period End Date", "type": "string", "format": "date"}, "periodStartDate": {"title": "Period Start Date", "type": "string", "format": "date"}, "periodType": {"title": "Period Type", "type": "string", "maxLength": 255, "minLength": 0}, "withdrawals": {"title": "<PERSON><PERSON><PERSON><PERSON>", "type": "number"}}}, "ClientMember": {"type": "object", "title": "ClientMember", "description": "Client Member", "properties": {"member": {"title": "Member", "type": "object", "$ref": "#/definitions/Person"}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "j_Primary": {"title": "Primary", "type": "boolean"}, "relationship": {"title": "Relationship", "type": "string", "maxLength": 255, "minLength": 0}}}, "ClientPositions": {"type": "object", "title": "ClientPositions", "description": "Client Positions", "properties": {"advisor": {"title": "Advisor", "type": "object", "$ref": "#/definitions/Associate"}, "client": {"title": "Client", "type": "object", "$ref": "#/definitions/Client"}, "security": {"title": "Security", "type": "object", "$ref": "#/definitions/Security"}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "marketValue": {"title": "Market Value", "type": "number"}, "numberOfAccounts": {"title": "Number of accounts", "type": "integer"}, "numberOfShares": {"title": "Number of shares", "type": "number", "multipleOf": 1e-05}, "periodEndDate": {"title": "Period End Date", "type": "string", "format": "date"}}}, "Collateral": {"type": "object", "title": "Collateral", "description": "Collateral", "properties": {"loan": {"title": "Loan", "type": "object", "$ref": "#/definitions/Account"}, "code": {"title": "Code", "type": "string", "maxLength": 255, "minLength": 0}, "description": {"title": "Description", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "isReleased": {"title": "Is Released", "type": "boolean"}, "priority": {"title": "Priority", "type": "integer"}, "serialNumber": {"title": "Serial Number", "type": "string", "maxLength": 255, "minLength": 0}, "type": {"title": "Type", "type": "string", "maxLength": 255, "minLength": 0}}}, "Commissions": {"type": "object", "title": "Commissions", "description": "Commissions", "properties": {"associate": {"title": "Associate", "type": "object", "$ref": "#/definitions/Associate"}, "commissionsByProduct": {"title": "Commissions by Product", "type": "array", "items": {"$ref": "#/definitions/CommissionsByProduct"}}, "exchange": {"title": "Exchange", "type": "object", "$ref": "#/definitions/Exchange"}, "product": {"title": "Product", "type": "object", "$ref": "#/definitions/Product"}, "repCode": {"title": "Rep Code", "type": "object", "$ref": "#/definitions/RepCode"}, "asOfDate": {"title": "As of Date", "type": "string", "format": "date"}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "commissions": {"title": "Commissions", "type": "number"}, "commissionsMTD": {"title": "Commissions MTD", "type": "number"}, "commissionsYTD": {"title": "Commissions YTD", "type": "number"}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "tickets": {"title": "Tickets", "type": "number", "multipleOf": 0.01}, "ticketsMTD": {"title": "Tickets MTD", "type": "number", "multipleOf": 0.01}, "ticketsYTD": {"title": "Tickets YTD", "type": "number", "multipleOf": 0.01}}}, "CommissionsByProduct": {"type": "object", "title": "CommissionsByProduct", "description": "Commissions by Product", "properties": {"exchange": {"title": "Exchange", "type": "object", "$ref": "#/definitions/Exchange"}, "product": {"title": "Product", "type": "object", "$ref": "#/definitions/Product"}, "asOfDate": {"title": "As of Date", "type": "string", "format": "date"}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "commissions": {"title": "Commissions", "type": "number"}, "commissionsMTD": {"title": "Commissions MTD", "type": "number"}, "commissionsYTD": {"title": "Commissions YTD", "type": "number"}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "tickets": {"title": "Tickets", "type": "integer"}, "ticketsMTD": {"title": "Tickets MTD", "type": "integer"}, "ticketsYTD": {"title": "Tickets YTD", "type": "integer"}}}, "ConfigSchema": {"type": "object", "title": "ConfigSchema", "description": "ConfigSchema", "properties": {"id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "settingName": {"title": "<PERSON><PERSON><PERSON>", "type": "string", "maxLength": 255, "minLength": 0}, "settingValue": {"title": "settingValue", "type": "string", "maxLength": 255, "minLength": 0}}}, "Country": {"type": "object", "title": "Country", "description": "Country", "properties": {"bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "code2Letters": {"title": "Code 2-letters", "type": "string", "maxLength": 255, "minLength": 0, "pattern": "[A-Z]"}, "code3Letters": {"title": "Code 3-letters", "type": "string", "maxLength": 255, "minLength": 0, "pattern": "[A-Z]"}, "fullName": {"title": "Full Name", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "shortName": {"title": "Short Name", "type": "string", "maxLength": 255, "minLength": 0}, "sortOrder": {"title": "Sort order", "type": "integer"}}}, "CreateAccountResponse": {"type": "object", "title": "CreateAccountResponse", "description": "Create Account Response", "properties": {"accountId": {"title": "accountId", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "requestId": {"title": "requestId", "type": "string", "maxLength": 255, "minLength": 0}, "statusMsg": {"title": "statusMsg", "type": "string", "maxLength": 255, "minLength": 0}}}, "Currency": {"type": "object", "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON>", "properties": {"alphabeticCode": {"title": "Alphabetic Code", "type": "string", "maxLength": 255, "minLength": 0, "pattern": "[A-Z]"}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "countryCode": {"title": "Country Code", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "issuingEntity": {"title": "Issuing Entity", "type": "string", "maxLength": 255, "minLength": 0}, "minorUnits": {"title": "Minor Units", "type": "integer"}, "name": {"title": "Name", "type": "string", "maxLength": 255, "minLength": 0}, "numericCode": {"title": "Numeric Code", "type": "string", "maxLength": 255, "minLength": 0, "pattern": "[0-9]"}, "sortOrder": {"title": "Sort order", "type": "integer"}, "symbol": {"title": "Symbol", "type": "string", "maxLength": 255, "minLength": 0}}}, "Custodian": {"type": "object", "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON>", "properties": {"bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "code": {"title": "Code", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "name": {"title": "Name", "type": "string", "maxLength": 255, "minLength": 0}}}, "Customemail": {"type": "object", "title": "Customemail", "description": "Customemail", "properties": {"attachments": {"title": "attachments", "type": "string"}, "bcc": {"title": "bcc", "type": "string", "minLength": 0}, "cc": {"title": "cc", "type": "string", "minLength": 0}, "emailpriority": {"title": "emailpriority", "type": "string"}, "htmlBody": {"title": "htmlBody", "type": "string", "minLength": 0}, "providerName": {"title": "providerName", "type": "string", "minLength": 0}, "subject": {"title": "subject", "type": "string", "minLength": 0}, "textBody": {"title": "textBody", "type": "string", "minLength": 0}, "to": {"title": "to", "type": "string", "minLength": 0}}}, "Document": {"type": "object", "title": "Document", "description": "Document", "properties": {"createdAt": {"title": "Created At", "type": "string", "format": "date-time"}, "createdBy": {"title": "Created By", "type": "string", "maxLength": 255, "minLength": 0}, "description": {"title": "Description", "type": "string", "maxLength": 255, "minLength": 0}, "document": {"title": "Document", "type": "string", "maxLength": 2096}, "documentRevision": {"title": "Document Revision", "type": "string", "maxLength": 255, "minLength": 0}, "documentStatus": {"title": "Document Status", "type": "string", "maxLength": 255, "minLength": 0}, "documentType": {"title": "Document Type", "type": "string", "maxLength": 255, "minLength": 0}, "esignatureAvailable": {"title": "e-Signature Available", "type": "boolean"}, "externalId": {"title": "External Id", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "lastModifiedAt": {"title": "Last Modified At", "type": "string", "format": "date-time"}, "lastModifiedBy": {"title": "Last Modified By", "type": "string", "maxLength": 255, "minLength": 0}, "tag": {"title": "Tag", "type": "string", "maxLength": 255, "minLength": 0}}}, "EnumerationOptions": {"type": "object", "title": "EnumerationOptions", "description": "Enumeration Options", "properties": {"code": {"title": "Code", "type": "string", "maxLength": 255, "minLength": 0}, "color": {"title": "Color", "type": "string", "maxLength": 255, "minLength": 0}, "description": {"title": "Description", "type": "string", "maxLength": 255, "minLength": 0}, "disabled": {"title": "Disabled", "type": "boolean"}, "domainName": {"title": "Domain Name", "type": "string", "maxLength": 255, "minLength": 0}, "enumerationName": {"title": "Enumeration Name", "type": "string", "maxLength": 255, "minLength": 0}, "icon": {"title": "Icon", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "isDefault": {"title": "<PERSON>", "type": "boolean"}, "optionLabel": {"title": "Option Label", "type": "string", "maxLength": 1500, "minLength": 0}, "optionOrder": {"title": "Option Order", "type": "integer"}, "optionValue": {"title": "Option Value", "type": "string", "maxLength": 255, "minLength": 0}, "orgUnitCode": {"title": "Org Unit code", "type": "string", "maxLength": 255, "minLength": 0}}}, "Error": {"type": "object", "title": "Error", "description": "Error", "properties": {"field": {"title": "field", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}}}, "EsignatureEnvelope": {"type": "object", "title": "EsignatureEnvelope", "description": "e-Signature Envelope", "properties": {"documents": {"title": "Documents", "type": "array", "items": {"$ref": "#/definitions/Document"}}, "description": {"title": "Description", "type": "string", "maxLength": 255, "minLength": 0}, "envelopeStatus": {"title": "Envelope Status", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}}}, "EsignaturePositions": {"type": "object", "title": "EsignaturePositions", "description": "ESignature Positions", "properties": {"datePageNumber": {"title": "Date Page Number", "type": "integer"}, "dateXPosition": {"title": "Date XPosition", "type": "integer"}, "dateYPosition": {"title": "Date YPosition", "type": "integer"}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "index": {"title": "Index", "type": "integer"}, "initialsPageNumber": {"title": "Initials Page Number", "type": "integer"}, "initialsXPosition": {"title": "Initials XPosition", "type": "integer"}, "initialsYPosition": {"title": "Initials YPosition", "type": "integer"}, "namePageNumber": {"title": "Name Page Number", "type": "integer"}, "nameXPosition": {"title": "Name XPosition", "type": "integer"}, "nameYPosition": {"title": "Name YPosition", "type": "integer"}, "rowNumber": {"title": "Row Number", "type": "integer"}, "signPageNumber": {"title": "Sign Page Number", "type": "integer"}, "signXPosition": {"title": "Sign XPosition", "type": "integer"}, "signYPosition": {"title": "Sign YPosition", "type": "integer"}, "signerType": {"title": "Signer Type", "type": "string", "maxLength": 255, "minLength": 0, "enum": ["Primary", "Advisor", "Secondary"]}}}, "Exchange": {"type": "object", "title": "Exchange", "description": "Exchange", "properties": {"bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "custodianCode": {"title": "Custodian Code", "type": "string", "maxLength": 255, "minLength": 0}, "description": {"title": "Description", "type": "string", "maxLength": 255, "minLength": 0}, "exchangeCode": {"title": "Exchange Code", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "name": {"title": "Name", "type": "string", "maxLength": 255, "minLength": 0}}}, "ExecutionHistory": {"type": "object", "title": "ExecutionHistory", "description": "Execution History", "properties": {"inputFiles": {"title": "Input Files", "type": "array", "items": {"$ref": "#/definitions/ReceivedFile"}}, "outputFiles": {"title": "Output Files", "type": "array", "items": {"$ref": "#/definitions/OutputFile"}}, "steps": {"title": "Steps", "type": "array", "items": {"$ref": "#/definitions/ExecutionHistoryStep"}}, "asOfDate": {"title": "As of Date", "type": "string", "format": "date"}, "createdAt": {"title": "Created At", "type": "string", "format": "date-time"}, "createdBy": {"title": "Created By", "type": "string", "maxLength": 255, "minLength": 0}, "executionTime": {"title": "Execution Time", "type": "string", "pattern": "^([0-1][0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$", "minimum": "00:00:00", "maximum": "23:59:59"}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "jobName": {"title": "Job Name", "type": "string", "maxLength": 255, "minLength": 0}, "lastModifiedAt": {"title": "Last Modified At", "type": "string", "format": "date-time"}, "lastModifiedBy": {"title": "Last Modified By", "type": "string", "maxLength": 255, "minLength": 0}, "message": {"title": "Message", "type": "string", "maxLength": 255, "minLength": 0}, "status": {"title": "Status", "type": "string", "maxLength": 255, "minLength": 0}, "stepType": {"title": "Step Type", "type": "string", "maxLength": 255, "minLength": 0}}}, "ExecutionHistoryStep": {"type": "object", "title": "ExecutionHistoryStep", "description": "Execution History Step", "properties": {"expectedFiles": {"title": "Expected Files", "type": "array", "items": {"$ref": "#/definitions/OutputFile"}}, "createdAt": {"title": "Created At", "type": "string", "format": "date-time"}, "createdBy": {"title": "Created By", "type": "string", "maxLength": 255, "minLength": 0}, "executionTime": {"title": "Execution Time", "type": "string", "pattern": "^([0-1][0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$", "minimum": "00:00:00", "maximum": "23:59:59"}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "lastModifiedAt": {"title": "Last Modified At", "type": "string", "format": "date-time"}, "lastModifiedBy": {"title": "Last Modified By", "type": "string", "maxLength": 255, "minLength": 0}, "message": {"title": "Message", "type": "string", "maxLength": 255, "minLength": 0}, "status": {"title": "Status", "type": "string", "maxLength": 255, "minLength": 0}, "stepName": {"title": "Step Name", "type": "string", "maxLength": 255, "minLength": 0}, "stepType": {"title": "Step Type", "type": "string", "maxLength": 255, "minLength": 0}}}, "ExpectedFile": {"type": "object", "title": "ExpectedFile", "description": "Expected File", "properties": {"businessObject": {"title": "Business Object", "type": "string", "maxLength": 255, "minLength": 0}, "domainName": {"title": "Domain Name", "type": "string", "maxLength": 255, "minLength": 0}, "expectedDays": {"title": "Expected Days", "type": "string", "maxLength": 255, "minLength": 0}, "expectedTime": {"title": "Expected Time", "type": "string", "format": "date-time"}, "folder": {"title": "Folder", "type": "string", "maxLength": 255, "minLength": 0}, "frequency": {"title": "Frequency", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "name": {"title": "Name", "type": "string", "maxLength": 255, "minLength": 0}, "namePattern": {"title": "Name Pattern", "type": "string", "maxLength": 255, "minLength": 0}, "timeout": {"title": "Timeout", "type": "string", "pattern": "^([0-1][0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$", "minimum": "00:00:00", "maximum": "23:59:59"}}}, "FeeSchedule": {"type": "object", "title": "FeeSchedule", "description": "Fee Schedule", "properties": {"discount": {"title": "Discount", "type": "number", "maximum": 100.0, "minimum": 0.0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "minimumFees": {"title": "Minimum Fees", "type": "integer"}, "name": {"title": "Name", "type": "string", "maxLength": 255, "minLength": 0}, "tier1": {"title": "Tier 1", "type": "integer"}, "tier1Rate": {"title": "Tier 1 Rate", "type": "number", "maximum": 100.0, "minimum": 0.0}, "tier2": {"title": "Tier 2", "type": "integer"}, "tier2Rate": {"title": "Tier 2 Rate", "type": "number", "maximum": 100.0, "minimum": 0.0}, "tier3": {"title": "Tier 3", "type": "integer"}, "tier3Rate": {"title": "Tier 3 Rate", "type": "number", "maximum": 100.0, "minimum": 0.0}, "tier4": {"title": "Tier 4", "type": "integer"}, "tier4Rate": {"title": "Tier 4 Rate", "type": "number", "maximum": 100.0, "minimum": 0.0}, "tier5": {"title": "Tier 5", "type": "integer"}, "tier5Rate": {"title": "Tier 5 Rate", "type": "number", "maximum": 100.0, "minimum": 0.0}, "tieredRate": {"title": "Tiered Rate", "type": "boolean"}, "topTierRate": {"title": "Top Tier Rate", "type": "number", "maximum": 100.0, "minimum": 0.0}}}, "FinancialGoal": {"type": "object", "title": "FinancialGoal", "description": "Financial Goal", "properties": {"accounts": {"title": "Accounts", "type": "array", "items": {"$ref": "#/definitions/Account"}}, "extRetirementGoal": {"title": "(Ext) Retirement Goal", "type": "object", "$ref": "#/definitions/RetirementGoal"}, "participants": {"title": "Participants", "type": "array", "items": {"$ref": "#/definitions/Person"}}, "retirementState": {"title": "Retirement State", "type": "object", "$ref": "#/definitions/StateOrProvince"}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "endYear": {"title": "End Year", "type": "integer"}, "goalAmount": {"title": "Goal Amount", "type": "number"}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "initialContribution": {"title": "Initial Contribution", "type": "number"}, "investmentModel": {"title": "Investment Model", "type": "string", "maxLength": 255, "minLength": 0}, "livingExpenses": {"title": "Living Expenses", "type": "number"}, "name": {"title": "Name", "type": "string", "maxLength": 255, "minLength": 0}, "recurringContribution": {"title": "Recurring Contribution", "type": "number"}, "recurringContributionFrequency": {"title": "Recurring Contribution Frequency", "type": "string", "maxLength": 255, "minLength": 0}, "recurringContributionRate": {"title": "Recurring Contribution Rate", "type": "number", "maximum": 100.0, "minimum": 0.0}, "riskLevel": {"title": "Risk Level", "type": "string", "maxLength": 255, "minLength": 0}, "startYear": {"title": "Start Year", "type": "integer"}, "type": {"title": "Type", "type": "string", "maxLength": 255, "minLength": 0}}}, "FinancialInstitution": {"type": "object", "title": "FinancialInstitution", "description": "Financial institution", "properties": {"additionalRoutingNumbers": {"title": "Additional Routing Numbers", "type": "array", "items": {"$ref": "#/definitions/RoutingNumber"}}, "address": {"title": "Address", "type": "object", "$ref": "#/definitions/Address"}, "organizationalUnit": {"title": "OrganizationalUnit", "type": "object", "$ref": "#/definitions/OrganizationalUnit"}, "bankCK2vyw": {"title": "Bank Code", "type": "string", "maxLength": 255, "minLength": 0}, "bankCode": {"title": "bankCode", "type": "string", "maxLength": 255, "minLength": 0}, "domesticWireRoutingNumber": {"title": "Domestic Wire Routing Number", "type": "string", "maxLength": 255, "minLength": 0}, "fax": {"title": "Fax", "type": "string", "maxLength": 50, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "internationalWireRoutingNumber": {"title": "International Wire Routing Number", "type": "integer"}, "name": {"title": "Name", "type": "string", "maxLength": 255, "minLength": 0}, "primaryPhoneNumber": {"title": "Primary Phone Number", "type": "string", "maxLength": 50, "minLength": 0}, "secondaryPhoneNumber": {"title": "Secondary Phone Number", "type": "string", "maxLength": 50, "minLength": 0}, "swiftCode": {"title": "Swift code", "type": "integer"}}}, "FormGenerationCriteria": {"type": "object", "title": "FormGenerationCriteria", "description": "Form Generation Criteria", "properties": {"esignatureDetails": {"title": "ESignature Details", "type": "object", "$ref": "#/definitions/EsignaturePositions"}, "client": {"title": "Client", "type": "string", "maxLength": 255, "minLength": 0}, "displayOrder": {"title": "Display Order", "type": "integer"}, "docCategory": {"title": "Doc Category", "type": "string", "maxLength": 255, "minLength": 0}, "docTypeName": {"title": "Doc Type Name", "type": "string", "maxLength": 255, "minLength": 0}, "domainModel": {"title": "Domain Model", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "label": {"title": "Label", "type": "string", "maxLength": 255, "minLength": 0}, "rawFile": {"title": "RawFile", "type": "string", "maxLength": 2096}, "serviceRequestLabel": {"title": "Service Request Label", "type": "string", "maxLength": 255, "minLength": 0}, "uploadDocumentToCustodian": {"title": "Upload Document to Custodian", "type": "boolean"}}}, "FormsByRegistrationType": {"type": "object", "title": "FormsByRegistrationType", "description": "Forms by Registration Type", "properties": {"conditions": {"title": "Conditions", "type": "string", "maxLength": 255, "minLength": 0}, "custodian": {"title": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "maxLength": 255, "minLength": 0}, "form": {"title": "Form", "type": "string", "maxLength": 2096}, "formName": {"title": "Form Name", "type": "string", "maxLength": 255, "minLength": 0}, "formType": {"title": "Form Type", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "registrationType": {"title": "Registration Type", "type": "string", "maxLength": 255, "minLength": 0}}}, "FundingMethodTypes": {"type": "object", "title": "FundingMethodTypes", "description": "Funding Method Types", "properties": {"description": {"title": "Description", "type": "string", "maxLength": 2000, "minLength": 0}, "fundingMethod": {"title": "Funding method", "type": "string", "maxLength": 255, "minLength": 0}, "icon": {"title": "Icon", "type": "string", "maxLength": 2096}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "sortOrder": {"title": "Sort order", "type": "integer"}}}, "GenericErrorResponse": {"type": "object", "title": "GenericErrorResponse", "description": "GenericErrorResponse", "properties": {"data": {"title": "data", "type": "string", "maxLength": 2000, "minLength": 0}, "error": {"title": "error", "type": "string", "maxLength": 2000, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}}}, "GetRCHKResponse": {"type": "object", "title": "GetRCHKResponse", "description": "Get RCHK Response", "properties": {"accountNumber": {"title": "accountNumber", "type": "string", "maxLength": 255, "minLength": 0}, "accountType": {"title": "accountType", "type": "string", "maxLength": 255, "minLength": 0}, "alternateNameAndAddressOrigin": {"title": "alternateNameAndAddressOrigin", "type": "string", "maxLength": 255, "minLength": 0}, "approver1Date": {"title": "approver1Date", "type": "string", "maxLength": 255, "minLength": 0}, "approver1Initials": {"title": "approver1Initials", "type": "string", "maxLength": 255, "minLength": 0}, "approver1WhoCode": {"title": "approver1WhoCode", "type": "string", "maxLength": 255, "minLength": 0}, "approver2Date": {"title": "approver2Date", "type": "string", "maxLength": 255, "minLength": 0}, "approver2Initials": {"title": "approver2Initials", "type": "string", "maxLength": 255, "minLength": 0}, "approver2WhoCode": {"title": "approver2WhoCode", "type": "string", "maxLength": 255, "minLength": 0}, "approver3Date": {"title": "approver3Date", "type": "string", "maxLength": 255, "minLength": 0}, "approver3Initials": {"title": "approver3Initials", "type": "string", "maxLength": 255, "minLength": 0}, "approver3WhoCode": {"title": "approver3WhoCode", "type": "string", "maxLength": 255, "minLength": 0}, "asOfFuturePayDate": {"title": "asOfFuturePayDate", "type": "string", "maxLength": 255, "minLength": 0}, "assignedWirePrinterId": {"title": "assignedWirePrinterId", "type": "string", "maxLength": 255, "minLength": 0}, "autoAlignmentSwitch": {"title": "autoAlignmentSwitch", "type": "string", "maxLength": 255, "minLength": 0}, "bankNumber": {"title": "bankNumber", "type": "string", "maxLength": 255, "minLength": 0}, "branchPrintSwitch": {"title": "branchPrintSwitch", "type": "string", "maxLength": 255, "minLength": 0}, "branchRepSeparationBranch": {"title": "branchRepSeparationBranch", "type": "string", "maxLength": 255, "minLength": 0}, "cashStandingInstructionCodes": {"title": "cashStandingInstructionCodes", "type": "string", "maxLength": 255, "minLength": 0}, "checkApprovalOverride": {"title": "checkApprovalOverride", "type": "string", "maxLength": 255, "minLength": 0}, "checkInstructions1": {"title": "checkInstructions1", "type": "string", "maxLength": 255, "minLength": 0}, "checkInstructions2": {"title": "checkInstructions2", "type": "string", "maxLength": 255, "minLength": 0}, "checkNextBusinessDate": {"title": "checkNextBusinessDate", "type": "string", "maxLength": 255, "minLength": 0}, "checkPrintedBranch": {"title": "checkPrintedBranch", "type": "string", "maxLength": 255, "minLength": 0}, "checkPrintedDate": {"title": "checkPrintedDate", "type": "string", "maxLength": 255, "minLength": 0}, "checkPrinterDescription": {"title": "checkPrinterDescription", "type": "string", "maxLength": 255, "minLength": 0}, "checkPrinterFeedSettings": {"title": "checkPrinterFeedSettings", "type": "string", "maxLength": 255, "minLength": 0}, "checkPrinterId": {"title": "checkPrinterId", "type": "string", "maxLength": 255, "minLength": 0}, "checkPrinterStatus": {"title": "checkPrinterStatus", "type": "string", "maxLength": 255, "minLength": 0}, "checkPurpose": {"title": "checkPurpose", "type": "string", "maxLength": 255, "minLength": 0}, "checkRequestDate": {"title": "checkRequestDate", "type": "string", "maxLength": 255, "minLength": 0}, "checkStatusCode": {"title": "checkStatusCode", "type": "string", "maxLength": 255, "minLength": 0}, "checkSystemType": {"title": "checkSystemType", "type": "string", "maxLength": 255, "minLength": 0}, "checkType": {"title": "checkType", "type": "string", "maxLength": 255, "minLength": 0}, "controlNumber": {"title": "controlNumber", "type": "string", "maxLength": 255, "minLength": 0}, "departmentCode": {"title": "departmentCode", "type": "string", "maxLength": 255, "minLength": 0}, "detailSourceCode": {"title": "detailSourceCode", "type": "string", "maxLength": 255, "minLength": 0}, "federalTaxWithholdingPercentage": {"title": "federalTaxWithholdingPercentage", "type": "string", "maxLength": 255, "minLength": 0}, "federalWithholdingTypeCode": {"title": "federalWithholdingTypeCode", "type": "string", "maxLength": 255, "minLength": 0}, "fifthNameAndAddressLine": {"title": "fifthNameAndAddressLine", "type": "string", "maxLength": 255, "minLength": 0}, "firmDefinedGlopProfileCode": {"title": "firmDefinedGlopProfileCode", "type": "string", "maxLength": 255, "minLength": 0}, "firstNameAndAddressLine": {"title": "firstNameAndAddressLine", "type": "string", "maxLength": 255, "minLength": 0}, "fourthNameAndAddressLine": {"title": "fourthNameAndAddressLine", "type": "string", "maxLength": 255, "minLength": 0}, "historicalStatusIndicator": {"title": "historicalStatusIndicator", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "itemNumberOfPayeeInstructions": {"title": "itemNumberOfPayeeInstructions", "type": "string", "maxLength": 255, "minLength": 0}, "lastCheckNumberPrinted": {"title": "lastCheckNumberPrinted", "type": "string", "maxLength": 255, "minLength": 0}, "lastUpdateDate": {"title": "lastUpdateDate", "type": "string", "maxLength": 255, "minLength": 0}, "lastUpdatedDepartmentCode": {"title": "lastUpdatedDepartmentCode", "type": "string", "maxLength": 255, "minLength": 0}, "marginStandingInstructionCode": {"title": "marginStandingInstructionCode", "type": "string", "maxLength": 255, "minLength": 0}, "messageFromApprover1": {"title": "messageFromApprover1", "type": "string", "maxLength": 255, "minLength": 0}, "messageFromApprover2": {"title": "messageFromApprover2", "type": "string", "maxLength": 255, "minLength": 0}, "messageToApprover1": {"title": "messageToApprover1", "type": "string", "maxLength": 255, "minLength": 0}, "messageToApprover2": {"title": "messageToApprover2", "type": "string", "maxLength": 255, "minLength": 0}, "mmfSymbol": {"title": "mmfSymbol", "type": "string", "maxLength": 255, "minLength": 0}, "originatorInitials": {"title": "originatorInitials", "type": "string", "maxLength": 255, "minLength": 0}, "portionOfCheckAmount": {"title": "portionOfCheckAmount", "type": "string", "maxLength": 255, "minLength": 0}, "rejectorWhoCode": {"title": "rejectorWhoCode", "type": "string", "maxLength": 255, "minLength": 0}, "repCode": {"title": "repCode", "type": "string", "maxLength": 255, "minLength": 0}, "requestUpdateInitials": {"title": "requestUpdateInitials", "type": "string", "maxLength": 255, "minLength": 0}, "reservedForFutureUse": {"title": "reservedForFutureUse", "type": "string", "maxLength": 255, "minLength": 0}, "rowIdentifier": {"title": "rowIdentifier", "type": "string", "maxLength": 255, "minLength": 0}, "secondNameAndAddressLine": {"title": "secondNameAndAddressLine", "type": "string", "maxLength": 255, "minLength": 0}, "sequenceNumber": {"title": "sequenceNumber", "type": "string", "maxLength": 255, "minLength": 0}, "sixthNameAndAddressLine": {"title": "sixthNameAndAddressLine", "type": "string", "maxLength": 255, "minLength": 0}, "sourceCode": {"title": "sourceCode", "type": "string", "maxLength": 255, "minLength": 0}, "stateTaxWithholdingPercentage": {"title": "stateTaxWithholdingPercentage", "type": "string", "maxLength": 255, "minLength": 0}, "stateWithholdingTypeCode": {"title": "stateWithholdingTypeCode", "type": "string", "maxLength": 255, "minLength": 0}, "subfirm": {"title": "subfirm", "type": "string", "maxLength": 255, "minLength": 0}, "taxId": {"title": "taxId", "type": "string", "maxLength": 255, "minLength": 0}, "thirdNameAndAddressLine": {"title": "thirdNameAndAddressLine", "type": "string", "maxLength": 255, "minLength": 0}, "transactionReferenceId": {"title": "transactionReferenceId", "type": "string", "maxLength": 255, "minLength": 0}, "typeOfPayee": {"title": "typeOfPayee", "type": "string", "maxLength": 255, "minLength": 0}, "uniqueTimestamp": {"title": "uniqueTimestamp", "type": "string", "maxLength": 255, "minLength": 0}, "whoCodeOfUserPrintedCheck": {"title": "whoCodeOfUserPrintedCheck", "type": "string", "maxLength": 255, "minLength": 0}, "wireNumber": {"title": "wireNumber", "type": "string", "maxLength": 255, "minLength": 0}}}, "Holdings": {"type": "object", "title": "Holdings", "description": "Holdings", "properties": {"employerSponsoredRetirementPlansValue": {"title": "Employer-Sponsored Retirement Plans Value", "type": "number"}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "irAValue": {"title": "IRA Value", "type": "number"}, "nonQualifiedAnnuitiesInsuranceValue": {"title": "Non-Qualified Annuities & Insurance Value", "type": "number"}, "nonQualifiedCashValue": {"title": "Non-Qualified Cash Value", "type": "number"}, "nonQualifiedNonRetirementInvestmentsValu": {"title": "Non-Qualified Non-Retirement Investments Value", "type": "number"}, "qualifiedAnnuitiesInsuranceValue": {"title": "Qualified Annuities & Insurance Value", "type": "number"}, "qualifiedCashValue": {"title": "Qualified Cash Value", "type": "number"}, "qualifiedNonRetirementInvestmentsValue": {"title": "Qualified Non-Retirement Investments Value", "type": "number"}}}, "IndexedAnnuityCreditingStrategy": {"type": "object", "title": "IndexedAnnuityCreditingStrategy", "description": "Indexed Annuity Crediting Strategy", "properties": {"bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "currentRate": {"title": "Current Rate", "type": "number", "maximum": 100.0, "minimum": 0.0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "interestCreditingStrategy": {"title": "Interest Crediting Strategy", "type": "string", "maxLength": 255, "minLength": 0}, "interestRateMargin": {"title": "Interest Rate Margin", "type": "number", "maximum": 100.0, "minimum": 0.0}, "minimumCapRate": {"title": "Minimum Cap Rate", "type": "number", "maximum": 100.0, "minimum": 0.0}, "participationRate": {"title": "Participation Rate", "type": "number", "maximum": 100.0, "minimum": 0.0}}}, "InterestRateChange": {"type": "object", "title": "InterestRateChange", "description": "Interest Rate Change", "properties": {"id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "rateChangeTrigger": {"title": "Rate Change Trigger", "type": "string", "maxLength": 255, "minLength": 0}, "triggerRateFactor": {"title": "Trigger Rate Factor", "type": "integer"}, "triggerRateVariation": {"title": "Trigger Rate Variation", "type": "integer"}}}, "InvestmentProgramType": {"type": "object", "title": "InvestmentProgramType", "description": "Investment Program Type", "properties": {"code": {"title": "Code", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "name": {"title": "Name", "type": "string", "maxLength": 255, "minLength": 0}}}, "IsCompleted": {"type": "object", "title": "IsCompleted", "description": "Is Completed", "properties": {"id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}}}, "J_Transaction": {"type": "object", "title": "J_Transaction", "description": "Transaction", "properties": {"account": {"title": "Account", "type": "object", "$ref": "#/definitions/Account"}, "ouLevel0": {"title": "OU Level 0", "type": "object", "$ref": "#/definitions/OrganizationalUnit"}, "ouLevel1": {"title": "OU Level 1", "type": "object", "$ref": "#/definitions/OrganizationalUnit"}, "ouLevel2": {"title": "OU Level 2", "type": "object", "$ref": "#/definitions/OrganizationalUnit"}, "ouLevel3": {"title": "OU Level 3", "type": "object", "$ref": "#/definitions/OrganizationalUnit"}, "ouLevel4": {"title": "OU Level 4", "type": "object", "$ref": "#/definitions/OrganizationalUnit"}, "ouLevel5": {"title": "OU Level 5", "type": "object", "$ref": "#/definitions/OrganizationalUnit"}, "ouLevel6": {"title": "OU Level 6", "type": "object", "$ref": "#/definitions/OrganizationalUnit"}, "security": {"title": "Security", "type": "object", "$ref": "#/definitions/Security"}, "accountNumber": {"title": "Account Number", "type": "string", "maxLength": 255, "minLength": 0}, "amount": {"title": "Amount", "type": "number"}, "blotterCode": {"title": "Blotter Code", "type": "string", "maxLength": 255, "minLength": 0}, "branchOrOfficeID": {"title": "Branch or Office ID", "type": "string", "maxLength": 255, "minLength": 0}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "cancelCode": {"title": "Cancel Code", "type": "string", "maxLength": 255, "minLength": 0}, "category": {"title": "Category", "type": "string", "maxLength": 255, "minLength": 0}, "categorySource": {"title": "Category Source", "type": "string", "maxLength": 255, "minLength": 0}, "checkOrTradeTicketNumber": {"title": "Check or trade ticket number", "type": "string", "maxLength": 255, "minLength": 0}, "code": {"title": "Code", "type": "string", "maxLength": 255, "minLength": 0}, "commission": {"title": "Commission", "type": "number"}, "contributionOrWithdrawal": {"title": "Contribution or Withdrawal", "type": "string", "maxLength": 255, "minLength": 0}, "creditOrDebit": {"title": "Credit or Debit", "type": "string", "maxLength": 255, "minLength": 0}, "currencyCode": {"title": "Currency Code", "type": "string", "maxLength": 255, "minLength": 0}, "cusip": {"title": "CUSIP", "type": "string", "maxLength": 255, "minLength": 0}, "custodianAssignedSecurityID": {"title": "Custodian Assigned Security ID", "type": "string", "maxLength": 255, "minLength": 0}, "custodianCharge": {"title": "Custodian Charge", "type": "number"}, "custodianName": {"title": "Custodian Name", "type": "string", "maxLength": 255, "minLength": 0}, "custodianRepCode": {"title": "Custodian Rep Code", "type": "string", "maxLength": 255, "minLength": 0}, "dealerCommissionRate": {"title": "Dealer Commission Rate", "type": "number", "maximum": 100.0, "minimum": 0.0}, "enteredBy": {"title": "Entered by", "type": "string", "maxLength": 255, "minLength": 0}, "feeAmount": {"title": "<PERSON><PERSON> Amount", "type": "number"}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "initialSourceOfFunds": {"title": "Initial Source of Funds", "type": "string", "maxLength": 255, "minLength": 0}, "intermediary": {"title": "intermediary", "type": "string", "maxLength": 255, "minLength": 0}, "intraDayTimestamp": {"title": "Intra-day Timestamp", "type": "string", "format": "date-time"}, "isPhysical": {"title": "Is Physical", "type": "boolean"}, "isSolicited": {"title": "Is Solicited", "type": "boolean"}, "isTradeTransaction": {"title": "Is Trade Transaction", "type": "boolean"}, "isin": {"title": "ISIN", "type": "string", "maxLength": 255, "minLength": 0}, "journalInOrOut": {"title": "Journal in or Out", "type": "string", "maxLength": 255, "minLength": 0}, "longDescription": {"title": "Long Description", "type": "string", "maxLength": 2000, "minLength": 0}, "marketCode": {"title": "Market Code", "type": "string", "maxLength": 255, "minLength": 0}, "merchant": {"title": "Merchant", "type": "string", "maxLength": 255, "minLength": 0}, "merchantType": {"title": "Merchant Type", "type": "string", "maxLength": 255, "minLength": 0}, "mutualFundValues": {"title": "Mutual Fund Values", "type": "string", "maxLength": 255, "minLength": 0}, "orderType": {"title": "Order Type", "type": "string", "maxLength": 255, "minLength": 0}, "originalCommission": {"title": "Original Commission", "type": "number"}, "postDate": {"title": "Post Date", "type": "string", "format": "date"}, "price": {"title": "Price", "type": "number", "multipleOf": 1e-05}, "principal": {"title": "Principal", "type": "number"}, "processedDate": {"title": "Processed Date", "type": "string", "format": "date"}, "quantity": {"title": "Quantity", "type": "number", "multipleOf": 1e-05}, "recordSource": {"title": "Record Source", "type": "string", "maxLength": 255, "minLength": 0}, "repCode": {"title": "Rep Code", "type": "string", "maxLength": 255, "minLength": 0}, "runningBalaJe4tm": {"title": "Running Balance", "type": "number"}, "runningBalance": {"title": "runningBalance", "type": "number"}, "securityDescription": {"title": "Security Description", "type": "string", "maxLength": 255, "minLength": 0}, "sedol": {"title": "SEDOL", "type": "string", "maxLength": 255, "minLength": 0}, "settlementDate": {"title": "Settlement Date", "type": "string", "format": "date"}, "shortDescription": {"title": "Short Description", "type": "string", "maxLength": 255, "minLength": 0}, "status": {"title": "status", "type": "string", "maxLength": 255, "minLength": 0}, "symbol": {"title": "Symbol", "type": "string", "maxLength": 255, "minLength": 0}, "tradeDate": {"title": "Trade Date", "type": "string", "format": "date"}, "transactionCode": {"title": "Transaction Code", "type": "string", "maxLength": 255, "minLength": 0}, "transactionDate": {"title": "Transaction Date", "type": "string", "format": "date"}, "transactionID": {"title": "Transaction ID", "type": "string", "maxLength": 255, "minLength": 0}, "transactionRecordSource": {"title": "Transaction Record Source", "type": "string", "maxLength": 255, "minLength": 0}, "transactionType": {"title": "Transaction Type", "type": "string", "maxLength": 255, "minLength": 0}, "valoren": {"title": "<PERSON><PERSON><PERSON>", "type": "string", "maxLength": 255, "minLength": 0}}}, "KeyValuePair": {"type": "object", "title": "KeyValuePair", "description": "KeyValuePair", "properties": {"id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "key": {"title": "key", "type": "string", "maxLength": 255, "minLength": 0}, "value": {"title": "value", "type": "string", "maxLength": 255, "minLength": 0}}}, "KyCInfo": {"type": "object", "title": "KyCInfo", "description": "KYC Info", "properties": {"asOfDate": {"title": "As of Date", "type": "string", "format": "date"}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "description": {"title": "Description", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "kyCStatus": {"title": "KYC Status", "type": "string", "maxLength": 255, "minLength": 0}, "kyCVerificationType": {"title": "KYC Verification Type", "type": "string", "maxLength": 255, "minLength": 0}}}, "Liabilities": {"type": "object", "title": "Liabilities", "description": "Liabilities", "properties": {"businessLoanBalance": {"title": "Business Loan Balance", "type": "number"}, "creditCardBalances": {"title": "Credit Card Balances", "type": "number"}, "homeEquityLineOfCreditBalance": {"title": "Home Equity Line of Credit Balance", "type": "number"}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "mortgageBalance": {"title": "Mortgage Balance", "type": "number"}, "otherLiabilities": {"title": "Other Liabilities", "type": "string", "maxLength": 255, "minLength": 0}, "otherLiabilitiesAmount": {"title": "Other Liabilities Amount", "type": "number"}, "secondMortgageBalance": {"title": "Second Mortgage Balance", "type": "number"}, "studentLoanBalances": {"title": "Student Loan Ba<PERSON>s", "type": "number"}, "vehicleLoanBalance": {"title": "Vehicle Loan Balance", "type": "number"}}}, "MarginInterest": {"type": "object", "title": "MarginInterest", "description": "<PERSON>gin <PERSON>", "properties": {"account": {"title": "Account", "type": "object", "$ref": "#/definitions/Account"}, "asOfDate": {"title": "As of Date", "type": "string", "format": "date"}, "branchMarginInterest": {"title": "Branch Margin Interest", "type": "number"}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "interest": {"title": "Interest", "type": "number"}, "isBulkPost": {"title": "Is Bulk Post", "type": "boolean"}, "marginMTD": {"title": "<PERSON>gin MTD", "type": "number"}, "postDate": {"title": "Post Date", "type": "string", "format": "date"}, "postUserCode": {"title": "Post User Code", "type": "string", "maxLength": 255, "minLength": 0}}}, "MarginRates": {"type": "object", "title": "MarginRates", "description": "Margin Rates", "properties": {"bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "rangeMaximum": {"title": "Range Maximum", "type": "number"}, "rangeMinimum": {"title": "Range Minimum", "type": "number"}, "rate": {"title": "Rate", "type": "number", "maximum": 100.0, "minimum": 0.0}, "rateDelta": {"title": "Rate Delta", "type": "number", "maximum": 100.0, "minimum": 0.0}, "rateDeltaIsPositive": {"title": "Rate Delta Is Positive", "type": "boolean"}}}, "MarginRequirements": {"type": "object", "title": "MarginRequirements", "description": "Margin Requirements", "properties": {"orgUnit": {"title": "Org Unit", "type": "object", "$ref": "#/definitions/OrganizationalUnit"}, "asOfDate": {"title": "As Of Date", "type": "string", "format": "date"}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "category": {"title": "Category", "type": "string", "maxLength": 255, "minLength": 0}, "firmMarketValueRequired": {"title": "Firm Market Value Required", "type": "number", "maximum": 100.0, "minimum": 0.0}, "firmPrincipalRequired": {"title": "Firm Principal Required", "type": "number", "maximum": 100.0, "minimum": 0.0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "maintenancePoints": {"title": "Maintenance Points", "type": "number", "multipleOf": 1e-09}, "marginCallThreshold": {"title": "<PERSON>gin Call Threshold", "type": "number", "maximum": 100.0, "minimum": 0.0}, "priceBreak": {"title": "Price Break", "type": "number", "multipleOf": 1e-09}, "regulationTMarketValueRequired": {"title": "Regulation-T Market Value Required", "type": "number", "maximum": 100.0, "minimum": 0.0}, "regulationTPrincipalRequired": {"title": "Regulation-T Principal Required", "type": "number", "maximum": 100.0, "minimum": 0.0}}}, "MultiAccountBuilder": {"type": "object", "title": "MultiAccountBuilder", "description": "Multi Account Builder", "properties": {"accountCreationList": {"title": "Account Creation List", "type": "array", "items": {"$ref": "#/definitions/AccountBuilder"}}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}}}, "MultiAccountCreation": {"type": "object", "title": "MultiAccountCreation", "description": "Multi Account Creation", "properties": {"accountCreationList": {"title": "Account Creation List", "type": "array", "items": {"$ref": "#/definitions/AccountCreation"}}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}}}, "NoteResponse": {"type": "object", "title": "NoteResponse", "description": "NoteResponse", "properties": {"accountNumber": {"title": "accountNumber", "type": "string", "maxLength": 255, "minLength": 0}, "addTimestamp": {"title": "addTimestamp", "type": "string", "maxLength": 255, "minLength": 0}, "alphaKey": {"title": "alphaKey", "type": "string", "maxLength": 255, "minLength": 0}, "changeDateCYMD": {"title": "changeDateCYMD", "type": "string", "maxLength": 255, "minLength": 0}, "changeWhoCode": {"title": "changeWhoCode", "type": "string", "maxLength": 255, "minLength": 0}, "effectiveDateCYMD": {"title": "effectiveDateCYMD", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "note": {"title": "note", "type": "string", "maxLength": 255, "minLength": 0}, "padType": {"title": "padType", "type": "string", "maxLength": 255, "minLength": 0}}}, "OnlineAccountSecurityQA": {"type": "object", "title": "OnlineAccountSecurityQA", "description": "Online Account Security Q & A", "properties": {"answer": {"title": "Answer", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "question": {"title": "Question", "type": "string", "maxLength": 255, "minLength": 0}}}, "OnlineAccountSecurityQuestions": {"type": "object", "title": "OnlineAccountSecurityQuestions", "description": "Online Account Security Questions", "properties": {"enabled": {"title": "Enabled", "type": "boolean"}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "question": {"title": "Question", "type": "string", "maxLength": 255, "minLength": 0}}}, "OnlineAccountSetup": {"type": "object", "title": "OnlineAccountSetup", "description": "Online Account Setup", "properties": {"accountsToLink": {"title": "Accounts to <PERSON>", "type": "array", "items": {"$ref": "#/definitions/Account"}}, "person": {"title": "Person", "type": "object", "$ref": "#/definitions/Person"}, "securityQuestionsAndAnswers": {"title": "Security Questions and Answers", "type": "array", "items": {"$ref": "#/definitions/OnlineAccountSecurityQA"}}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "firstChoiceForUserID": {"title": "First Choice for User ID", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "secondChoiceForUserID": {"title": "Second Choice for User ID", "type": "string", "maxLength": 255, "minLength": 0}}}, "OrgUnitAccountBalances": {"type": "object", "title": "OrgUnitAccountBalances", "description": "Org Unit Account Balances", "properties": {"orgUnit": {"title": "Org Unit", "type": "object", "$ref": "#/definitions/OrganizationalUnit"}, "ouLevel0": {"title": "OU Level 0", "type": "object", "$ref": "#/definitions/OrganizationalUnit"}, "ouLevel1": {"title": "OU Level 1", "type": "object", "$ref": "#/definitions/OrganizationalUnit"}, "ouLevel2": {"title": "OU Level 2", "type": "object", "$ref": "#/definitions/OrganizationalUnit"}, "ouLevel3": {"title": "OU Level 3", "type": "object", "$ref": "#/definitions/OrganizationalUnit"}, "ouLevel4": {"title": "OU Level 4", "type": "object", "$ref": "#/definitions/OrganizationalUnit"}, "ouLevel5": {"title": "OU Level 5", "type": "object", "$ref": "#/definitions/OrganizationalUnit"}, "ouLevel6": {"title": "OU Level 6", "type": "object", "$ref": "#/definitions/OrganizationalUnit"}, "beginningAccounts": {"title": "Beginning Accounts", "type": "integer"}, "beginningBuyingPower": {"title": "Beginning Buying Power", "type": "number"}, "beginningCashBalance": {"title": "Beginning Cash Balance", "type": "number"}, "beginningEts": {"title": "Beginning Assets", "type": "number"}, "beginningMarginBalance": {"title": "Beginning Margin Balance", "type": "number"}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "closedAccounts": {"title": "Closed Accounts", "type": "integer"}, "contributions": {"title": "Contributions", "type": "number"}, "endingAccounts": {"title": "Ending Accounts", "type": "integer"}, "endingBuyingPower": {"title": "Ending Buying Power", "type": "number"}, "endingCashBalance": {"title": "Ending Cash Balance", "type": "number"}, "endingEts": {"title": "Ending Assets", "type": "number"}, "endingMarginBalance": {"title": "Ending <PERSON><PERSON>", "type": "number"}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "newAccounts": {"title": "New Accounts", "type": "integer"}, "periodEndDate": {"title": "Period End Date", "type": "string", "format": "date"}, "periodStartDate": {"title": "Period Start Date", "type": "string", "format": "date"}, "periodType": {"title": "Period Type", "type": "string", "maxLength": 255, "minLength": 0}, "withdrawals": {"title": "<PERSON><PERSON><PERSON><PERSON>", "type": "number"}}}, "OrgUnitPositions": {"type": "object", "title": "OrgUnitPositions", "description": "Org Unit Positions", "properties": {"orgUnit": {"title": "Org Unit", "type": "object", "$ref": "#/definitions/OrganizationalUnit"}, "ouLevel0": {"title": "OU Level 0", "type": "object", "$ref": "#/definitions/OrganizationalUnit"}, "ouLevel1": {"title": "OU Level 1", "type": "object", "$ref": "#/definitions/OrganizationalUnit"}, "ouLevel2": {"title": "OU Level 2", "type": "object", "$ref": "#/definitions/OrganizationalUnit"}, "ouLevel3": {"title": "OU Level 3", "type": "object", "$ref": "#/definitions/OrganizationalUnit"}, "ouLevel4": {"title": "OU Level 4", "type": "object", "$ref": "#/definitions/OrganizationalUnit"}, "ouLevel5": {"title": "OU Level 5", "type": "object", "$ref": "#/definitions/OrganizationalUnit"}, "ouLevel6": {"title": "OU Level 6", "type": "object", "$ref": "#/definitions/OrganizationalUnit"}, "security": {"title": "Security", "type": "object", "$ref": "#/definitions/Security"}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "marketValue": {"title": "Market Value", "type": "number"}, "numberOfAccounts": {"title": "Number of accounts", "type": "integer"}, "numberOfShares": {"title": "Number of shares", "type": "number", "multipleOf": 1e-05}, "periodEndDate": {"title": "Period End Date", "type": "string", "format": "date"}, "securityCategory": {"title": "Security Category", "type": "string"}}}, "OrgUnitRelation": {"type": "object", "title": "OrgUnitRelation", "description": "Org Unit Relation", "properties": {"childUnit": {"title": "Child Unit", "type": "object", "$ref": "#/definitions/OrganizationalUnit"}, "parentUnit": {"title": "Parent Unit", "type": "object", "$ref": "#/definitions/OrganizationalUnit"}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}}}, "OrgUnitType": {"type": "object", "title": "OrgUnitType", "description": "Org Unit Type", "properties": {"id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "level": {"title": "Level", "type": "integer"}, "name": {"title": "Name", "type": "string", "maxLength": 255, "minLength": 0}}}, "OrganizationalUnit": {"type": "object", "title": "OrganizationalUnit", "description": "Organizational Unit", "properties": {"address": {"title": "Address", "type": "object", "$ref": "#/definitions/Address"}, "childUnits": {"title": "Child Units", "type": "array", "items": {"$ref": "#/definitions/OrgUnitRelation"}}, "alternateEmail": {"title": "Alternate Email", "type": "string", "maxLength": 50, "minLength": 0, "pattern": "^[a-zA-Z0-9.!#$%&'*+\\=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\\.[a-zA-Z0-9-]+)*$"}, "billingCode": {"title": "Billing Code", "type": "string", "maxLength": 255, "minLength": 0}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "code": {"title": "Code", "type": "string", "maxLength": 255, "minLength": 0}, "description": {"title": "Description", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "name": {"title": "Name", "type": "string", "maxLength": 255, "minLength": 0}, "omsId": {"title": "OMS ID", "type": "string", "maxLength": 255, "minLength": 0}, "parentCode": {"title": "Parent Code", "type": "string", "maxLength": 255, "minLength": 0}, "primaryEmail": {"title": "Primary Email", "type": "string", "maxLength": 50, "minLength": 0, "pattern": "^[a-zA-Z0-9.!#$%&'*+\\=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\\.[a-zA-Z0-9-]+)*$"}, "primaryPhoneNumber": {"title": "Primary Phone Number", "type": "string", "maxLength": 50, "minLength": 0}, "secondaryPhoneNumber": {"title": "Secondary Phone Number", "type": "string", "maxLength": 50, "minLength": 0}, "taxID": {"title": "Tax ID", "type": "string", "maxLength": 255, "minLength": 0}, "type": {"title": "Type", "type": "string", "maxLength": 255, "minLength": 0}}}, "OutputFile": {"type": "object", "title": "OutputFile", "description": "Output File", "properties": {"expectedFiles": {"title": "Expected Files", "type": "array", "items": {"$ref": "#/definitions/ExpectedFile"}}, "createdAt": {"title": "Created At", "type": "string", "format": "date-time"}, "createdBy": {"title": "Created By", "type": "string", "maxLength": 255, "minLength": 0}, "domainName": {"title": "Domain Name", "type": "string", "maxLength": 255, "minLength": 0}, "fileName": {"title": "File Name", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "isNIGO": {"title": "Is NIGO", "type": "boolean"}, "j_Size": {"title": "Size", "type": "integer"}, "lastModifiedAt": {"title": "Last Modified At", "type": "string", "format": "date-time"}, "lastModifiedBy": {"title": "Last Modified By", "type": "string", "maxLength": 255, "minLength": 0}, "name": {"title": "Name", "type": "string", "maxLength": 255, "minLength": 0}, "rows": {"title": "Rows", "type": "integer"}, "targetBusinessObject": {"title": "Target Business Object", "type": "string", "maxLength": 255, "minLength": 0}}}, "Person": {"type": "object", "title": "Person", "description": "Person", "properties": {"accreditedInvestor": {"title": "Accredited Investor", "type": "object", "$ref": "#/definitions/RegulatoryDisclosure"}, "alternateMailingAddress": {"title": "Alternate Mailing Address", "type": "object", "$ref": "#/definitions/Address"}, "assets": {"title": "Assets", "type": "object", "$ref": "#/definitions/Assets"}, "assignedBranch": {"title": "Assigned Branch", "type": "object", "$ref": "#/definitions/Branch"}, "associatedWithBrokerDealer": {"title": "Associated with Broker-Dealer", "type": "object", "$ref": "#/definitions/RegulatoryDisclosure"}, "associatedWithInvestmentAdvisor": {"title": "Associated with Investment Advisor", "type": "object", "$ref": "#/definitions/RegulatoryDisclosure"}, "associatedWithOtherBrokerDealer": {"title": "Associated with other Broker-Dealer", "type": "object", "$ref": "#/definitions/RegulatoryDisclosure"}, "countryOfCitizenship": {"title": "Country of Citizenship", "type": "object", "$ref": "#/definitions/Country"}, "countryOfResidence": {"title": "Country of Residence", "type": "object", "$ref": "#/definitions/Country"}, "employerAddress": {"title": "Employer Address", "type": "object", "$ref": "#/definitions/Address"}, "foreignOfficial": {"title": "Foreign Official", "type": "object", "$ref": "#/definitions/RegulatoryDisclosure"}, "governingStateLawForTrust": {"title": "Governing State Law for Trust", "type": "object", "$ref": "#/definitions/StateOrProvince"}, "grantor": {"title": "<PERSON><PERSON>", "type": "array", "items": {"$ref": "#/definitions/PersonRelation"}}, "holdings": {"title": "Holdings", "type": "object", "$ref": "#/definitions/Holdings"}, "kyCInfo": {"title": "KYC Info", "type": "array", "items": {"$ref": "#/definitions/KyCInfo"}}, "legalAddress": {"title": "Legal Address", "type": "object", "$ref": "#/definitions/Address"}, "liabilities": {"title": "Liabilities", "type": "object", "$ref": "#/definitions/Liabilities"}, "mailingAddress": {"title": "Mailing Address", "type": "object", "$ref": "#/definitions/Address"}, "otherDocuments": {"title": "Other Documents", "type": "array", "items": {"$ref": "#/definitions/Document"}}, "previousLegalAddress": {"title": "Previous Legal Address", "type": "object", "$ref": "#/definitions/Address"}, "proofOfAddress": {"title": "Proof of Address", "type": "object", "$ref": "#/definitions/Document"}, "proofOfIdentity": {"title": "Proof of Identity", "type": "object", "$ref": "#/definitions/ProofOfIdentity"}, "proofOfIncome": {"title": "Proof of Income", "type": "object", "$ref": "#/definitions/Document"}, "publicCompanyOfficial": {"title": "Public Company Official", "type": "object", "$ref": "#/definitions/RegulatoryDisclosure"}, "regulatoryDisclosuresV0": {"title": "Regulatory Disclosures v0", "type": "object", "$ref": "#/definitions/RegulatoryDisclosureV0"}, "relatedPersons": {"title": "Related Persons", "type": "array", "items": {"$ref": "#/definitions/PersonRelation"}}, "relatedToPublicCompanyOfficial": {"title": "Related to Public Company Official", "type": "object", "$ref": "#/definitions/RegulatoryDisclosure"}, "relationshipManager": {"title": "Relationship Manager", "type": "array", "items": {"$ref": "#/definitions/PersonRelation"}}, "revoker": {"title": "Revoker", "type": "object", "$ref": "#/definitions/PersonRelation"}, "secondCountryOfCitizenship": {"title": "Second Country of Citizenship", "type": "object", "$ref": "#/definitions/Country"}, "securitiesIndustryAffiliation": {"title": "Securities Industry Affiliation", "type": "object", "$ref": "#/definitions/RegulatoryDisclosure"}, "securityQuestionsAndAnswers": {"title": "Security Questions and Answers", "type": "array", "items": {"$ref": "#/definitions/OnlineAccountSecurityQA"}}, "spouse": {"title": "Spouse", "type": "object", "$ref": "#/definitions/PersonRelation"}, "successorTrustee": {"title": "Successor Trustee", "type": "array", "items": {"$ref": "#/definitions/PersonRelation"}}, "trustee": {"title": "Trustee", "type": "array", "items": {"$ref": "#/definitions/PersonRelation"}}, "alias": {"title": "<PERSON><PERSON>", "type": "string", "maxLength": 255, "minLength": 0}, "alternateEmail": {"title": "Alternate Email", "type": "string", "maxLength": 50, "minLength": 0, "pattern": "^[a-zA-Z0-9.!#$%&'*+\\=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\\.[a-zA-Z0-9-]+)*$"}, "annualExpenses": {"title": "Annual Expenses", "type": "number"}, "annualIncome": {"title": "Annual Income", "type": "string", "maxLength": 30, "minLength": 0}, "annualIncomeExact": {"title": "Annual Income (exact)", "type": "number"}, "authenticationMethod": {"title": "Authentication Method", "type": "string", "maxLength": 255, "minLength": 0}, "backupWithholdingExemptPayeeCode": {"title": "Backup withholding exempt payee code", "type": "string", "maxLength": 255, "minLength": 0}, "bankCustomerType": {"title": "Bank customer type", "type": "string", "maxLength": 255, "minLength": 0}, "beneficiariesAreNaturalPersons": {"title": "Beneficiaries are natural persons", "type": "boolean"}, "betterBusinessBureauRating": {"title": "Better Business Bureau Rating", "type": "boolean"}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "businessPhoneNumber": {"title": "Business Phone Number", "type": "string", "maxLength": 50, "minLength": 0}, "citizenshipStatus": {"title": "Citizenship Status", "type": "string", "maxLength": 255, "minLength": 0}, "createdAt": {"title": "Created At", "type": "string", "format": "date-time"}, "createdBy": {"title": "Created By", "type": "string", "maxLength": 255, "minLength": 0}, "creditRating": {"title": "Credit Rating", "type": "string", "maxLength": 255, "minLength": 0}, "currentCommercialAccountFeatures": {"title": "Current Commercial Account Features", "type": "string", "maxLength": 255, "minLength": 0}, "customerFileID": {"title": "Customer File ID", "type": "string", "maxLength": 255, "minLength": 0}, "dateOfBirth": {"title": "Date Of Birth", "type": "string", "format": "date"}, "dateOfDeath": {"title": "Date Of Death", "type": "string", "format": "date"}, "ein": {"title": "EIN", "type": "string", "maxLength": 255, "minLength": 0}, "employer": {"title": "Employer", "type": "string", "maxLength": 255, "minLength": 0}, "employerPhoneNumber": {"title": "Employer Phone number", "type": "string", "maxLength": 50, "minLength": 0}, "employmentDate": {"title": "Employment Date", "type": "string", "format": "date"}, "employmentStatus": {"title": "Employment Status", "type": "string", "maxLength": 255, "minLength": 0}, "estateNumber": {"title": "Estate Number", "type": "string", "maxLength": 255, "minLength": 0}, "exemptFromBackupWithholding": {"title": "Exempt from backup withholding", "type": "boolean"}, "faTCAReportingExemptionCode": {"title": "FATCA reporting exemption code", "type": "string", "maxLength": 255, "minLength": 0}, "faxNumber": {"title": "Fax Number", "type": "string", "maxLength": 50, "minLength": 0, "pattern": "^(\\+\\d{1,2}\\s)?\\(?\\d{3}\\)?[\\s.-]\\d{3}[\\s.-]\\d{4}$"}, "federalMarginalTaxRate": {"title": "Federal Marginal Tax Rate", "type": "string", "maxLength": 30, "minLength": 0}, "firstName": {"title": "First Name", "type": "string", "maxLength": 255, "minLength": 0, "pattern": "^[a-zA-Z '.-]*$"}, "fullDisplayName": {"title": "Full Display Name", "type": "string"}, "fullName": {"title": "Full Name", "type": "string", "maxLength": 255, "minLength": 0}, "gender": {"title": "Gender", "type": "string", "maxLength": 255, "minLength": 0}, "hasExistingLendingRelationship": {"title": "Has Existing Lending Relationship", "type": "boolean"}, "homeOwnership": {"title": "Home Ownership", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "inGoodStanding": {"title": "In Good Standing", "type": "boolean"}, "industry": {"title": "Industry", "type": "string", "maxLength": 255, "minLength": 0}, "investmentExperience": {"title": "Investment Experience", "type": "string", "maxLength": 255, "minLength": 0}, "investmentExperienceAlternatives": {"title": "Investment experience - Alternatives", "type": "integer"}, "investmentExperienceAlternativesTransact": {"title": "Investment experience - Alternatives Transactions", "type": "string"}, "investmentExperienceAnnuities": {"title": "Investment experience - Annuities", "type": "string", "maxLength": 30, "minLength": 0}, "investmentExperienceAnnuitiesTransaction": {"title": "Investment experience - Annuities Transactions", "type": "string"}, "investmentExperienceDate": {"title": "Investment Experience Date", "type": "string", "format": "date"}, "investmentExperienceDirectedParticipa5e2dc": {"title": "Investment experience - Directed Participation Program Transactions", "type": "string"}, "investmentExperienceDirectedParticipatio": {"title": "Investment experience - Directed Participation Program", "type": "string", "maxLength": 30, "minLength": 0}, "investmentExperienceEquities": {"title": "Investment experience - Equities", "type": "string", "maxLength": 30, "minLength": 0}, "investmentExperienceEquitiesTransactions": {"title": "Investment experience - Equities Transactions", "type": "string", "maxLength": 30, "minLength": 0}, "investmentExperienceFixedAnnuities": {"title": "Investment experience - Fixed Annuities", "type": "integer"}, "investmentExperienceFixedIncome": {"title": "Investment experience - Fixed Income", "type": "string", "maxLength": 30, "minLength": 0}, "investmentExperienceFixedIncomeTransacti": {"title": "Investment experience - Fixed Income Transactions", "type": "string"}, "investmentExperienceFutures": {"title": "Investment experience - Futures", "type": "integer"}, "investmentExperienceFuturesTransactions": {"title": "Investment experience - Futures Transactions", "type": "string"}, "investmentExperienceLevelAlternatives": {"title": "Investment experience level - Alternatives", "type": "string", "maxLength": 255, "minLength": 0}, "investmentExperienceLevelAnnuities": {"title": "Investment experience level - Annuities", "type": "string", "maxLength": 255, "minLength": 0}, "investmentExperienceLevelDirectedPartici": {"title": "Investment experience level - Directed Participation Program", "type": "string", "maxLength": 255, "minLength": 0}, "investmentExperienceLevelEquities": {"title": "Investment experience level - Equities", "type": "string", "maxLength": 255, "minLength": 0}, "investmentExperienceLevelFixedAnnuities": {"title": "Investment experience level - Fixed Annuities", "type": "string", "maxLength": 255, "minLength": 0}, "investmentExperienceLevelFixedIncome": {"title": "Investment experience level - Fixed Income", "type": "string", "maxLength": 255, "minLength": 0}, "investmentExperienceLevelFutures": {"title": "Investment experience level - Futures", "type": "string", "maxLength": 255, "minLength": 0}, "investmentExperienceLevelManagedMoney": {"title": "Investment experience level - Managed Money", "type": "string", "maxLength": 255, "minLength": 0}, "investmentExperienceLevelMutualFunds": {"title": "Investment experience level - Mutual Funds", "type": "string", "maxLength": 255, "minLength": 0}, "investmentExperienceLevelOptions": {"title": "Investment experience level - Options", "type": "string", "maxLength": 255, "minLength": 0}, "investmentExperienceLevelREIT": {"title": "Investment experience level - REIT", "type": "string", "maxLength": 255, "minLength": 0}, "investmentExperienceLevelRealEstate": {"title": "Investment experience level - Real Estate", "type": "string", "maxLength": 255, "minLength": 0}, "investmentExperienceLevelVariableAnnuiti": {"title": "Investment experience level - Variable Annuities", "type": "string", "maxLength": 255, "minLength": 0}, "investmentExperienceManagedMoney": {"title": "Investment experience - Managed Money", "type": "integer"}, "investmentExperienceMargin": {"title": "Investment experience - <PERSON><PERSON>", "type": "integer"}, "investmentExperienceMarginTransactions": {"title": "Investment experience - Margin Transactions", "type": "string"}, "investmentExperienceMutualFunds": {"title": "Investment experience - Mutual Funds", "type": "string", "maxLength": 30, "minLength": 0}, "investmentExperienceMutualFundsTransacti": {"title": "Investment experience - Mutual Funds Transactions", "type": "string"}, "investmentExperienceOptions": {"title": "Investment experience - Options", "type": "string", "maxLength": 30, "minLength": 0}, "investmentExperienceOptionsTransactions": {"title": "Investment experience - Options Transactions", "type": "string", "maxLength": 30, "minLength": 0}, "investmentExperienceREIT": {"title": "Investment experience - REIT", "type": "string", "maxLength": 30, "minLength": 0}, "investmentExperienceREITTransactions": {"title": "Investment experience - REIT Transactions", "type": "integer"}, "investmentExperienceRealEstate": {"title": "Investment experience - Real Estate", "type": "string", "maxLength": 30, "minLength": 0}, "investmentExperienceRealEstateTransactio": {"title": "Investment experience - Real Estate Transactions", "type": "string"}, "investmentExperienceVariableAnnuities": {"title": "Investment experience - Variable Annuities", "type": "integer"}, "isBetterBusinessBureauRegistered": {"title": "Is Better Business Bureau Registered", "type": "boolean"}, "isClientRecord": {"title": "Is Client Record", "type": "boolean"}, "isInACHTerminatedOriginatorsDatabase": {"title": "Is in ACH Terminated Originators Database", "type": "boolean"}, "isOnHighRiskEntitiesList": {"title": "Is on High Risk Entities List", "type": "boolean"}, "isResidingInCommunityPropertyState": {"title": "Is Residing In Community Property State", "type": "boolean"}, "lastModifiedAt": {"title": "Last Modified At", "type": "string", "format": "date-time"}, "lastModifiedBy": {"title": "Last Modified By", "type": "string", "maxLength": 255, "minLength": 0}, "lastName": {"title": "Last Name", "type": "string", "maxLength": 255, "minLength": 0, "pattern": "^[a-zA-Z '.-]*$"}, "legalAddressIsMailingAddress": {"title": "Legal Address is Mailing Address", "type": "boolean"}, "legalAddressLengthOfStay": {"title": "Legal Address Length of Stay", "type": "string", "maxLength": 255, "minLength": 0}, "legalAddressStartDate": {"title": "Legal Address Start Date", "type": "string", "format": "date"}, "legalName": {"title": "Legal Name", "type": "string", "maxLength": 255, "minLength": 0}, "liquidAssets": {"title": "Liquid Assets", "type": "string", "maxLength": 30, "minLength": 0}, "liquidAssetsExact": {"title": "Liquid Assets (exact)", "type": "number"}, "maritalStatus": {"title": "Marital Status", "type": "string", "maxLength": 255, "minLength": 0}, "maskedSSNOrTaxID": {"title": "Masked SSN or Tax ID", "type": "string"}, "middleName": {"title": "Middle Name", "type": "string", "maxLength": 255, "minLength": 0, "pattern": "^[a-zA-Z '.-]*$"}, "natureOfBusiness": {"title": "Nature of business", "type": "string", "maxLength": 255, "minLength": 0}, "netWorthExcludingHome": {"title": "Net Worth excluding Home", "type": "string"}, "netWorthExcludingHomeExact": {"title": "Net Worth excluding Home (exact)", "type": "number"}, "numberOfDependents": {"title": "Number of Dependents", "type": "integer"}, "occupation": {"title": "Occupation", "type": "string", "maxLength": 255, "minLength": 0}, "ofACReportingExempted": {"title": "OFAC reporting exempted", "type": "boolean"}, "personType": {"title": "Person Type", "type": "string", "maxLength": 255, "minLength": 0}, "postAccountOpeningReviewPerformed": {"title": "Post-Account-Opening Review Performed", "type": "boolean"}, "powersOfTrustee": {"title": "Powers of trustee", "type": "string", "maxLength": 255, "minLength": 0}, "preferredName": {"title": "Preferred Name", "type": "string", "maxLength": 255, "minLength": 0, "pattern": "^[a-zA-Z '.-]*$"}, "primaryEmail": {"title": "Primary Email", "type": "string", "maxLength": 50, "minLength": 0, "pattern": "^[a-zA-Z0-9.!#$%&'*+\\=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\\.[a-zA-Z0-9-]+)*$"}, "primaryPhoneIsMobile": {"title": "Primary Phone is Mobile", "type": "boolean"}, "primaryPhoneNumber": {"title": "Primary Phone Number", "type": "string", "maxLength": 50, "minLength": 0}, "primaryProductOrService": {"title": "Primary Product or Service", "type": "string", "maxLength": 255, "minLength": 0}, "profilePicture": {"title": "Profile Picture", "type": "string", "maxLength": 2096}, "providerAssignedID": {"title": "Provider Assigned ID", "type": "string", "maxLength": 255, "minLength": 0}, "recordSource": {"title": "Record Source", "type": "string", "maxLength": 255, "minLength": 0}, "recordSourceApplication": {"title": "Record Source Application", "type": "string", "maxLength": 255, "minLength": 0}, "recordSourceAssignedID": {"title": "Record Source Assigned ID", "type": "string", "maxLength": 255, "minLength": 0}, "recordSourcePortfolioID": {"title": "Record Source Portfolio ID", "type": "string", "maxLength": 255, "minLength": 0}, "riskRating": {"title": "Risk Rating", "type": "string", "maxLength": 255, "minLength": 0}, "secondaryPhoneIsMobile": {"title": "Secondary Phone is Mobile", "type": "boolean"}, "secondaryPhoneNumber": {"title": "Secondary Phone Number", "type": "string", "maxLength": 50, "minLength": 0}, "securityQuestion1Answer": {"title": "Security Question 1 Answer", "type": "string", "maxLength": 255, "minLength": 0}, "securityQuestion2Answer": {"title": "Security Question 2 Answer", "type": "string", "maxLength": 255, "minLength": 0}, "securityQuestion3Answer": {"title": "Security Question 3 Answer", "type": "string", "maxLength": 255, "minLength": 0}, "securityQuestion4Answer": {"title": "Security Question 4 Answer", "type": "string", "maxLength": 255, "minLength": 0}, "securityQuestion5Answer": {"title": "Security Question 5 Answer", "type": "string", "maxLength": 255, "minLength": 0}, "specialExpenses": {"title": "Special Expenses", "type": "number"}, "ssNOrTaxID": {"title": "SSN or Tax ID", "type": "string", "maxLength": 11, "minLength": 0, "pattern": "^(?!(000|666))\\d{3}-(?!00)\\d{2}-(?!0000)\\d{4}$"}, "suffix": {"title": "Suffix", "type": "string", "maxLength": 255, "minLength": 0}, "taxClassification": {"title": "Tax Classification", "type": "string", "maxLength": 255, "minLength": 0}, "taxExemptionStatus": {"title": "Tax Exemption Status", "type": "string", "maxLength": 255, "minLength": 0}, "taxIDType": {"title": "Tax ID Type", "type": "string", "maxLength": 255, "minLength": 0}, "title": {"title": "Title", "type": "string", "maxLength": 255, "minLength": 0}, "titleAtEmployer": {"title": "Title at employer", "type": "string", "maxLength": 255, "minLength": 0}, "totalAssets": {"title": "Total Assets", "type": "number"}, "totalLiabilities": {"title": "Total Liabilities", "type": "number"}, "totalNetWorth": {"title": "Total Net Worth", "type": "string"}, "trustAmendmentDate": {"title": "Trust Amendment Date", "type": "string", "format": "date"}, "trustIsDulyOrganized": {"title": "Trust is duly organized", "type": "boolean"}, "trustType": {"title": "Trust Type", "type": "string", "maxLength": 255, "minLength": 0}, "trusteesAreNaturalPersons": {"title": "Trustees are natural persons", "type": "boolean"}, "trusteesCanActIndividually": {"title": "Trustees can act individually", "type": "boolean"}, "withholdingOptions": {"title": "Withholding Options", "type": "string", "maxLength": 255, "minLength": 0}, "yeMployed": {"title": "Years employed", "type": "integer"}, "yearsAsClientOption": {"title": "Years As Client Option", "type": "string", "maxLength": 255, "minLength": 0}, "yearsSinceEstablishmentOption": {"title": "Years Since Establishment Option", "type": "boolean"}}}, "PersonRelation": {"type": "object", "title": "PersonRelation", "description": "Person Relation", "properties": {"sourcePerson": {"title": "Source Person", "type": "object", "$ref": "#/definitions/Person"}, "targetPerson": {"title": "Target Person", "type": "object", "$ref": "#/definitions/Person"}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "ownershipPercentage": {"title": "Ownership Percentage", "type": "number", "maximum": 100.0, "minimum": 0.0}, "relationship": {"title": "Relationship", "type": "string", "maxLength": 255, "minLength": 0}, "tradingAuthority": {"title": "Trading Authority", "type": "number", "maximum": 100.0, "minimum": 0.0}}}, "Position": {"type": "object", "title": "Position", "description": "Position", "properties": {"account": {"title": "Account", "type": "object", "$ref": "#/definitions/Account"}, "security": {"title": "Security", "type": "object", "$ref": "#/definitions/Security"}, "accruedAmount": {"title": "Accrued Amount", "type": "number"}, "annuityProvider": {"title": "Annuity Provider", "type": "string", "maxLength": 255, "minLength": 0}, "asOfDate": {"title": "As Of Date", "type": "string", "format": "date"}, "blendedUnitCost": {"title": "Blended Unit Cost", "type": "number", "multipleOf": 1e-09}, "bondCallType": {"title": "Bond Call Type", "type": "string", "maxLength": 255, "minLength": 0}, "bondFactor": {"title": "Bond Factor", "type": "number", "multipleOf": 1e-08}, "bondType": {"title": "Bond Type", "type": "string", "maxLength": 255, "minLength": 0}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "callOrPut": {"title": "Call or Put", "type": "string", "maxLength": 255, "minLength": 0}, "contractNumber": {"title": "Contract Number", "type": "string", "maxLength": 255, "minLength": 0}, "contractValue": {"title": "Contract Value", "type": "number"}, "costBasis": {"title": "<PERSON><PERSON>", "type": "number"}, "couponRate": {"title": "Coupon Rate", "type": "number", "multipleOf": 1e-05, "maximum": 100.0, "minimum": 0.0}, "cusip": {"title": "CUSIP", "type": "string", "maxLength": 255, "minLength": 0}, "custodianAssignedSecurityID": {"title": "Custodian Assigned Security ID", "type": "string", "maxLength": 255, "minLength": 0}, "description": {"title": "Description", "type": "string", "maxLength": 255, "minLength": 0}, "dividendRate": {"title": "Dividend Rate", "type": "number", "maximum": 100.0, "minimum": 0.0}, "duration": {"title": "Duration", "type": "string", "maxLength": 255, "minLength": 0}, "expiryDate": {"title": "Expiry Date", "type": "string", "format": "date"}, "firstCouponDate": {"title": "First Coupon Date", "type": "string", "format": "date"}, "held": {"title": "Held", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "instrumentType": {"title": "Instrument Type", "type": "string", "maxLength": 255, "minLength": 0}, "intraDayPriceChange": {"title": "Intra-day Price Change", "type": "number"}, "intraDayTimestamp": {"title": "Intra-day Timestamp", "type": "string", "format": "date-time"}, "isMarginable": {"title": "Is Marginable", "type": "boolean"}, "isin": {"title": "ISIN", "type": "string", "maxLength": 255, "minLength": 0}, "lastSettlementDate": {"title": "Last Settlement Date", "type": "string", "format": "date"}, "lastTradeDate": {"title": "Last Trade Date", "type": "string", "format": "date"}, "location": {"title": "Location", "type": "string", "maxLength": 255, "minLength": 0}, "marketPrice": {"title": "Market Price", "type": "number", "multipleOf": 1e-05}, "marketValue": {"title": "Market Value", "type": "number"}, "marketValueInOriginalCurrency": {"title": "Market Value in Original Currency", "type": "number"}, "marketValueOnSettlementDate": {"title": "Market Value on Settlement Date", "type": "number"}, "maturityDate": {"title": "Maturity Date", "type": "string", "format": "date"}, "optionType": {"title": "Option Type", "type": "string", "maxLength": 255, "minLength": 0}, "originalCurrencyCode": {"title": "Original Currency Code", "type": "string", "maxLength": 255, "minLength": 0}, "parValue": {"title": "Par Value", "type": "number"}, "priceDate": {"title": "Price Date", "type": "string", "format": "date"}, "purchaseAmount": {"title": "Purchase Amount", "type": "number"}, "purchaseDate": {"title": "Purchase Date", "type": "string", "format": "date"}, "purchasePrice": {"title": "Purchase Price", "type": "number", "multipleOf": 1e-05}, "quantity": {"title": "Quantity", "type": "number", "multipleOf": 1e-05}, "securityCategory": {"title": "Security Category", "type": "string"}, "sedol": {"title": "SEDOL", "type": "string", "maxLength": 255, "minLength": 0}, "strikePrice": {"title": "Strike Price", "type": "number", "multipleOf": 1e-09}, "symbol": {"title": "Symbol", "type": "string", "maxLength": 255, "minLength": 0}, "term": {"title": "Term", "type": "string", "maxLength": 255, "minLength": 0}, "totalPremium": {"title": "Total Premium", "type": "number"}, "totalQuantity": {"title": "Total Quantity", "type": "decimal", "multipleOf": 1e-09}, "underlyingSymbol": {"title": "Underlying Symbol", "type": "string", "maxLength": 255, "minLength": 0}, "underlyingSymbolPrice": {"title": "Underlying Symbol Price", "type": "number", "multipleOf": 1e-05}, "unitCost": {"title": "Unit Cost", "type": "number", "multipleOf": 1e-09}, "unrealizedGainLoss": {"title": "Unrealized Gain / Loss", "type": "number"}, "valoren": {"title": "VALOREN", "type": "string", "maxLength": 255, "minLength": 0}, "yieldToCall": {"title": "Yield to Call", "type": "number"}, "yieldToMaturity": {"title": "Yield to Maturity", "type": "number", "multipleOf": 1e-08}}}, "Product": {"type": "object", "title": "Product", "description": "Product", "properties": {"features": {"title": "Features", "type": "array", "items": {"$ref": "#/definitions/ProductFeatures"}}, "productCategory": {"title": "Product Category", "type": "object", "$ref": "#/definitions/ProductCategory"}, "provider": {"title": "Provider", "type": "object", "$ref": "#/definitions/ProductProvider"}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "category": {"title": "Category", "type": "string", "maxLength": 255, "minLength": 0}, "code": {"title": "Code", "type": "string", "maxLength": 255, "minLength": 0}, "description": {"title": "Description", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "image": {"title": "Image", "type": "string", "maxLength": 2096}, "name": {"title": "Name", "type": "string", "maxLength": 255, "minLength": 0}, "rating": {"title": "Rating", "type": "string", "maxLength": 255, "minLength": 0}, "subCategory": {"title": "Sub Category", "type": "string", "maxLength": 255, "minLength": 0}}}, "ProductCategory": {"type": "object", "title": "ProductCategory", "description": "Product Category", "properties": {"bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "category": {"title": "Category", "type": "string", "maxLength": 255, "minLength": 0}, "code": {"title": "Code", "type": "string", "maxLength": 255, "minLength": 0}, "description": {"title": "Description", "type": "string", "maxLength": 255, "minLength": 0}, "domain": {"title": "Domain", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "image": {"title": "Image", "type": "string", "maxLength": 2096}, "subDomain": {"title": "Sub Domain", "type": "string", "maxLength": 255, "minLength": 0}}}, "ProductDomain": {"type": "object", "title": "ProductDomain", "description": "Product Domain", "properties": {"custodian": {"title": "<PERSON><PERSON><PERSON><PERSON>", "type": "object", "$ref": "#/definitions/Custodian"}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "category": {"title": "Category", "type": "string", "maxLength": 255, "minLength": 0}, "code": {"title": "Code", "type": "string", "maxLength": 255, "minLength": 0}, "description": {"title": "Description", "type": "string", "maxLength": 255, "minLength": 0}, "domain": {"title": "Domain", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "image": {"title": "Image", "type": "string", "maxLength": 2096}, "managementType": {"title": "Management Type", "type": "string", "maxLength": 255, "minLength": 0}, "managementTypeDescription": {"title": "Management Type Description", "type": "string", "maxLength": 255, "minLength": 0}, "subDomain": {"title": "Sub Domain", "type": "string", "maxLength": 255, "minLength": 0}}}, "ProductFeatures": {"type": "object", "title": "ProductFeatures", "description": "Product Features", "properties": {"feature": {"title": "Feature", "type": "object", "$ref": "#/definitions/Product"}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}}}, "ProductProvider": {"type": "object", "title": "ProductProvider", "description": "Product Provider", "properties": {"bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "code": {"title": "Code", "type": "string", "maxLength": 255, "minLength": 0}, "description": {"title": "Description", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "image": {"title": "Image", "type": "string", "maxLength": 2096}, "name": {"title": "Name", "type": "string", "maxLength": 255, "minLength": 0}}}, "ProofOfIdentity": {"type": "object", "title": "ProofOfIdentity", "description": "Proof of Identity", "properties": {"document": {"title": "Document", "type": "object", "$ref": "#/definitions/Document"}, "issuingCountry": {"title": "Issuing Country", "type": "object", "$ref": "#/definitions/Country"}, "issuingState": {"title": "Issuing State", "type": "object", "$ref": "#/definitions/StateOrProvince"}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "expiryDate": {"title": "Expiry Date", "type": "string", "format": "date"}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "idNumber": {"title": "ID Number", "type": "string", "maxLength": 255, "minLength": 0}, "issueDate": {"title": "Issue Date", "type": "string", "format": "date"}, "issuingStateOrProvince": {"title": "Issuing State or Province", "type": "string", "maxLength": 255, "minLength": 0}, "type": {"title": "Type", "type": "string", "maxLength": 255, "minLength": 0}}}, "ReceivedFile": {"type": "object", "title": "ReceivedFile", "description": "Received File", "properties": {"expectedFile": {"title": "Expected File", "type": "object", "$ref": "#/definitions/ExpectedFile"}, "createdAt": {"title": "Created At", "type": "string", "format": "date-time"}, "createdBy": {"title": "Created By", "type": "string", "maxLength": 255, "minLength": 0}, "fileName": {"title": "File Name", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "j_Size": {"title": "Size", "type": "integer"}, "lastModifiedAt": {"title": "Last Modified At", "type": "string", "format": "date-time"}, "lastModifiedBy": {"title": "Last Modified By", "type": "string", "maxLength": 255, "minLength": 0}, "name": {"title": "Name", "type": "string", "maxLength": 255, "minLength": 0}, "niGOFileName": {"title": "NIGO File Name", "type": "string", "maxLength": 255, "minLength": 0}, "rows": {"title": "Rows", "type": "integer"}}}, "RecordSource": {"type": "object", "title": "RecordSource", "description": "Record Source", "properties": {"code": {"title": "Code", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "name": {"title": "Name", "type": "string", "maxLength": 255, "minLength": 0}, "priority": {"title": "Priority", "type": "integer"}}}, "RegistrationType": {"type": "object", "title": "RegistrationType", "description": "Registration Type", "properties": {"custodian": {"title": "<PERSON><PERSON><PERSON><PERSON>", "type": "object", "$ref": "#/definitions/Custodian"}, "applicableTo": {"title": "Applicable to", "type": "string", "maxLength": 255, "minLength": 0}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "category": {"title": "Category", "type": "string", "maxLength": 255, "minLength": 0}, "code": {"title": "Code", "type": "string", "maxLength": 255, "minLength": 0}, "custodianCode": {"title": "Custodian Code", "type": "string", "maxLength": 255, "minLength": 0}, "description": {"title": "Description", "type": "string", "maxLength": 255, "minLength": 0}, "enabled": {"title": "Enabled", "type": "boolean"}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "image": {"title": "Image", "type": "string", "maxLength": 2096}, "instructions": {"title": "Instructions", "type": "string", "maxLength": 2000, "minLength": 0}, "isCoveredByERISA": {"title": "Is Covered by ERISA", "type": "boolean"}, "isRetirement": {"title": "Is Retirement", "type": "boolean"}, "name": {"title": "Name", "type": "string", "maxLength": 255, "minLength": 0}, "sortOrder": {"title": "Sort Order", "type": "integer"}}}, "RegistrationTypeGroup": {"type": "object", "title": "RegistrationTypeGroup", "description": "Registration Type Group", "properties": {"registrationTypes": {"title": "Registration Types", "type": "array", "items": {"$ref": "#/definitions/RegistrationType"}}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "name": {"title": "Name", "type": "string", "maxLength": 255, "minLength": 0}}}, "RegulatoryDisclosure": {"type": "object", "title": "RegulatoryDisclosure", "description": "Regulatory Disclosure", "properties": {"firmAddress": {"title": "Firm Address", "type": "object", "$ref": "#/definitions/Address"}, "foreignCountry": {"title": "Foreign Country", "type": "object", "$ref": "#/definitions/Country"}, "regulatoryDisclosureOption": {"title": "Regulatory Disclosure Option", "type": "object", "$ref": "#/definitions/RegulatoryDisclosureOption"}, "affiliationType": {"title": "Affiliation Type", "type": "string", "maxLength": 255, "minLength": 0}, "enabled": {"title": "Enabled", "type": "boolean"}, "firmNameForEmployee": {"title": "Firm Name for Employee", "type": "string", "maxLength": 255, "minLength": 0}, "firmNameForOfficer": {"title": "Firm Name for Officer", "type": "string", "maxLength": 255, "minLength": 0}, "firmTickerForOfficer": {"title": "Firm Ticker for Officer", "type": "string", "maxLength": 255, "minLength": 0}, "foreignCountryName": {"title": "Foreign Country Name", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "nameOfEmployee": {"title": "Name of Employee", "type": "string", "maxLength": 255, "minLength": 0}, "nameOfForeignOfficial": {"title": "Name of Foreign Official", "type": "string", "maxLength": 255, "minLength": 0}, "nameOfIndividual": {"title": "Name of individual", "type": "string", "maxLength": 255, "minLength": 0}, "officeHeldWithForeignGovt": {"title": "Office held with Foreign Govt", "type": "string", "maxLength": 255, "minLength": 0}, "relationshipOfEmployee": {"title": "Relationship of Employee", "type": "string", "maxLength": 255, "minLength": 0}, "relationshipOfOfficer": {"title": "Relationship of Officer", "type": "string", "maxLength": 255, "minLength": 0}, "typeOfEmployer": {"title": "Type of employer", "type": "string", "maxLength": 255, "minLength": 0}}}, "RegulatoryDisclosureOption": {"type": "object", "title": "RegulatoryDisclosureOption", "description": "Regulatory Disclosure Option", "properties": {"disclosureType": {"title": "Disclosure type", "type": "string", "maxLength": 255, "minLength": 0}, "enabled": {"title": "Enabled", "type": "boolean"}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "label": {"title": "Label", "type": "string", "maxLength": 255, "minLength": 0}}}, "RegulatoryDisclosureV0": {"type": "object", "title": "RegulatoryDisclosureV0", "description": "Regulatory Disclosure v0", "properties": {"foreignCountry": {"title": "Foreign Country", "type": "object", "$ref": "#/definitions/Country"}, "directorOrOfficerInPublicCompany": {"title": "Director or Officer in Public Company", "type": "boolean"}, "employedBySecurityIndustryEntity": {"title": "Employed by security industry entity", "type": "boolean"}, "firmNameForEmployee": {"title": "Firm Name for Employee", "type": "string", "maxLength": 255, "minLength": 0}, "firmNameForOfficer": {"title": "Firm Name for Officer", "type": "string", "maxLength": 255, "minLength": 0}, "firmTickerForOfficer": {"title": "Firm Ticker for Officer", "type": "string", "maxLength": 255, "minLength": 0}, "foreignCountryName": {"title": "Foreign Country Name", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "nameOfEmployee": {"title": "Name of Employee", "type": "string", "maxLength": 255, "minLength": 0}, "nameOfForeignOfficial": {"title": "Name of Foreign Official", "type": "string", "maxLength": 255, "minLength": 0}, "nameOfIndividual": {"title": "Name of individual", "type": "string", "maxLength": 255, "minLength": 0}, "officeHeldWithForeignGovt": {"title": "Office held with Foreign Govt", "type": "string", "maxLength": 255, "minLength": 0}, "officerRole": {"title": "Officer Role", "type": "string", "maxLength": 255, "minLength": 0}, "relationshipOfEmployee": {"title": "Relationship of Employee", "type": "string", "maxLength": 255, "minLength": 0}, "relationshipOfOfficer": {"title": "Relationship of Officer", "type": "string", "maxLength": 255, "minLength": 0}, "seniorMilitaryGovernmentOrPoliticalOffic": {"title": "Senior Military, Government or Political Official", "type": "boolean"}, "typeOfEmployer": {"title": "Type of employer", "type": "string", "maxLength": 255, "minLength": 0}}}, "RelatedAccountResponse": {"type": "object", "title": "RelatedAccountResponse", "description": "RelatedAccountResponse", "properties": {"accountNumber": {"title": "accountNumber", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "primaryAcctBranch": {"title": "primaryAcctBranch", "type": "string", "maxLength": 255, "minLength": 0}, "primaryAcctChangeDate": {"title": "primaryAcctChangeDate", "type": "string", "maxLength": 255, "minLength": 0}, "primaryAcctHomePhone": {"title": "primaryAcctHomePhone", "type": "string", "maxLength": 255, "minLength": 0}, "primaryAcctNameAddr1": {"title": "primaryAcctNameAddr1", "type": "string", "maxLength": 255, "minLength": 0}, "primaryAcctNameAddr2": {"title": "primaryAcctNameAddr2", "type": "string", "maxLength": 255, "minLength": 0}, "primaryAcctNameAddr3": {"title": "primaryAcctNameAddr3", "type": "string", "maxLength": 255, "minLength": 0}, "primaryAcctNameAddr4": {"title": "primaryAcctNameAddr4", "type": "string", "maxLength": 255, "minLength": 0}, "primaryAcctNameAddr5": {"title": "primaryAcctNameAddr5", "type": "string", "maxLength": 255, "minLength": 0}, "primaryAcctRep": {"title": "primaryAcctRep", "type": "string", "maxLength": 255, "minLength": 0}, "primaryAcctTaxId": {"title": "primaryAcctTaxId", "type": "string", "maxLength": 255, "minLength": 0}, "sub": {"title": "sub", "type": "string", "maxLength": 255, "minLength": 0}}}, "RepCode": {"type": "object", "title": "RepCode", "description": "Rep Code", "properties": {"associates": {"title": "Associates", "type": "string"}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "description": {"title": "Description", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "isSplit": {"title": "Is Split", "type": "boolean"}, "name": {"title": "Name", "type": "string", "maxLength": 255, "minLength": 0}, "parentCode": {"title": "Parent Code", "type": "string", "maxLength": 255, "minLength": 0}, "repCode": {"title": "Rep Code", "type": "string", "maxLength": 255, "minLength": 0}, "repName": {"title": "Rep Name", "type": "string", "maxLength": 255, "minLength": 0}, "repNameAndCode": {"title": "Rep Name And Code", "type": "string"}, "source": {"title": "Source", "type": "string", "maxLength": 255, "minLength": 0}}}, "RepCodeAccountBalances": {"type": "object", "title": "RepCodeAccountBalances", "description": "Rep-code Account Balances", "properties": {"repCode": {"title": "Rep Code", "type": "object", "$ref": "#/definitions/RepCode"}, "beginningAccounts": {"title": "Beginning Accounts", "type": "integer"}, "beginningAssets": {"title": "Beginning Assets", "type": "number"}, "beginningBuyingPower": {"title": "Beginning Buying Power", "type": "number"}, "beginningCashBalance": {"title": "Beginning Cash Balance", "type": "number"}, "beginningMarginBalance": {"title": "Beginning Margin Balance", "type": "number"}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "closedAccounts": {"title": "Closed Accounts", "type": "integer"}, "contributions": {"title": "Contributions", "type": "number"}, "endingAccounts": {"title": "Ending Accounts", "type": "integer"}, "endingAssets": {"title": "Ending Assets", "type": "number"}, "endingBuyingPower": {"title": "Ending Buying Power", "type": "number"}, "endingCashBalance": {"title": "Ending Cash Balance", "type": "number"}, "endingMarginBalance": {"title": "Ending <PERSON><PERSON>", "type": "number"}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "marketAppreciation": {"title": "Market Appreciation", "type": "number"}, "newAccounts": {"title": "New Accounts", "type": "integer"}, "periodEndDate": {"title": "Period End Date", "type": "string", "format": "date"}, "periodStartDate": {"title": "Period Start Date", "type": "string", "format": "date"}, "periodType": {"title": "Period Type", "type": "string", "maxLength": 255, "minLength": 0}, "withdrawals": {"title": "<PERSON><PERSON><PERSON><PERSON>", "type": "number"}}}, "RepCodeSplit": {"type": "object", "title": "RepCodeSplit", "description": "Rep Code Split", "properties": {"sourceRepCode": {"title": "Source Rep Code", "type": "object", "$ref": "#/definitions/RepCode"}, "targetRepCode": {"title": "Target Rep Code", "type": "object", "$ref": "#/definitions/RepCode"}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "splitPercentage": {"title": "Split Percentage", "type": "number", "maximum": 100.0, "minimum": 0.0}}}, "RepPositions": {"type": "object", "title": "RepPositions", "description": "Rep Positions", "properties": {"repCode": {"title": "Rep Code", "type": "object", "$ref": "#/definitions/RepCode"}, "security": {"title": "Security", "type": "object", "$ref": "#/definitions/Security"}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "marketValue": {"title": "Market Value", "type": "number"}, "numberOfAccounts": {"title": "Number of accounts", "type": "integer"}, "numberOfShares": {"title": "Number of shares", "type": "number", "multipleOf": 1e-05}, "periodEndDate": {"title": "Period End Date", "type": "string", "format": "date"}, "securityCategory": {"title": "Security Category", "type": "string"}}}, "ReplacedAnnuityDetails": {"type": "object", "title": "ReplacedAnnuityDetails", "description": "Replaced Annuity Details", "properties": {"additionalDeathBenefitFeesPercent": {"title": "Additional Death Benefit Fe<PERSON>", "type": "number", "maximum": 100.0, "minimum": 0.0}, "administrationFeesPercent": {"title": "Administration Fees <PERSON>", "type": "number", "maximum": 100.0, "minimum": 0.0}, "baseFees": {"title": "Base Fees", "type": "number"}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "enhancedDeathBenefitFeesPercent": {"title": "Enhanced Death Benefit Fees Percent", "type": "number", "maximum": 100.0, "minimum": 0.0}, "fundExpensesPercent": {"title": "Fund Expenses Percent", "type": "number", "maximum": 100.0, "minimum": 0.0}, "guaranteedDeathBenefitFees": {"title": "Guaranteed Death Benefit Fees", "type": "number"}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "livingBenefitFeesPercent": {"title": "Living Benefit Fees Percent", "type": "number", "maximum": 100.0, "minimum": 0.0}, "name": {"title": "Name", "type": "string", "maxLength": 255, "minLength": 0}, "otherFeesDetails": {"title": "Other Fees Details", "type": "string", "maxLength": 255, "minLength": 0}, "otherFeesPercent": {"title": "Other Fees Percent", "type": "number", "maximum": 100.0, "minimum": 0.0}, "totalAnnualTransactionCosts": {"title": "Total Annual Transaction Costs", "type": "number"}, "type": {"title": "Type", "type": "string", "maxLength": 255, "minLength": 0}}}, "RequiredDocument": {"type": "object", "title": "RequiredDocument", "description": "Required Document", "properties": {"code": {"title": "Code", "type": "string", "maxLength": 255, "minLength": 0}, "documentTypeName": {"title": "Document Type Name", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "sendDocumentToAXOS": {"title": "Send Document to AXOS", "type": "boolean"}}}, "RequriedDocumentsCriteria": {"type": "object", "title": "RequriedDocumentsCriteria", "description": "Requried Documents Criteria", "properties": {"documentType": {"title": "Document Type", "type": "object", "$ref": "#/definitions/RequiredDocument"}, "fundingMethodInfo": {"title": "Funding Method Info", "type": "array", "items": {"$ref": "#/definitions/FundingMethodTypes"}}, "registrationTypeInfo": {"title": "Registration Type Info", "type": "array", "items": {"$ref": "#/definitions/RegistrationType"}}, "criteria": {"title": "Criteria", "type": "string", "maxLength": 255, "minLength": 0, "enum": ["Required for registration type", "Required for funding method", "Required for funding method for a registration type"]}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}}}, "ResourceFilterTypes": {"type": "object", "title": "ResourceFilterTypes", "description": "Resource Filter Types", "properties": {"bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "fieldName": {"title": "Field Name", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "label": {"title": "Label", "type": "string", "maxLength": 255, "minLength": 0}, "mappingTable": {"title": "Mapping Table", "type": "string", "maxLength": 255, "minLength": 0}, "name": {"title": "Name", "type": "string", "maxLength": 255, "minLength": 0}}}, "RetirementGoal": {"type": "object", "title": "RetirementGoal", "description": "Retirement Goal", "properties": {"id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "livingExpensesInRetirement": {"title": "Living Expenses in Retirement", "type": "number"}, "mortgagePaidOffByRetirement": {"title": "Mortgage Paid Off by Retirement", "type": "boolean"}, "retireWithSpouse": {"title": "Retire With Spouse", "type": "boolean"}, "retirementAge": {"title": "Retirement Age", "type": "integer"}, "retirementLifestyle": {"title": "Retirement Lifestyle", "type": "string", "maxLength": 255, "minLength": 0}}}, "RetirementPlanSummary": {"type": "object", "title": "RetirementPlanSummary", "description": "Retirement Plan Summary", "properties": {"account": {"title": "Account", "type": "object", "$ref": "#/definitions/Account"}, "asOfDate": {"title": "As of Date", "type": "string", "format": "date"}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "corrected1099Status": {"title": "Corrected 1099 Status", "type": "string", "maxLength": 255, "minLength": 0}, "corrected5498Status": {"title": "Corrected 5498 Status", "type": "string", "maxLength": 255, "minLength": 0}, "correctedFMVStatementStatus": {"title": "Corrected FMV Statement Status", "type": "string", "maxLength": 255, "minLength": 0}, "currentEndOfYearMarketValue": {"title": "Current End of Year Market Value ", "type": "number"}, "distributionSource": {"title": "Distribution Source", "type": "string", "maxLength": 255, "minLength": 0}, "eligibleForFeeDiscounts": {"title": "Eligible for Fee Discounts", "type": "boolean"}, "federalWithholdingPercentage": {"title": "Federal Withholding Percentage", "type": "number", "maximum": 100.0, "minimum": 0.0}, "feeSchedule": {"title": "Fee Schedule", "type": "string", "maxLength": 255, "minLength": 0}, "feesPaid": {"title": "<PERSON><PERSON>", "type": "boolean"}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "isParticipantDeceased": {"title": "Is Participant Deceased", "type": "boolean"}, "isPooledPlan": {"title": "Is Pooled Plan", "type": "boolean"}, "isPrimary": {"title": "Is Primary", "type": "boolean"}, "lastChangeDate": {"title": "Last Change Date", "type": "string", "format": "date"}, "lastChangeTerminal": {"title": "Last Change Terminal", "type": "string", "maxLength": 255, "minLength": 0}, "lastChangedBy": {"title": "Last Changed By", "type": "string", "maxLength": 255, "minLength": 0}, "overrideMarketValue": {"title": "Override Market Value", "type": "boolean"}, "planClass": {"title": "Plan Class", "type": "string", "maxLength": 255, "minLength": 0}, "previousEndOfYearMarketValue": {"title": "Previous End of Year Market Value ", "type": "number"}, "relatedAccountNumber": {"title": "Related Account Number", "type": "string", "maxLength": 255, "minLength": 0}, "skipFees": {"title": "<PERSON><PERSON>", "type": "boolean"}, "skipGenerating1099R": {"title": "Skip Generating 1099R", "type": "boolean"}, "skipGenerating5498": {"title": "Skip Generating 5498", "type": "boolean"}, "skipGeneratingFMVStatement": {"title": "Skip Generating FMV Statement", "type": "boolean"}, "stateWithholdingPercentage": {"title": "State Withholding Percentage", "type": "number", "maximum": 100.0, "minimum": 0.0}, "terminationReason": {"title": "Termination Reason", "type": "string", "maxLength": 255, "minLength": 0}}}, "RoutingNumber": {"type": "object", "title": "RoutingNumber", "description": "Routing Number", "properties": {"state": {"title": "State", "type": "array", "items": {"$ref": "#/definitions/StateOrProvince"}}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "routingNumber": {"title": "Routing number", "type": "string", "maxLength": 255, "minLength": 0}}}, "SecondaryOwners": {"type": "object", "title": "SecondaryOwners", "description": "Secondary Owners", "properties": {"id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}}}, "SecuritiesTransferSecurity": {"type": "object", "title": "SecuritiesTransferSecurity", "description": "Securities Transfer Security", "properties": {"amount": {"title": "Amount", "type": "number"}, "capitalGainsOption": {"title": "Capital Gains Option", "type": "string", "maxLength": 255, "minLength": 0}, "createdAt": {"title": "Created At", "type": "string", "format": "date-time"}, "cusip": {"title": "CUSIP", "type": "string", "maxLength": 255, "minLength": 0}, "description": {"title": "Description", "type": "string", "maxLength": 255, "minLength": 0}, "dividendOption": {"title": "Dividend Option", "type": "string", "maxLength": 255, "minLength": 0}, "fundAccountNumber": {"title": "Fund Account Number", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "lastModifiedAt": {"title": "Last Modified At", "type": "string", "format": "date-time"}, "lastModifiedBy": {"title": "Last Modified By", "type": "string", "maxLength": 255, "minLength": 0}, "mutualFundTransferOption": {"title": "Mutual Fund Transfer Option", "type": "string", "maxLength": 255, "minLength": 0}, "quantity": {"title": "Quantity", "type": "decimal", "multipleOf": 1e-05}, "symbol": {"title": "Symbol", "type": "string", "maxLength": 255, "minLength": 0}, "transferAll": {"title": "Transfer All", "type": "boolean"}, "transferUnits": {"title": "Transfer Units", "type": "string", "maxLength": 255, "minLength": 0}}}, "SecuritiesTransferSourceOrReceiver": {"type": "object", "title": "SecuritiesTransferSourceOrReceiver", "description": "Securities Transfer Source or Receiver", "properties": {"accountOwners": {"title": "Account Owners", "type": "array", "items": {"$ref": "#/definitions/Person"}}, "firmAddress": {"title": "Firm Address", "type": "object", "$ref": "#/definitions/Address"}, "payeeAddress": {"title": "Payee Address", "type": "object", "$ref": "#/definitions/Address"}, "registrationType": {"title": "Registration Type", "type": "object", "$ref": "#/definitions/RegistrationType"}, "accountHolderAlternateName": {"title": "Account Holder Alternate Name", "type": "string", "maxLength": 255, "minLength": 0}, "accountNumber": {"title": "Account Number", "type": "string", "maxLength": 255, "minLength": 0}, "accountTitle": {"title": "Account Title", "type": "string", "maxLength": 255, "minLength": 0}, "additionalInformation": {"title": "Additional Information", "type": "string", "maxLength": 255, "minLength": 0}, "clearingNumber": {"title": "Clearing Number", "type": "string", "maxLength": 255, "minLength": 0}, "createdAt": {"title": "Created At", "type": "string", "format": "date-time"}, "createdBy": {"title": "Created By", "type": "string", "maxLength": 255, "minLength": 0}, "deliveringAccountTitleSameAsReceiving": {"title": "Delivering Account Title same as Receiving", "type": "boolean"}, "deliveryMethod": {"title": "Delivery method", "type": "string", "maxLength": 255, "minLength": 0}, "firmName": {"title": "Firm Name", "type": "string", "maxLength": 255, "minLength": 0}, "firmPhoneNumber": {"title": "Firm Phone Number", "type": "string", "maxLength": 50, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "intermediaryBank": {"title": "Intermediary Bank", "type": "string", "maxLength": 255, "minLength": 0}, "lastModifiedAt": {"title": "Last Modified At", "type": "string", "format": "date-time"}, "lastModifiedBy": {"title": "Last Modified By", "type": "string", "maxLength": 255, "minLength": 0}, "payeeAddressType": {"title": "Payee Address Type", "type": "string", "maxLength": 255, "minLength": 0}, "payeeIsSender": {"title": "Payee Is Sender", "type": "boolean"}, "payeeName": {"title": "Payee Name", "type": "string", "maxLength": 255, "minLength": 0}, "payeePhoneNumber": {"title": "Payee Phone Number", "type": "string", "maxLength": 50, "minLength": 0}, "payeeRelationshipType": {"title": "Payee Relationship Type", "type": "string", "maxLength": 255, "minLength": 0}}}, "Security": {"type": "object", "title": "Security", "description": "Security", "properties": {"product": {"title": "Product", "type": "object", "$ref": "#/definitions/Product"}, "securityType": {"title": "Security Type", "type": "object", "$ref": "#/definitions/SecurityType"}, "assetClass": {"title": "Asset Class", "type": "string", "maxLength": 255, "minLength": 0}, "bondClass": {"title": "Bond Class", "type": "string", "maxLength": 255, "minLength": 0}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "callIndicator": {"title": "Call Indicator", "type": "string", "maxLength": 255, "minLength": 0}, "closedEnd": {"title": "Closed End", "type": "boolean"}, "closedToBuys": {"title": "Closed to Buys", "type": "boolean"}, "closedToNewInvestors": {"title": "Closed to New Investors", "type": "boolean"}, "closedToSells": {"title": "Closed to Sells", "type": "boolean"}, "countryCode": {"title": "Country Code", "type": "string", "maxLength": 255, "minLength": 0}, "couponRate": {"title": "Coupon Rate", "type": "number", "maximum": 100.0, "minimum": 0.0}, "cusip": {"title": "CUSIP", "type": "string", "maxLength": 255, "minLength": 0}, "custodianAssignedID": {"title": "Custodian Assigned ID", "type": "string", "maxLength": 255, "minLength": 0}, "dividendFrequency": {"title": "Dividend Frequency", "type": "string", "maxLength": 255, "minLength": 0}, "dividendOrInterestPaymentMethod": {"title": "Dividend Or Interest Payment Method", "type": "string", "maxLength": 255, "minLength": 0}, "dividendReinvestEligible": {"title": "Dividend Reinvest Eligible", "type": "boolean"}, "dividendYield": {"title": "Dividend Yield", "type": "number", "maximum": 100.0, "minimum": 0.0}, "dtCCEligible": {"title": "DTCC Eligible", "type": "boolean"}, "endOfMonthPrice": {"title": "End of Month Price", "type": "number", "multipleOf": 1e-05}, "endOfMonthPriceDate": {"title": "End of Month Price Date", "type": "string", "format": "date"}, "etFIndicator": {"title": "ETF Indicator", "type": "boolean"}, "exchange": {"title": "Exchange", "type": "string", "maxLength": 255, "minLength": 0}, "expenseRatio": {"title": "Expense Ratio", "type": "number", "maximum": 100.0, "minimum": 0.0}, "factorDate": {"title": "Factor Date", "type": "string", "format": "date"}, "foreignSecurityIndicator": {"title": "Foreign Security Indicator", "type": "boolean"}, "fundFamilyCode": {"title": "Fund Family Code", "type": "string", "maxLength": 255, "minLength": 0}, "fundFamilyName": {"title": "Fund Family Name", "type": "string", "maxLength": 255, "minLength": 0}, "fundServIndicator": {"title": "FundServ Indicator", "type": "boolean"}, "fundShareClass": {"title": "Fund Share Class", "type": "string", "maxLength": 255, "minLength": 0}, "fundType": {"title": "Fund Type", "type": "string", "maxLength": 255, "minLength": 0}, "fundVestIndicator": {"title": "FundVest Indicator", "type": "boolean"}, "id": {"title": "ID", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "interestCalculation": {"title": "Interest Calculation", "type": "string", "maxLength": 255, "minLength": 0}, "interestFrequency": {"title": "Interest Frequency", "type": "string", "maxLength": 255, "minLength": 0}, "isVariableRate": {"title": "Is Variable Rate", "type": "boolean"}, "isin": {"title": "ISIN", "type": "string", "maxLength": 255, "minLength": 0}, "issuer": {"title": "Issuer", "type": "string", "maxLength": 255, "minLength": 0}, "issuingCurrency": {"title": "Issuing Currency", "type": "string", "maxLength": 255, "minLength": 0}, "lastPrice": {"title": "Last Price", "type": "number", "multipleOf": 1e-05}, "lastPriceDate": {"title": "Last Price Date", "type": "string", "format": "date"}, "leveragedPercentage": {"title": "Leveraged Percentage", "type": "number", "maximum": 100.0, "minimum": 0.0}, "maturityOrExpirationDate": {"title": "Maturity Or Expiration Date", "type": "string", "format": "date"}, "minorProductCode": {"title": "Minor Product Code", "type": "string", "maxLength": 255, "minLength": 0}, "moodysBondRating": {"title": "Moody's Bond Rating", "type": "string", "maxLength": 255, "minLength": 0}, "optionExpiryDate": {"title": "Option Expiry Date", "type": "string", "format": "date"}, "paymentDelayDays": {"title": "Payment Delay Days", "type": "string", "maxLength": 255, "minLength": 0}, "poolFactor": {"title": "Pool Factor", "type": "number", "multipleOf": 1e-10}, "previousDayPrice": {"title": "Previous Day Price", "type": "number", "multipleOf": 1e-05}, "previousFactor": {"title": "Previous Factor", "type": "number", "multipleOf": 1e-10}, "previousFactorDate": {"title": "Previous Factor Date", "type": "string", "format": "date"}, "previousPriceDate": {"title": "Previous Price Date", "type": "string", "format": "date"}, "productCodeName": {"title": "Product Code Name", "type": "string", "maxLength": 255, "minLength": 0}, "recordSource": {"title": "Record Source", "type": "string", "maxLength": 255, "minLength": 0}, "reinvestCapitalGains": {"title": "Reinvest Capital Gains", "type": "boolean"}, "reinvestDividends": {"title": "Reinvest Dividends", "type": "boolean"}, "securityCalculationCode": {"title": "Security Calculation Code", "type": "string", "maxLength": 255, "minLength": 0}, "securityCategory": {"title": "Security Category", "type": "string"}, "securityDescription": {"title": "Security Description", "type": "string"}, "securityDescription1": {"title": "Security Description 1", "type": "string", "maxLength": 255, "minLength": 0}, "securityDescription2": {"title": "Security Description 2", "type": "string", "maxLength": 255, "minLength": 0}, "securityDescription3": {"title": "Security Description 3", "type": "string", "maxLength": 255, "minLength": 0}, "securityDescription4": {"title": "Security Description 4", "type": "string", "maxLength": 255, "minLength": 0}, "securityDescription5": {"title": "Security Description 5", "type": "string", "maxLength": 255, "minLength": 0}, "securityDescription6": {"title": "Security Description 6", "type": "string", "maxLength": 255, "minLength": 0}, "securityModifier": {"title": "Security Modifier", "type": "string", "maxLength": 255, "minLength": 0}, "sedol": {"title": "SEDOL", "type": "string", "maxLength": 255, "minLength": 0}, "siCCode": {"title": "SIC Code", "type": "string", "maxLength": 255, "minLength": 0}, "strikePrice": {"title": "Strike Price", "type": "number", "multipleOf": 1e-05}, "structuredProductIndicator": {"title": "Structured Product Indicator", "type": "boolean"}, "symbol": {"title": "Symbol", "type": "string", "maxLength": 255, "minLength": 0}, "underlyingSecurityNumber": {"title": "Underlying Security Number", "type": "string", "maxLength": 255, "minLength": 0}, "valoren": {"title": "VALOREN", "type": "string", "maxLength": 255, "minLength": 0}, "worthlessSecurityIndicator": {"title": "Worthless Security Indicator", "type": "boolean"}}}, "SecurityRestriction": {"type": "object", "title": "SecurityRestriction", "description": "Security Restriction", "properties": {"security": {"title": "Security", "type": "object", "$ref": "#/definitions/Security"}, "asOfDate": {"title": "As of Date", "type": "string", "format": "date"}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "dateAdded": {"title": "Date Added", "type": "string", "format": "date"}, "errorLevel": {"title": "Error level", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "restriction": {"title": "Restriction", "type": "string", "maxLength": 255, "minLength": 0}}}, "SecurityType": {"type": "object", "title": "SecurityType", "description": "Security Type", "properties": {"custodian": {"title": "<PERSON><PERSON><PERSON><PERSON>", "type": "object", "$ref": "#/definitions/Custodian"}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "category": {"title": "Category", "type": "string", "maxLength": 255, "minLength": 0}, "code": {"title": "Code", "type": "string", "maxLength": 255, "minLength": 0}, "custodianCode": {"title": "Custodian Code", "type": "string", "maxLength": 255, "minLength": 0}, "description": {"title": "Description", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "name": {"title": "Name", "type": "string", "maxLength": 255, "minLength": 0}}}, "SecurityTypeCustodianMapping": {"type": "object", "title": "SecurityTypeCustodianMapping", "description": "Security Type <PERSON><PERSON><PERSON>ian Mapping", "properties": {"custodian": {"title": "<PERSON><PERSON><PERSON><PERSON>", "type": "object", "$ref": "#/definitions/Custodian"}, "securityType": {"title": "Security Type", "type": "object", "$ref": "#/definitions/SecurityType"}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "code": {"title": "Code", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "name": {"title": "Name", "type": "string", "maxLength": 255, "minLength": 0}}}, "Signature": {"type": "object", "title": "Signature", "description": "Signature", "properties": {"emailID": {"title": "Email ID", "type": "string", "maxLength": 50, "minLength": 0, "pattern": "^[a-zA-Z0-9.!#$%&'*+\\=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\\.[a-zA-Z0-9-]+)*$"}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "role": {"title": "Role", "type": "string", "maxLength": 255, "minLength": 0}, "routingOrder": {"title": "Routing Order", "type": "integer"}, "signatory": {"title": "Signatory", "type": "string", "maxLength": 255, "minLength": 0}, "status": {"title": "Status", "type": "string", "maxLength": 255, "minLength": 0}}}, "SrAdditionalInfo": {"type": "object", "title": "SrAdditionalInfo", "description": "SR Additional Info", "properties": {"id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "info1": {"title": "Info1", "type": "string", "maxLength": 255, "minLength": 0}, "info10": {"title": "Info10", "type": "string", "maxLength": 255, "minLength": 0}, "info11": {"title": "Info11", "type": "string", "maxLength": 255, "minLength": 0}, "info12": {"title": "Info12", "type": "string", "maxLength": 255, "minLength": 0}, "info13": {"title": "Info13", "type": "string", "maxLength": 255, "minLength": 0}, "info14": {"title": "Info14", "type": "string", "maxLength": 255, "minLength": 0}, "info15": {"title": "Info15", "type": "string", "maxLength": 255, "minLength": 0}, "info2": {"title": "Info2", "type": "string", "maxLength": 255, "minLength": 0}, "info3": {"title": "Info3", "type": "string", "maxLength": 5000, "minLength": 0}, "info4": {"title": "Info4", "type": "string", "maxLength": 255, "minLength": 0}, "info5": {"title": "Info5", "type": "string", "maxLength": 255, "minLength": 0}, "info6": {"title": "Info6", "type": "string", "maxLength": 255, "minLength": 0}, "info7": {"title": "Info7", "type": "string", "maxLength": 255, "minLength": 0}, "info8": {"title": "Info8", "type": "string", "maxLength": 255, "minLength": 0}, "info9": {"title": "Info9", "type": "string", "maxLength": 255, "minLength": 0}}}, "SrCategory": {"type": "object", "title": "SrCategory", "description": "SR Category", "properties": {"subCategory": {"title": "Sub Category", "type": "array", "items": {"$ref": "#/definitions/SrSubCategory"}}, "description": {"title": "Description", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "name": {"title": "Name", "type": "string", "maxLength": 255, "minLength": 0}, "rank": {"title": "Rank", "type": "integer"}}}, "SrDefinition": {"type": "object", "title": "SrDefinition", "description": "SR Definition", "properties": {"category": {"title": "Category", "type": "object", "$ref": "#/definitions/SrCategory"}, "mergeReadQuery": {"title": "Merge Read Query", "type": "object", "$ref": "#/definitions/SrQuery"}, "subCategory": {"title": "Sub Category", "type": "object", "$ref": "#/definitions/SrSubCategory"}, "tasks": {"title": "Tasks", "type": "array", "items": {"$ref": "#/definitions/SrTaskDef"}}, "createdAt": {"title": "Created At", "type": "string", "format": "date-time"}, "createdBy": {"title": "Created By", "type": "string", "maxLength": 255, "minLength": 0}, "description": {"title": "Description", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "lastModifiedAt": {"title": "Last Modified At", "type": "string", "format": "date-time"}, "lastModifiedBy": {"title": "Last Modified By", "type": "string", "maxLength": 255, "minLength": 0}, "name": {"title": "Name", "type": "string", "maxLength": 255, "minLength": 0}, "priority": {"title": "Priority", "type": "string", "maxLength": 255, "minLength": 0, "enum": ["1", "2", "3"]}, "rootBusinessObject": {"title": "Root Business Object", "type": "string", "maxLength": 255, "minLength": 0}, "storyID": {"title": "Story ID", "type": "string", "maxLength": 255, "minLength": 0}, "validationRule": {"title": "Validation Rule", "type": "string", "maxLength": 255, "minLength": 0}, "workflowDefID": {"title": "Workflow Def ID", "type": "string", "maxLength": 255, "minLength": 0}}}, "SrInstance": {"type": "object", "title": "SrInstance", "description": "SR Instance", "properties": {"additionalInfo": {"title": "Additional Info", "type": "object", "$ref": "#/definitions/SrAdditionalInfo"}, "currentTask": {"title": "Current Task", "type": "object", "$ref": "#/definitions/SrTask"}, "documents": {"title": "Documents", "type": "array", "items": {"$ref": "#/definitions/Document"}}, "notes": {"title": "Notes", "type": "array", "items": {"$ref": "#/definitions/SrNote"}}, "srDef": {"title": "SR Def", "type": "object", "$ref": "#/definitions/SrDefinition"}, "tasks": {"title": "Tasks", "type": "array", "items": {"$ref": "#/definitions/SrTask"}}, "accountID": {"title": "Account ID", "type": "string", "maxLength": 255, "minLength": 0}, "accountNumber": {"title": "Account Number", "type": "string", "maxLength": 255, "minLength": 0}, "advisorID": {"title": "Advisor ID", "type": "string", "maxLength": 255, "minLength": 0}, "afterCommit": {"title": "After Commit", "type": "string", "maxLength": 200000, "minLength": 0}, "beforeCommit": {"title": "Before Commit", "type": "string", "maxLength": 200000, "minLength": 0}, "boInstanceID": {"title": "BO Instance ID", "type": "string", "maxLength": 255, "minLength": 0}, "clientID": {"title": "Client ID", "type": "string", "maxLength": 255, "minLength": 0}, "clientName": {"title": "Client Name", "type": "string", "maxLength": 255, "minLength": 0}, "closedAt": {"title": "Closed At", "type": "string", "format": "date-time"}, "createdAt": {"title": "Created At", "type": "string", "format": "date-time"}, "createdBy": {"title": "Created By", "type": "string", "maxLength": 255, "minLength": 0}, "elapsedDays": {"title": "Elapsed Days", "type": "string"}, "filterValue": {"title": "Filter Value", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "lastModifiedAt": {"title": "Last Modified At", "type": "string", "format": "date-time"}, "lastModifiedBy": {"title": "Last Modified By", "type": "string", "maxLength": 255, "minLength": 0}, "repCode": {"title": "Rep Code", "type": "string", "maxLength": 255, "minLength": 0}, "sparseObject": {"title": "Sparse Object", "type": "string", "maxLength": 100000, "minLength": 0}, "srDefinitionType": {"title": "SR Definition Type", "type": "string"}, "srId": {"title": "SR ID", "type": "string"}, "srNumber": {"title": "SR Number", "type": "string"}, "workflowID": {"title": "Workflow ID", "type": "string", "maxLength": 255, "minLength": 0}}}, "SrNote": {"type": "object", "title": "SrNote", "description": "SR Note", "properties": {"srInstanceNotes": {"title": "srInstance Notes", "type": "string"}, "createdAt": {"title": "Created At", "type": "string", "format": "date-time"}, "createdBy": {"title": "Created By", "type": "string", "maxLength": 255, "minLength": 0}, "documents": {"title": "Documents", "type": "string", "maxLength": 2096}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "lastModifiedAt": {"title": "Last Modified At", "type": "string", "format": "date-time"}, "lastModifiedBy": {"title": "Last Modified By", "type": "string", "maxLength": 255, "minLength": 0}, "note": {"title": "Note", "type": "string", "maxLength": 255, "minLength": 0}}}, "SrQuery": {"type": "object", "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "SR Query", "properties": {"domain": {"title": "Domain", "type": "string", "maxLength": 255, "minLength": 0}, "domainName": {"title": "Domain Name", "type": "string", "maxLength": 255, "minLength": 0}, "entityID": {"title": "Entity ID", "type": "string", "maxLength": 255, "minLength": 0}, "entityLabel": {"title": "Entity Label", "type": "string", "maxLength": 255, "minLength": 0}, "entityName": {"title": "Entity Name", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "operationID": {"title": "Operation ID", "type": "string", "maxLength": 255, "minLength": 0}, "queryName": {"title": "Query Name", "type": "string", "maxLength": 255, "minLength": 0}, "queryText": {"title": "Query Text", "type": "string", "maxLength": 200000, "minLength": 0}, "serviceID": {"title": "Service ID", "type": "string", "maxLength": 255, "minLength": 0}}}, "SrStateDef": {"type": "object", "title": "SrStateDef", "description": "SR State Def", "properties": {"description": {"title": "Description", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "isFinal": {"title": "IsFinal", "type": "boolean"}, "isStart": {"title": "IsStart", "type": "boolean"}, "name": {"title": "Name", "type": "string", "maxLength": 255, "minLength": 0}, "stateId": {"title": "State Id", "type": "string", "maxLength": 255, "minLength": 0}}}, "SrStatus": {"type": "object", "title": "SrStatus", "description": "SR Status", "properties": {"srTaskStatus": {"title": "srTask Status", "type": "string"}, "status": {"title": "Status", "type": "object", "$ref": "#/definitions/SrStatusCode"}, "createdAt": {"title": "Created At", "type": "string", "format": "date-time"}, "createdBy": {"title": "Created By", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "lastModifiedAt": {"title": "Last Modified At", "type": "string", "format": "date-time"}, "lastModifiedBy": {"title": "Last Modified By", "type": "string", "maxLength": 255, "minLength": 0}}}, "SrStatusCode": {"type": "object", "title": "SrStatusCode", "description": "SR Status Code", "properties": {"description": {"title": "Description", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "isFinal": {"title": "IsFinal", "type": "boolean"}, "isStart": {"title": "IsStart", "type": "boolean"}, "name": {"title": "Name", "type": "string", "maxLength": 255, "minLength": 0}, "stateId": {"title": "State Id", "type": "string", "maxLength": 255, "minLength": 0}}}, "SrSubCategory": {"type": "object", "title": "SrSubCategory", "description": "SR Sub Category", "properties": {"description": {"title": "Description", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "name": {"title": "Name", "type": "string", "maxLength": 255, "minLength": 0}, "rank": {"title": "Rank", "type": "integer"}}}, "SrTask": {"type": "object", "title": "SrTask", "description": "SR Task", "properties": {"assignedTo": {"title": "Assigned To", "type": "object", "$ref": "#/definitions/Associate"}, "currentStatus": {"title": "Current Status", "type": "object", "$ref": "#/definitions/SrStatus"}, "signatures": {"title": "Signatures", "type": "array", "items": {"$ref": "#/definitions/Signature"}}, "srInstanceTasks": {"title": "srInstance Tasks", "type": "string"}, "status": {"title": "Status", "type": "array", "items": {"$ref": "#/definitions/SrStatus"}}, "actualTime": {"title": "Actual Time", "type": "string", "format": "date-time"}, "createdAt": {"title": "Created At", "type": "string", "format": "date-time"}, "createdBy": {"title": "Created By", "type": "string", "maxLength": 255, "minLength": 0}, "esignatureEnvelopeID": {"title": "e-Signature Envelope ID", "type": "string", "maxLength": 255, "minLength": 0}, "estimatedTime": {"title": "Estimated Time", "type": "string", "format": "date-time"}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "lastModifiedAt": {"title": "Last Modified At", "type": "string", "format": "date-time"}, "lastModifiedBy": {"title": "Last Modified By", "type": "string", "maxLength": 255, "minLength": 0}, "response": {"title": "Response", "type": "string", "maxLength": 10000, "minLength": 0}, "sequenceID": {"title": "Sequence ID", "type": "string"}, "signalID": {"title": "Signal ID", "type": "string", "maxLength": 255, "minLength": 0}, "taskDefID": {"title": "Task Def ID", "type": "string", "maxLength": 255, "minLength": 0}, "workflowID": {"title": "Workflow ID", "type": "string", "maxLength": 255, "minLength": 0}}}, "SrTaskDef": {"type": "object", "title": "SrTaskDef", "description": "SR Task Def", "properties": {"status": {"title": "Status", "type": "array", "items": {"$ref": "#/definitions/SrStatusCode"}}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "name": {"title": "Name", "type": "string", "maxLength": 255, "minLength": 0}, "taskId": {"title": "Task Id", "type": "string", "maxLength": 255, "minLength": 0}}}, "SrWorkflowArgs": {"type": "object", "title": "SrWorkflowArgs", "description": "SR Workflow Args", "properties": {"errors": {"title": "Errors", "type": "object", "$ref": "#/definitions/Error"}, "assignTo": {"title": "Assign To", "type": "string", "maxLength": 255, "minLength": 0}, "boInstanceID": {"title": "BO Instance ID", "type": "string", "maxLength": 255, "minLength": 0}, "data": {"title": "Data", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "isCompleted": {"title": "Is Completed", "type": "boolean"}, "preview": {"title": "Preview", "type": "boolean"}, "srDefID": {"title": "SR Def ID", "type": "string", "maxLength": 255, "minLength": 0}, "srInstanceID": {"title": "SR Instance ID", "type": "string", "maxLength": 255, "minLength": 0}, "taskStatus": {"title": "Task Status", "type": "string", "maxLength": 255, "minLength": 0}, "validate": {"title": "Validate", "type": "boolean"}}}, "StandingInstructionsResponse": {"type": "object", "title": "StandingInstructionsResponse", "description": "Standing Instructions Response", "properties": {"bankAccountNumber": {"title": "bankAccountNumber", "type": "string", "maxLength": 255, "minLength": 0}, "bankAccountOwnersName": {"title": "bankAccountOwnersName", "type": "string", "maxLength": 255, "minLength": 0}, "bankAccountType": {"title": "bankAccountType", "type": "string", "maxLength": 255, "minLength": 0}, "bankName": {"title": "bankName", "type": "string", "maxLength": 255, "minLength": 0}, "bankRoutingNumber": {"title": "bankRoutingNumber", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "instructionStatus": {"title": "instructionStatus", "type": "string", "maxLength": 255, "minLength": 0}}}, "StateOrProvince": {"type": "object", "title": "StateOrProvince", "description": "State or Province", "properties": {"country": {"title": "Country", "type": "object", "$ref": "#/definitions/Country"}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "code": {"title": "Code", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "name": {"title": "Name", "type": "string", "maxLength": 255, "minLength": 0}}}, "StepVisibilityCriteria": {"type": "object", "title": "StepVisibilityCriteria", "description": "Step Visibility Criteria", "properties": {"registrationGroup": {"title": "Registration Group", "type": "object", "$ref": "#/definitions/RegistrationTypeGroup"}, "step": {"title": "Step", "type": "array", "items": {"$ref": "#/definitions/AccountCreationStep"}}, "custodian": {"title": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}}}, "SupportedRegistrationTypes": {"type": "object", "title": "SupportedRegistrationTypes", "description": "Supported Registration Types", "properties": {"code": {"title": "Code", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "isAvailable": {"title": "isAvailable", "type": "boolean"}}}, "SupportingDocuments": {"type": "object", "title": "SupportingDocuments", "description": "Supporting Documents", "properties": {"registrationTypeInfo": {"title": "Registration Type Info", "type": "object", "$ref": "#/definitions/RegistrationType"}, "clientName": {"title": "Client Name", "type": "string", "maxLength": 255, "minLength": 0}, "documentTypeName": {"title": "Document Type Name", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}}}, "Task": {"type": "object", "title": "Task", "description": "Task", "properties": {"account": {"title": "Account", "type": "object", "$ref": "#/definitions/Account"}, "assignedTo": {"title": "Assigned To", "type": "object", "$ref": "#/definitions/Associate"}, "createdAt": {"title": "Created At", "type": "string", "format": "date-time"}, "createdBy": {"title": "Created By", "type": "string", "maxLength": 255, "minLength": 0}, "elapsedDays": {"title": "Elapsed Days", "type": "integer"}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "lastModifiedAt": {"title": "Last Modified At", "type": "string", "format": "date-time"}, "lastModifiedBy": {"title": "Last Modified By", "type": "string", "maxLength": 255, "minLength": 0}, "priority": {"title": "Priority", "type": "integer"}, "priorityText": {"title": "Priority Text", "type": "string"}, "repCode": {"title": "Rep Code", "type": "string", "maxLength": 255, "minLength": 0}, "status": {"title": "Status", "type": "string", "maxLength": 255, "minLength": 0}, "statusMessage": {"title": "Status Message", "type": "string", "maxLength": 255, "minLength": 0}}}, "TaxLot": {"type": "object", "title": "TaxLot", "description": "Tax Lot", "properties": {"account": {"title": "Account", "type": "object", "$ref": "#/definitions/Account"}, "currency": {"title": "<PERSON><PERSON><PERSON><PERSON>", "type": "object", "$ref": "#/definitions/Currency"}, "security": {"title": "Security", "type": "object", "$ref": "#/definitions/Security"}, "asOfDate": {"title": "As Of Date", "type": "string", "format": "date"}, "blendedUnitCost": {"title": "Blended Unit Cost", "type": "number", "multipleOf": 1e-09}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "closedDate": {"title": "Closed Date", "type": "string", "format": "date"}, "cusip": {"title": "CUSIP", "type": "string", "maxLength": 255, "minLength": 0}, "custodianAssignedSecurityID": {"title": "Custodian Assigned Security ID", "type": "string", "maxLength": 255, "minLength": 0}, "description": {"title": "Description", "type": "string", "maxLength": 255, "minLength": 0}, "held": {"title": "Held", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "isClosed": {"title": "Is Closed", "type": "boolean"}, "isin": {"title": "ISIN", "type": "string", "maxLength": 255, "minLength": 0}, "longTermRealizedGainOrLoss": {"title": "Long Term Realized Gain or Loss", "type": "number"}, "longTermUnrealizedGainOrLoss": {"title": "Long Term Unrealized Gain or Loss", "type": "number"}, "marketPrice": {"title": "Market Price", "type": "number", "multipleOf": 1e-09}, "marketValue": {"title": "Market Value", "type": "number"}, "openDate": {"title": "Open Date", "type": "string", "format": "date"}, "quantity": {"title": "Quantity", "type": "number", "multipleOf": 1e-05}, "sedol": {"title": "SEDOL", "type": "string", "maxLength": 255, "minLength": 0}, "shortTermRealizedGainOrLoss": {"title": "Short Term Realized Gain or Loss", "type": "number"}, "shortTermUnrealizedGainOrLoss": {"title": "Short Term Unrealized Gain or Loss", "type": "number"}, "symbol": {"title": "Symbol", "type": "string", "maxLength": 255, "minLength": 0}, "taxLotID": {"title": "Tax Lot ID", "type": "string", "maxLength": 255, "minLength": 0}, "term": {"title": "Term", "type": "string", "maxLength": 255, "minLength": 0}, "totalCost": {"title": "Total Cost", "type": "number"}, "totalRealizedGainOrLoss": {"title": "Total Realized Gain or Loss", "type": "number"}, "totalUnrealizedGainOrLoss": {"title": "Total Unrealized Gain or Loss", "type": "number"}, "tradePrice": {"title": "Trade Price", "type": "number", "multipleOf": 1e-05}, "valoren": {"title": "VALOREN", "type": "string", "maxLength": 255, "minLength": 0}}}, "Templatedemail": {"type": "object", "title": "Templatedemail", "description": "Templatedemail", "properties": {"attachments": {"title": "attachments", "type": "string"}, "data": {"title": "data", "type": "string"}, "providerName": {"title": "providerName", "type": "string", "minLength": 0}, "templatePath": {"title": "templatePath", "type": "string", "minLength": 0}}}, "Transfer": {"type": "object", "title": "Transfer", "description": "Transfer", "properties": {"bankAccount": {"title": "Bank Account", "type": "object", "$ref": "#/definitions/AccountSourceOfFunds"}, "otherAccount": {"title": "Other Account", "type": "object", "$ref": "#/definitions/SecuritiesTransferSourceOrReceiver"}, "securities": {"title": "Securities", "type": "array", "items": {"$ref": "#/definitions/SecuritiesTransferSecurity"}}, "sender": {"title": "Sender", "type": "object", "$ref": "#/definitions/Person"}, "taxFilingState": {"title": "Tax Filing State", "type": "object", "$ref": "#/definitions/StateOrProvince"}, "accountHolderMeetsAgeRequirement": {"title": "Account Holder Meets age requirement", "type": "boolean"}, "amount": {"title": "Amount", "type": "number"}, "bankAccountTransferType": {"title": "Bank Account Transfer Type", "type": "string", "maxLength": 255, "minLength": 0}, "bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "cdMaturityDate": {"title": "CD Maturity Date", "type": "string", "format": "date"}, "checkDepositOption": {"title": "Check Deposit Option", "type": "string", "maxLength": 255, "minLength": 0}, "checkPayableTo": {"title": "Check Payable To", "type": "string", "maxLength": 255, "minLength": 0}, "closeAccount": {"title": "Close Account", "type": "boolean"}, "contributionYear": {"title": "Contribution Year", "type": "string", "maxLength": 255, "minLength": 0}, "costBasisStepUp": {"title": "Cost Basis Step Up", "type": "boolean"}, "createdAt": {"title": "Created At", "type": "string", "format": "date-time"}, "dateOfDeath": {"title": "Date of Death", "type": "string", "format": "date"}, "detailsToBeProvided": {"title": "Details to be provided", "type": "boolean"}, "distributeSecurities": {"title": "Distribute Securities", "type": "boolean"}, "excessContributionAmount": {"title": "Excess Contribution Amount", "type": "number"}, "excessContributionDate": {"title": "Excess Contribution Date", "type": "string", "format": "date"}, "excessContributionEarnings": {"title": "Excess Contribution Earnings", "type": "number"}, "federalWithholdingAmount": {"title": "Federal Withholding Amount", "type": "number"}, "federalWithholdingPercent": {"title": "Federal Withholding Percent", "type": "number", "maximum": 100.0, "minimum": 0.0}, "frequency": {"title": "Frequency", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "internalTransferAssetTypeOptions": {"title": "Internal Transfer Asset Type Options", "type": "string", "maxLength": 255, "minLength": 0}, "internalTransferType": {"title": "Internal Transfer Type", "type": "string", "maxLength": 255, "minLength": 0}, "irADistributionAmount": {"title": "IRA Distribution Amount", "type": "string", "maxLength": 255, "minLength": 0}, "irADistributionFrequency": {"title": "IRA Distribution Frequency", "type": "string", "maxLength": 255, "minLength": 0}, "irADistributionReason": {"title": "IRA Distribution Reason", "type": "string", "maxLength": 255, "minLength": 0}, "isForProductPurchase": {"title": "Is For Product Purchase", "type": "boolean"}, "isIRAContribution": {"title": "Is IRA Contribution", "type": "boolean"}, "isPriorYearExcessContribution": {"title": "Is Prior-Year Excess Contribution", "type": "boolean"}, "isRolloverWithin60Days": {"title": "Is Rollover within 60 days", "type": "boolean"}, "isThirdParty": {"title": "Is Third Party", "type": "boolean"}, "lastModifiedBy": {"title": "Last Modified By", "type": "string", "maxLength": 255, "minLength": 0}, "memo": {"title": "Memo", "type": "string", "maxLength": 2000, "minLength": 0}, "netAttributableIncome": {"title": "Net Attributable Income", "type": "number"}, "notes": {"title": "Notes", "type": "string", "maxLength": 2000, "minLength": 0}, "optOutOfStateWithholding": {"title": "Opt Out of State Withholding", "type": "boolean"}, "ownerAddressType": {"title": "Owner Address Type", "type": "string", "maxLength": 255, "minLength": 0}, "partialTransferAmount": {"title": "Partial Transfer Amount", "type": "number"}, "partialTransferPercent": {"title": "Partial Transfer Percent", "type": "number", "maximum": 100.0, "minimum": 0.0}, "reCharacterizationYear": {"title": "Re-characterization Year", "type": "string", "maxLength": 255, "minLength": 0}, "replaceExistingInstruction": {"title": "Replace Existing Instruction", "type": "boolean"}, "requestSource": {"title": "Request Source", "type": "string", "maxLength": 255, "minLength": 0}, "retainForFutureTransfers": {"title": "Retain for Future Transfers", "type": "boolean"}, "rolloverQuantityChoice": {"title": "Rollover Quantity Choice", "type": "string", "maxLength": 255, "minLength": 0}, "rothIRA5YearRequirementMet": {"title": "Roth IRA 5-Year Requirement Met", "type": "boolean"}, "securitiesTransferType": {"title": "Securities Transfer Type", "type": "string", "maxLength": 255, "minLength": 0}, "siMPLEIRAOriginalFundingDate": {"title": "SIMPLE IRA Original Funding Date", "type": "string", "format": "date"}, "specialDelivery": {"title": "Special Delivery", "type": "string", "maxLength": 255, "minLength": 0}, "srId": {"title": "SR Id", "type": "string", "maxLength": 255, "minLength": 0}, "standingInstruction": {"title": "Standing Instruction", "type": "boolean"}, "startDate": {"title": "Start Date", "type": "string", "format": "date"}, "stateWithholdingAmount": {"title": "State Withholding Amount", "type": "number"}, "stateWithholdingOption": {"title": "State Withholding Option", "type": "string", "maxLength": 255, "minLength": 0}, "stateWithholdingPercent": {"title": "State Withholding Percent", "type": "number", "maximum": 100.0, "minimum": 0.0}, "stateWithholdingPercentOfFederal": {"title": "State Withholding Percent of Federal", "type": "number", "maximum": 100.0, "minimum": 0.0}, "status": {"title": "Status", "type": "string", "maxLength": 255, "minLength": 0}, "taxYear": {"title": "Tax Year", "type": "integer"}, "transferType": {"title": "Transfer Type", "type": "string", "maxLength": 255, "minLength": 0}, "typeOfFunds": {"title": "Type of funds", "type": "string", "maxLength": 255, "minLength": 0}, "useForAccountFunding": {"title": "Use for Account Funding", "type": "boolean"}, "withholdingType": {"title": "Withholding Type", "type": "string", "maxLength": 255, "minLength": 0}}}, "TransferStatusResponse": {"type": "object", "title": "TransferStatusResponse", "description": "Transfer Status Response", "properties": {"id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "messages": {"title": "messages", "type": "string", "maxLength": 2000, "minLength": 0}, "requestId": {"title": "requestId", "type": "string", "maxLength": 255, "minLength": 0}, "sequence": {"title": "sequence", "type": "string", "maxLength": 255, "minLength": 0}, "status": {"title": "status", "type": "string", "maxLength": 255, "minLength": 0}, "timestamp": {"title": "timestamp", "type": "string", "format": "date-time"}}}, "UiToggleFields": {"type": "object", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>s", "description": "UI Toggle Fields", "properties": {"clientSelection": {"title": "Client Selection", "type": "string", "maxLength": 255, "minLength": 0, "enum": ["Search For a Client", "Add New Client"]}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "summaryView": {"title": "Summary View", "type": "string", "maxLength": 255, "minLength": 0, "enum": ["View Forms", "Account Summary"]}}}, "UserRepCodeMapping": {"type": "object", "title": "UserRepCodeMapping", "description": "UserRepCodeMapping", "properties": {"bulkLoadRecIdJfyApx": {"title": "Bulk Load Record Id", "type": "string"}, "bulkLoadRunIdJfyApx": {"title": "Bulk Load Run Id", "type": "string"}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "repCode": {"title": "Rep Code", "type": "string"}, "userId": {"title": "User Id", "type": "string", "maxLength": 255, "minLength": 0}}}, "ValidationConfig": {"type": "object", "title": "ValidationConfig", "description": "Validation Config", "properties": {"rule": {"title": "Rule", "type": "object", "$ref": "#/definitions/ValidationRule"}, "enabled": {"title": "Enabled", "type": "boolean"}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "override": {"title": "Override", "type": "boolean"}}}, "ValidationRule": {"type": "object", "title": "ValidationRule", "description": "Validation Rule", "properties": {"description": {"title": "Description", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "name": {"title": "Name", "type": "string", "maxLength": 255, "minLength": 0}, "ruleset": {"title": "Ruleset", "type": "string", "maxLength": 255, "minLength": 0}}}, "WireCreateResponse": {"type": "object", "title": "WireCreateResponse", "description": "Wire Create Response", "properties": {"id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "requestId": {"title": "requestId", "type": "string", "maxLength": 255, "minLength": 0}}}, "WizardForm": {"type": "object", "title": "WizardForm", "description": "Wizard Form", "properties": {"custodianName": {"title": "Custodian Name", "type": "string", "maxLength": 255, "minLength": 0}, "docCategory": {"title": "Doc Category", "type": "string", "maxLength": 255, "minLength": 0}, "docTypeName": {"title": "Doc Type Name", "type": "string", "maxLength": 255, "minLength": 0}, "firmName": {"title": "Firm Name", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}}}, "WizardStep": {"type": "object", "title": "WizardStep", "description": "Wizard Step", "properties": {"custodianName": {"title": "Custodian Name", "type": "string", "maxLength": 255, "minLength": 0}, "id": {"title": "id", "type": "string", "format": "uuid", "maxLength": 255, "minLength": 0}, "regTypeName": {"title": "Reg Type Name", "type": "string", "maxLength": 255, "minLength": 0}, "stepName": {"title": "Step Name", "type": "string", "maxLength": 255, "minLength": 0}}}}}