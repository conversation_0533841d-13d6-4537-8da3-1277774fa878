[{"id": 1, "question": "Get account details including id and account number from Account", "answer": {"Account": {"select": {"id": true, "accountNumber": true}}}, "context": "", "dynamic_query_explanation": "", "dynamic_query_type": "simple select query"}, {"id": 2, "question": "Get account details including id and account number, ordered by the primary owner's last name in descending order", "answer": {"Account": {"select": {"id": true, "accountNumber": true}, "orderBy": "desc(primaryOwner.lastName)"}}, "context": "", "dynamic_query_explanation": "", "dynamic_query_type": "Select with order by on linked field"}, {"id": 3, "question": "Get account details including id, account number, and primary owner's first and last name, filter by account number 1234 and balance greater than or equal to 10000, order by balance descending and account number, skip first result and limit to 10 records", "answer": {"Account": {"select": {"id": true, "accountNumber": true, "primaryOwner": {"select": {"owner": {"select": {"firstName": true, "lastName": true}}}}}, "filter": ["accountNumber == '1234'", "balance >= '10000'"], "orderBy": ["desc(balance)", "accountNumber"], "offset": 1, "limit": 10}}, "context": "", "dynamic_query_explanation": "", "dynamic_query_type": "Select query with filter and order by, offset, limit"}, {"id": 4, "question": "Get account details including id and account number, with primary owner's first and last name only if owner's first name is <PERSON>, filter accounts where account number is either 1234 or 5678 and balance is over 1000, order results by account number in descending order", "answer": {"Account": {"select": {"id": true, "accountNumber": true, "primaryOwner": {"select": {"firstName": true, "lastName": true}, "filter": "firstName == 'Alice' "}}, "filter": "(accountNumber == '1234' || accountNumber == '5678') && balance > 1000", "orderBy": "desc(accountNumber)"}}, "context": "", "dynamic_query_explanation": "", "dynamic_query_type": "Select query with nested filters and order by"}, {"id": 5, "question": "Count total accounts and find minimum balance for savings and current accounts with balance over 1000", "answer": {"Account": {"aggregate": {"count": "${count(id)}", "minBalance": "${min(balance)}"}, "filter": "(accountType == 'Savings' || accountType == 'Current') && balance > 1000"}}, "context": "", "dynamic_query_explanation": "", "dynamic_query_type": "Aggregate query with filter"}, {"id": 6, "question": "Group accounts by account type and creation date, showing count and minimum balance for accounts created since 2024 with balance over 100", "answer": {"Account": {"aggregate": {"count": "${count(id)}", "minBalance": "${min(balance)}"}, "filter": "createdOn >= '2024-01-01' && balance >= 100", "groupBy": ["accountType", "createdOn"]}}, "context": "", "dynamic_query_explanation": "", "dynamic_query_type": "Group by query with filter"}, {"id": 8, "question": "Get all fields from match summary", "answer": {"matchSummary": {"select": {"*": true}}}, "context": "", "dynamic_query_explanation": "", "dynamic_query_type": "Select all fields"}, {"id": 9, "question": "Get account details including id and account number, with all fields of primary owner where primary owner's first name is <PERSON> and account number is either 1234 or 5678 with balance over 1000, order results by account number in descending order", "answer": {"Account": {"select": {"id": true, "accountNumber": true, "primaryOwner": {"select": {"*": true}, "filter": "owner.firstName == 'Alice' "}}, "filter": "(accountNumber == '1234' || accountNumber == '5678') && balance > 1000", "orderBy": "desc(accountNumber)"}}, "context": "", "dynamic_query_explanation": "", "dynamic_query_type": "Select all fields of link"}, {"id": 10, "question": "Get detailed account statistics grouped by account type and creation date, including count, min, max, average, and total balance for accounts since 2024 with balance greater than or equal to 100", "answer": {"Account": {"aggregate": {"count": "${count(id)}", "minBalance": "${min(balance)}", "maxBalance": "${max(balance)}", "avgBalance": "${avg(balance)}", "totalBalance": "${sum(balance)}"}, "filter": "createdOn >= '2024-01-01' && balance >= 100", "groupBy": ["accountType", "createdOn"]}}, "context": "", "dynamic_query_explanation": "", "dynamic_query_type": "Group by with filter and include group by keys"}, {"id": 11, "question": "Get distinct account types for accounts with status 'Active'", "answer": {"Account": {"distinct": {"accountType": true}, "filter": "accountStatus == 'Active'"}}, "context": "", "dynamic_query_explanation": "", "dynamic_query_type": "Distinct with filter"}, {"id": 12, "question": "Count total number of accounts with status 'Active'", "answer": {"Account": {"aggregate": {"count": "${count(id)}"}, "filter": "accountStatus == 'Active'"}}, "context": "", "dynamic_query_explanation": "", "dynamic_query_type": "Count with filter"}, {"id": 14, "question": "Search for accounts starting with '<PERSON>' and return name, description, and relevance score for the top 10 results, ordered by relevance score in descending order", "answer": {"with": [{"key": "res", "value": {"Account": {"search": {"text": "Raj:*"}}}}], "${__.res.object}": {"select": {"name": true, "description": true, "score": "${__.res.score}"}, "orderBy": "desc(__.res.score)", "limit": 10}}, "context": "", "dynamic_query_explanation": "", "dynamic_query_type": "Full text search query (Works only when full text is enabled)"}, {"id": 15, "question": "Get security IDs and create a new field named 'description' that combines securityDescription1 and securityDescription2 fields", "answer": {"Security": {"select": {"id": true, "description": "${securityDescription1 + securityDescription2}"}}}, "context": "", "dynamic_query_explanation": "", "dynamic_query_type": "Concatenate two fields"}, {"id": 16, "question": "Search for securities containing 'BGMO' in their securityDescription or symbol fields", "answer": {"Security": {"textsearch": {"fields": ["securityDescription", "symbol"], "word": "BGMO"}}}, "context": "", "dynamic_query_explanation": "", "dynamic_query_type": "Full text search based on Pg_trgm"}, {"id": 17, "question": "Get account details including id and account number, with list of beneficiaries's first and last names only if owner's first name is <PERSON>, filter accounts where account number is either 1234 or 5678 and balance is over 1000, order results by account number in descending order", "answer": {"Account": {"select": {"id": true, "accountNumber": true, "beneficiaries": {"select": {"firstName": true, "lastName": true}, "filter": "firstName == 'Alice' "}}, "filter": "(accountNumber == '1234' || accountNumber == '5678') && balance > 1000", "orderBy": "desc(accountNumber)"}}, "context": "", "dynamic_query_explanation": "", "dynamic_query_type": "Select query with nested filters and order by"}]