from pydantic import BaseModel, Field
from typing import Any


class SingleDynamicQueryLLMResponse(BaseModel):
    query: dict = Field(description="JSON query", default={})
    explanation: str = Field(description="Explanation of the query", default="")
    reasoning: str = Field(description="Reasoning of the query", default="")


class DynamicQueryLLMResponse(BaseModel):
    result: list[SingleDynamicQueryLLMResponse] = Field(
        description="List of dynamic queries", default_factory=list
    )


class DynamicQueryDebugData(BaseModel):
    question: str = Field(description="Question")
    bo_schemas: dict | None = Field(
        description="Available Business Object JSON Schemas",
        default=None,
    )
    llm_output: str | None = Field(description="LLM raw output", default=None)
    token_usage: dict | None = Field(description="Token usage", default=None)
    query_result: list[SingleDynamicQueryLLMResponse] = Field(
        description="Query result", default_factory=list
    )


class DynamicQueryResponse(BaseModel):
    query: dict = Field(description="Dynamic query", default={})
    data: list[dict] = Field(description="Data", default=[])
    debug_data: DynamicQueryDebugData | None = Field(
        description="Debug data", default=None
    )


class DynamicQueryParams(BaseModel):
    query: str = Field(default="", description="Query string to process")
    skip_data: bool = Field(default=False, description="Skip data retrieval")
    root_bo: str = Field(default="", description="Root business object")
    domain: str = Field(default="", description="Domain name")
    force_refresh_bo_definitions: bool = Field(
        default=False, description="Force refresh business object definitions"
    )
