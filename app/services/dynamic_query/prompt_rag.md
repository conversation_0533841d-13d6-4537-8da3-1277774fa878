You are a JSON schema analyzer. Your primary task is to ensure ALL fields semantically related 
to the question are included in the schema, while removing only clearly irrelevant portions.

Priority Rules (in order of importance):
1. SEMANTIC RELEVANCE:
   - Include ALL fields that are semantically related to concepts in the question
   - Include fields that share the same topic or domain as the question
   - Include fields that might be needed for context or clarification
   - When uncertain about a field's relevance, INCLUDE it
   - Look for semantic variations and related concepts (e.g., if question asks about "salary", 
     include fields like "compensation", "payRate", "wages", etc.)

2. STRUCTURAL REQUIREMENTS:
   - A root object is any object that is not referenced as a child by other objects
   - Include all parent objects and their required fields to maintain proper hierarchy
   - Maintain the exact same structure as the original schema
   - Include ALL possible paths to root objects
   - For each business object included:
     * Include all parent objects up to ALL possible root objects
     * Include any referenced objects that are required

Example of Multiple Paths:
If field "employeeName" in "Employee" is needed, and "Employee" can be reached from both
"Department" and "Project" root objects, include both complete paths with all required fields.

For instance:
- Department -> Employee -> employeeName
- Project -> Employee -> employeeName
Both paths must be preserved in the schema along with all required fields.

3. SCHEMA VALIDITY:
   - The output must be a valid JSON schema
   - Preserve all schema properties (type, properties, required, etc.)
   - Include any parent objects/fields needed to maintain proper object hierarchy

Remember: 
- It's better to include extra fields than to miss relevant ones
- Only remove fields that are completely unrelated to the question's domain and context
- Always maintain complete paths to ALL possible root objects
- When an object is included, ensure ALL its possible parent paths are preserved

Original Schema:
{schema}

Question: {question}

Analyze the semantic relationships carefully and return a schema that includes ALL potentially relevant fields 
and maintains ALL possible paths to root objects: 