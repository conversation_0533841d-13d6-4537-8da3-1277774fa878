from fastapi import APIRout<PERSON>, Request, HTTPException
from fastapi.responses import <PERSON><PERSON><PERSON>esponse

from .data_extractor import DataExtractor
from .type import DataExtractionPayload
from core.jiffy_drive import JiffyDrive


router = APIRouter(tags=["Data Extraction"])


@router.post("/data-extraction")
async def data_extraction_endpoint(request: Request, payload: DataExtractionPayload):
    tenant_id = request.headers.get("X-Jiffy-Tenant-ID", None)
    app_id = request.headers.get("X-Jiffy-App-ID", None)
    if not tenant_id or not app_id:
        raise HTTPException(
            status_code=400, detail="Tenant ID and App ID are required in headers"
        )

    if payload.content.startswith("private/") or payload.content.startswith("shared/"):
        jiffy_drive = JiffyDrive()
        payload.content = await jiffy_drive.download_file_from_jiffydrive(
            payload.content
        )

    data_extractor = DataExtractor(tenant_id, app_id, payload.prompt)
    response = await data_extractor.extract(
        payload.content, payload.boEntityId, payload.fieldsList
    )
    if response["status"]:
        return JSONResponse(content=response["data"])
    else:
        raise HTTPException(status_code=500, detail=response["error"])
