import os
from tarfile import SUPPORTED_TYPES


class TableMLConstants(object):
    """Global variables"""

    IMG_DPI = 300


OPENAI_API_TIMEOUT = int(os.environ.get("OPENAI_API_TIMEOUT", 60))
BACKOFF_MAX_TRIES = int(os.environ.get("BACKOFF_MAX_TRIES", 3))
BACKOFF_MAX_TIME = int(os.environ.get("BACKOFF_MAX_TIME", 120))
SUPPORTED_IMAGE_TYPES = ["jpg", "png", "gif", "webp"]
SUPPORTED_AUDIO_TYPES = ["mp3", "mp4", "mpweg", "mpga", "m4a", "wav", "webm"]
