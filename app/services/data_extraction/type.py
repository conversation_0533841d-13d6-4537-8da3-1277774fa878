from typing import TypedDict, List, Literal, Optional, Union
from pydantic import BaseModel, Field, field_validator
from common.type import ModelName

class Attribute(TypedDict):
    type: str
    targetId: str
    isComposition: bool


MultiplicityTypes = Literal["ONE_TO_ONE",
                            "ONE_TO_MANY", "MANY_TO_ONE", "MANY_TO_MANY"]


class BOAssociation(TypedDict):
    attribute: Attribute
    multiplicity: MultiplicityTypes
    type: Literal["COMPOSITION", "AGGREGATION"]
    isSource: bool


class LiteralValue(TypedDict):
    value: str
    type: Literal["string"]


class ConstraintArgument(TypedDict):
    list: 'dict[Literal["list"], List[dict[Literal["literal"], LiteralValue]]]'


class PredefinedConstraint(TypedDict):
    constraintType: Literal["MAX_LENGTH", "ALLOWED_VALUES", "MIN_LENGTH"]
    argument: ConstraintArgument


class FieldConstraint(TypedDict):
    predefined: PredefinedConstraint


class BOField(TypedDict):
    label: str
    name: str
    type: str
    uuid: str
    artifactId: str
    association: BOAssociation
    constraints: List[FieldConstraint]
    array: Optional['dict[Literal["itemType"], Literal["string"]]']


class Structured(TypedDict):
    name: str
    uuid: str
    label: str
    modelInfo: 'dict[Literal["entityType"], Literal["REGULAR", "REFERENCE_DATA"]]'


class BOEntity(TypedDict):
    structured: Structured
    fields: List[BOField]


class FieldsListType(TypedDict):
    name: str
    uuid: str
    displayName: str
    boAttributeName: str
    boAttributeId: str
    fullBoAttributeId: str
    fullBoAttributeName: str


class DataExtractionPayload(BaseModel):
    content: str = Field(description="Content to be extracted. It can be a text or a file path.")
    boEntityId: str
    fieldsList: "Union[List[str], str]" = Field(default=[])
    prompt: str = Field(default="")
    modelName: ModelName = Field(default=ModelName.GEMINI_20_FLASH, description="Model to use for extraction")
    
    @field_validator('modelName', mode='before')
    @classmethod
    def validate_model_name(cls, value):
        if value == "":
            return ModelName.GEMINI_20_FLASH
        return value