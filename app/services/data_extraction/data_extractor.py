import sys
import os
import traceback
import asyncio
import mimetypes

from pathlib import Path
from typing import Dict, List, Union
from google import genai
from google.genai import types as genai_types
from httpx import AsyncClient

from .constants import SUPPORTED_IMAGE_TYPES, SUPPORTED_AUDIO_TYPES
from .type import BOEntity
from .helper import encode_file


from core.config import SERVICES_PROPERTIES, AUTH_PROPERTIES
from core.auth import get_headers
from common.type import ModelName

BASEPATH = os.path.abspath(os.path.dirname(__file__))


def get_type_info(type: str):
    if type == "Checkbox":
        return {"type": "boolean"}
    elif type == "Number":
        return {"type": "number"}
    else:
        return {"type": "string"}


class DataExtractor:
    def __init__(
        self,
        tenant_id: str,
        app_id: str,
        prompt="",
        model_name=ModelName.GEMINI_20_FLASH,
    ):
        self.tenant_id = tenant_id
        self.app_id = app_id
        self.bo_cache: "Dict[str, BOEntity]" = {}
        self.genai_client = genai.Client(
            vertexai=True,
            project=AUTH_PROPERTIES.GOOGLE_CLOUD_PROJECT,
            location=AUTH_PROPERTIES.GOOGLE_CLOUD_LOCATION,
        )
        self.model_name = model_name

        if prompt:
            self.prompt_text = prompt
            return

        with open(os.path.join(BASEPATH, "./prompt.md"), "r") as fp:
            self.prompt_text = fp.read()

    async def _get_bo_entity(self, bo_entity_id: str) -> BOEntity:
        url = f"{SERVICES_PROPERTIES.MODEL_REPO_URL}/tenant/{self.tenant_id}/app/{self.app_id}/composite/entity/{bo_entity_id}"
        headers = await get_headers()
        async with AsyncClient() as client:
            response = await client.get(url, headers=headers, timeout=30)
            response.raise_for_status()
            return response.json()

    @staticmethod
    def _handle_pre_defined_fields(field, schema):
        if field["type"] != "Singlelinetext":
            return False

        allowed_values = []
        for constraint in field.get("constraints", []):
            if constraint["predefined"]["constraintType"] != "ALLOWED_VALUES":
                continue

            for literal in constraint["predefined"]["argument"]["list"]["list"]:
                allowed_values.append(literal["literal"]["value"])

        if len(allowed_values) == 0:
            return False

        if "array" in field:
            schema["properties"][field["name"]] = {
                "type": "array",
                "enum": allowed_values,
            }
        else:
            schema["properties"][field["name"]] = {
                "enum": allowed_values,
                "type": "string",
            }

        return True

    async def _handle_linked_field(
        self, field, schema, required_fields, full_name, depth
    ):
        child_bo_entity_id = field["association"]["attribute"]["targetId"]
        if depth > 5:
            return

        if field["association"]["multiplicity"] in ["ONE_TO_ONE", "MANY_TO_ONE"]:
            child_schmea = await self._generate_json_schema(
                child_bo_entity_id,
                required_fields,
                full_name,
                depth + 1,
            )
            if len(child_schmea["properties"]) > 0:
                schema["properties"][field["name"]] = child_schmea
        else:
            child_schmea = await self._generate_json_schema(
                child_bo_entity_id,
                required_fields,
                full_name,
                depth + 1,
            )
            if len(child_schmea["properties"]) > 0:
                schema["properties"][field["name"]] = {
                    "type": "array",
                    "items": child_schmea,
                }

    async def _generate_json_schema(
        self, bo_entity_id: str, required_fields=[], parent_name="", depth=1
    ):
        schema = {"type": "object", "properties": {}}
        if bo_entity_id not in self.bo_cache:
            bo = await self._get_bo_entity(bo_entity_id)
            self.bo_cache[bo_entity_id] = bo
        else:
            bo = self.bo_cache[bo_entity_id]

        for field in bo["fields"]:
            full_id = f"{parent_name}.{field['uuid']}" if parent_name else field["uuid"]
            if field["name"] == "id" or (
                required_fields
                and ("association" not in field and full_id not in required_fields)
            ):
                continue

            is_predefined_field = self._handle_pre_defined_fields(field, schema)
            if is_predefined_field:
                continue

            if "association" in field:
                await self._handle_linked_field(
                    field, schema, required_fields, full_id, depth
                )
            else:
                schema["properties"][field["name"]] = get_type_info(field["type"])

        return schema

    async def _llm_extract(
        self,
        json_schema: Dict,
        content: str,
    ):
        config = genai_types.GenerateContentConfig(
            system_instruction=genai_types.Content(
                parts=[genai_types.Part(text=self.prompt_text)],
                role="modal",
            ),
            response_mime_type="application/json",
            response_schema=json_schema,
            temperature=0,
        )

        contents = []
        if os.path.exists(content):
            file_path = content
            mime_type, _ = mimetypes.guess_type(file_path)
            data = Path(content).read_bytes()
            contents.extend(
                [
                    "Extract the data from the document and return it in JSON format.",
                    genai_types.Part.from_bytes(data=data, mime_type=mime_type),
                ]
            )
        else:
            contents.append(genai_types.Part(text=content))

        response = await self.genai_client.aio.models.generate_content(
            model=self.model_name,
            contents=contents,
            config=config,
        )
        return response.parsed

    async def extract(
        self,
        content: str,
        bo_entity_id: str,
        fields_list: "Union[List[str], str]" = [],
    ):
        response = {}

        if isinstance(fields_list, str):
            fields_list = fields_list.split(",")
        try:

            json_schema = await self._generate_json_schema(bo_entity_id, fields_list)
            data = await self._llm_extract(json_schema, content)
            response["status"] = True
            response["data"] = data
        except Exception as e:
            response["status"] = False
            response["error"] = str(e)

            traceback.print_exc()

        return response


if __name__ == "__main__":
    tenant_id = "34ff8202-ada1-44b6-aed0-bc8e8387923e"
    app_id = "791d5f47-d903-46c5-acad-52fa75259932"
    headers = {
        "X-Jiffy-Tenant-ID": tenant_id,
        "X-Jiffy-App-ID": app_id,
        "Authorization": "Bearer eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJNNW4zM1dpb0F2RU16S0JjSFVGM0drcXQ0VnM4RjdicUpxVFN0aUFtRTdFIn0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.OpJxLerMeXSauWBIuIUHcrfCtr0jT4dPcVd3PRxe6tUK3bkYk3N7cjw18JEBzQCv8lOU7iQeTd5PhiVb_pQdme0Sdo_VbARjEOK1Dtc53Go2_6jZijgkDNpinTgtgnuyFfYMmeeexU0zgc0X7ZCDC0ZRe2LKU-YG4_vcjopTXXKnjSdYuKf6vWa6xUTNBTVx78zy_ldEgZyExfxdDMntNB-c-d6uw7Wt97VN8M_Ii_r7AdJNLpmulJXPZbaRzqGWuD3HOgMBqjj1ZRzUJOQ8huv43TQ6GYuq9XDqNEIDkBBiJ3p4IWHh64ep75Qr-93v47DEnWFlDnwgbJBlIVLCUA",
    }
    extractor = DataExtractor(tenant_id, app_id)
    # file_path = "/home/<USER>/Downloads/amazon_image.jpg"
    file_path = "/home/<USER>/Downloads/test.wav"
    # file_path = "/home/<USER>/Downloads/Amazon1.pdf"
    res = asyncio.run(extractor.extract(file_path, "crt866r56bukkbluclc0"))

    import json

    with open("out.json", "w") as f:
        json.dump(res, f)
