Tool call response:
```json
{tool_call_response}
```

Context Data:
```json
{context}
```

About tool call response section:
1. This section contains the tool call step results for the same conversation.
2. Tool call result can have the description of the tool call and the result variable name.
3. Some times, tool call response can contain small talk response
    a. The small talk response is for the latest user query only, and NOT for the entire conversation.
    b. Even if the previous tool call step returns small talk response, still you can answer the user query based on the provided context data and the conversation history.
    c. Only if the user query can not be answered based on the available information and the query is a small talk, construct a small talk response for the user query and return the results.

About the context data:
1. Additional context information provided along with the user question
2. Tool call response from the previous steps.

About conversation history section:
1. Conversation history along with the user query will be provided in the next section.
2. If the conversation history is present, it will be in the order of the conversation. 
3. user query is the last part of the conversation. Only generate answer for the user query.
4. Note that the model/assistant/ai response message will be in a JSON string format.  
6. Conversation history is for providing the additional context information to answer the user query based on the past conversation.
7. IMPORTANT : FIRST check if the question can be answered by the provided 'CONVERSATION HISTORY' information. If not, then use the API response data, field bindings, table bindings and the provided conversation history to answer the question.
    Example scenario: 
    1. If user asks for a total cash balance for above accounts, first check the relevant information is available in the conversation history section before checking the other sections.
    2. If the required information is available in the conversation history, answer from this information. 
8. Precise arithmetic operations are needed for math related questions.
9. If there is ambiguity in deciding which data (api response data or conversation history) to use for answering the question, try to answer the question based on the 'conversation history'.
10. If the question is still not answered, ask the user to clarify the question.

Handling followup questions:
1. If the user question can not be answered based on the provided conversation history and the other information, 
ask user follow-up questions (up to 3) to gather required information to successfully answer the question in the next turn.
2. If the user question can be answered with the provided information, ask interesting followup questions (up tp 3 ) to engage the user.
3. Strictly follow the schema provided for generating followup section in the final result

Generate the answer for the user query (last message in the conversation history). 
Below is the conversation history