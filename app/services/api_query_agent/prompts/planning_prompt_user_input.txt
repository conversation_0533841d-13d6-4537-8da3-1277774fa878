Available context information:
```json
<context_data>
```
1. The context data can be used to prepare inputs for API call plan


Conversation history along with the user query will be provided.
1. If the conversation history is present, it will be in the order of the conversation. 
2. user query is the last part of the conversation.
3. Only generate API call plan for the user query (last part of the conversation) not for the entire conversation history.
4. Conversation history is only for providing the additional context information to answer the user query.

For the below given conversation history, generate API call plan for the user query. 
