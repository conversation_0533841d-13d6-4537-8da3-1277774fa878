{"pages": [{"id": "homePage", "title": "Home", "url": "/home", "parameters": [], "description": "Landing page for the app, showing top 5 accounts as a table, portfolio allocation as a pie chart etc.", "endpoints": [{"description": "Get Top 5 Accounts By Ending Market Value. Note: This is NOT for fetching client accounts", "url": "/api/domain/wealthdomain/account/dynamic/query/topAccountsByEndingMarketValue?", "base_url": "http://app-data-manager:8002", "method": "get", "query_params": {"excludeStatus": "Draft,Data%20Capture,Validation%20Failed,Validation%20Successful,Forms%20Generating,Form%20Generation%20Failed,Forms%20Generated,Options%20Pre-approval%20Pending,Options%20Approved,Initiating%20E-Signature,E-Signing,E-Signing%20Cancelled,E-Sign%20Expired,E-Signing%20Failed,E-Signed,Signing,Signed,Supervisor%20Approval%20Pending,Rep%20Action%20Required,Rejected,Approved,Account%20Setup%20In%20Progress,Account%20Setup%20Failure,Account%20Opened,Margin%20Review%20Required,Margin%20Disallowed,Margin%20Review%20Completed,Forms%20Uploading,Forms%20Failed%20to%20Load,Forms%20Successfully%20Uploaded,Wet%20Sign%20Cancelled,Ready%20for%20QC%20Review,Options%20Review%20Pending"}, "path_params": {}, "requestBody": {}, "responseSchema": {"type": "array", "items": {"accountNumber": "string", "accountStatus": "string", "name": "string", "dailyBalances": {"type": "object", "properties": {"endingCashBalance": "currency", "endingMarginBalance": "currency", "endingMarketValue": "currency", "endingMoneyMarketBalance": "currency"}}, "registrationType": "string"}}, "resultVariable": "top5AccountsByEndingMarketValue", "references": {"tables": [{"name": "Top 5 Accounts", "description": "Top 5 accounts by ending market value", "type": "table", "columns": [{"name": "Account Number", "type": "string", "fieldBinding": "${!isEmpty(accountDetail.accountNumber)? accountDetail.accountNumber : \" \"}"}, {"name": "Account Name", "type": "string", "fieldBinding": "${!isEmpty(accountDetail.name)? accountDetail.name  : \" \"}"}, {"name": "Registration Type", "type": "string", "fieldBinding": "${!isEmpty(accountDetail.registrationType.name)? accountDetail.registrationType.name : \" \"}"}, {"name": "Account Status", "type": "string", "fieldBinding": "${!isEmpty(accountDetail.accountStatus)? accountDetail.accountStatus : \" \"}"}, {"name": "Market Value", "type": "currency", "fieldBinding": "${!isEmpty(accountDetail.dailyBalances.endingMarketValue)? accountDetail.dailyBalances.endingMarketValue : \" \"}"}, {"name": "Cash Balance", "type": "currency", "fieldBinding": "${!isEmpty(accountDetail.dailyBalances.endingCashBalance)? accountDetail.dailyBalances.endingCashBalance : \" \"}"}, {"name": "<PERSON>gin <PERSON>", "type": "currency", "fieldBinding": "${!isEmpty(accountDetail.dailyBalances.endingMarginBalance)? accountDetail.dailyBalances.endingMarginBalance : \" \"}"}, {"name": "MMF Balance", "type": "currency", "fieldBinding": "${!isEmpty(accountDetail.dailyBalances.endingMoneyMarketBalance)? accountDetail.dailyBalances.endingMoneyMarketBalance : \" \"}"}]}]}, "id": "1fb09e9b-a2ad-4106-8ba7-3789c1615d52"}, {"description": "Get Monthly Account Balances of advisor OR Assets Over Time/Period for 3 months, 6 months etc. Mostly used for a bar chart", "url": "/workflow/v1/instances/execute/sync/d0a7sue8eadump4qcv0g", "base_url": "http://workhorse:8080", "method": "post", "query_params": {}, "path_params": {}, "requestBody": {"arguments": {"periodInMonths": "${Period in months}"}}, "responseSchema": {"type": "array", "items": {"endingEts": "currency", "id": "string", "periodEndDate": "Date (YYYY-MM-DD)", "periodType": "string"}}, "resultVariable": "assetsOverTimeForAdvisor", "references": {"charts": [{"name": "Assets Over Time/Period", "description": "Assets over a time/period like 3 months, 6 months etc.", "type": "bar", "schema": {"type": "array", "items": {"endingEts": "number", "id": "string", "periodEndDate": "Date (YYYY-MM-DD)", "periodType": "string"}}}]}, "id": "e7c202b8-b85f-459c-9775-c7f5dc6f3380"}, {"description": "Get Portfolio Allocation or Asset allocation by asset class for an advisor.Mostly used for a pie chart", "url": "/api/domain/wealthdomain/getAdvisorAssetAllocationByPeriodEndDateAndUserId", "base_url": "http://app-data-manager:8002", "method": "get", "query_params": {"_fields": "securityCategory,allocationPercentage", "periodEndDate": "${gvGlobalAsOfDate}", "userId": "${userId}"}, "path_params": {}, "requestBody": {}, "responseSchema": {"type": "array", "items": {"securityCategory": "string", "totalMarketValue": "currency"}}, "resultVariable": "userPortfolioAllocation", "references": {"charts": [{"name": "Portfolio Allocation", "description": "Portfolio allocation or Asset allocation by asset class", "type": "pie", "schema": {"type": "array", "items": {"securityCategory": "string", "totalMarketValue": "number"}}}]}, "id": "999b10f4-62ca-428c-ba8f-bf3488fcf5b1"}, {"description": "Book of Business Summary. The data is based on active accounts associated with the advisor. NOTE: This is used to get the total number of accounts, total value, cash balance and margin balance for the advisor. This api can be referred for querying the total number of accounts", "url": "/api/domain/wealthdomain/advisorAccountBalances/dynamic/query/byAdvisorIdPeriodTypePeriodEndDate", "base_url": "http://app-data-manager:8002", "method": "get", "query_params": {"periodType": "${period type. Default is MTD}", "currentUserId": "${userId}", "periodEndDate": "${gvGlobalAsOfDate}"}, "path_params": {}, "requestBody": {}, "responseSchema": {"type": "array", "items": {"advisorAccountBalances": {"type": "object", "properties": {"endingAccounts": "number", "endingBalance": "currency", "endingCashBalance": "currency", "endingMarginBalance": "currency"}}}}, "resultVariable": "listOfAdvisorAccountSummaries", "references": {"fieldBindings": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "The advisor account balances for the client. This is used to get the total number of accounts, total value, cash balance and margin balance for the advisor.", "type": "object", "value": "${!isEmpty(listOfAdvisorAccountSummaries.advisorAccountBalances[0])? listOfAdvisorAccountSummaries.advisorAccountBalances[0] : {}}"}, {"name": "pvAdvisorTotalAccounts", "description": "The total number of accounts for the advisor.", "type": "number", "value": "${!isEmpty(advisorAccountSummary.endingAccounts)? advisorAccountSummary.endingAccounts : 0}"}, {"name": "pvAdvisorCashAvailable", "description": "The total cash available for the advisor.", "type": "currency", "value": "${!isEmpty(advisorAccountSummary.endingCashBalance)? advisorAccountSummary.endingCashBalance : 0}"}, {"name": "pvAdvisorMarginBalance", "description": "The margin balance for the advisor.", "type": "currency", "value": "${!isEmpty(advisorAccountSummary.endingMarginBalance)? advisorAccountSummary.endingMarginBalance : 0}"}, {"name": "pvAdvisorTotalValue", "description": "The total value for the advisor.", "type": "currency", "value": "${!isEmpty(advisorAccountSummary.endingBalance)? advisorAccountSummary.endingBalance : 0}"}]}, "id": "af7f93ec-4fa1-411e-abd9-fc3e71cc77ed"}, {"description": "Get open service requests count", "url": "/api/domain/wealthdomain/srInstance/dynamic/query/getSRKPITotalRequest", "base_url": "http://app-data-manager:8002", "method": "get", "query_params": {"skipSubCategory": "true", "skipBoInstanceID": "true", "skipIncludeList": "true", "skipExcludeList": "false", "excludeList": "Rejected,Closed,Deleted,Expired"}, "path_params": {}, "requestBody": {}, "responseSchema": {"type": "array", "items": {"type": "number"}}, "resultVariable": "openServiceRequestsList", "references": {"fieldBindings": [{"name": "Open Service Requests", "description": "The total number of open service requests for the advisor.", "type": "number", "value": "${!isEmpty(openServiceRequestsList[0])? openServiceRequestsList[0] : \" \"}", "section": "/Open Service Requests"}]}, "id": "d9381693-9aa0-47f7-8141-9dcccaa7473e"}, {"description": "Get service requests(SR) count by type. This is used to get the summary of service requests by type.", "url": "/api/domain/wealthdomain/openTicketsForEachSRType_aggregate", "base_url": "http://app-data-manager:8002", "method": "get", "query_params": {"_size": "${10}", "_sort": "-ticketCount"}, "path_params": {}, "requestBody": {}, "responseSchema": {"type": "array", "items": {"type": "object", "properties": {"srDefinitionType": {"type": "string", "description": "Type of service request definition"}, "ticketCount": {"type": "number", "description": "Number of tickets for the service request type"}}}}, "resultVariable": "openTicketsForEachSRType_aggregate", "references": {"fieldBindings": [{"name": "ACH In", "description": "The total number of ACH In service requests for the advisor.", "type": "number", "value": "${openTicketsForEachSRType_aggregate.ticketCount for openTicketsForEachSRType_aggregate.srDefinitionType == \"ACH In\"}", "section": "/Service Requests"}, {"name": "ACH Out", "description": "The total number of ACH Out service requests for the advisor.", "type": "number", "value": "${openTicketsForEachSRType_aggregate.ticketCount for openTicketsForEachSRType_aggregate.srDefinitionType == \"ACH Out\"`}", "section": "/Service Requests"}, {"name": "Beneficiary Update", "description": "The total number of Beneficiary Update service requests for the advisor.", "type": "number", "value": "${openTicketsForEachSRType_aggregate.ticketCount for openTicketsForEachSRType_aggregate.srDefinitionType == \"Beneficiary Update\"`}", "section": "/Service Requests"}, {"name": "Invest Cash", "description": "The total number of Invest Cash service requests for the advisor.", "type": "number", "value": "${openTicketsForEachSRType_aggregate.ticketCount for openTicketsForEachSRType_aggregate.srDefinitionType == \"Invest Cash\"`}", "section": "/Service Requests"}, {"name": "Raise Cash", "description": "The total number of Raise Cash service requests for the advisor.", "type": "number", "value": "${openTicketsForEachSRType_aggregate.ticketCount for openTicketsForEachSRType_aggregate.srDefinitionType == \"Raise Cash\"`}", "section": "/Service Requests"}]}, "id": "389cd813-cbab-40a8-a416-f567da9692aa"}, {"description": "Get onboarding service requests count", "url": "/api/domain/wealthdomain/task/dynamic/query/countTasksWithStatusAndType", "base_url": "http://app-data-manager:8002", "method": "get", "query_params": {}, "path_params": {}, "requestBody": {}, "responseSchema": {"type": "array", "items": {"type": "number"}}, "resultVariable": "openOnboardingRequestsList", "id": "cf5b4b9c-d338-4f3c-8a9e-d558c5e933da"}], "tables": [{"name": "Top 5 Accounts", "description": "Top 5 accounts by ending market value", "type": "table", "columns": [{"name": "Account Number", "type": "string", "fieldBinding": "${!isEmpty(accountDetail.accountNumber)? accountDetail.accountNumber : \" \"}"}, {"name": "Account Name", "type": "string", "fieldBinding": "${!isEmpty(accountDetail.name)? accountDetail.name  : \" \"}"}, {"name": "Registration Type", "type": "string", "fieldBinding": "${!isEmpty(accountDetail.registrationType.name)? accountDetail.registrationType.name : \" \"}"}, {"name": "Account Status", "type": "string", "fieldBinding": "${!isEmpty(accountDetail.accountStatus)? accountDetail.accountStatus : \" \"}"}, {"name": "Market Value", "type": "currency", "fieldBinding": "${!isEmpty(accountDetail.dailyBalances.endingMarketValue)? accountDetail.dailyBalances.endingMarketValue : \" \"}"}, {"name": "Cash Balance", "type": "currency", "fieldBinding": "${!isEmpty(accountDetail.dailyBalances.endingCashBalance)? accountDetail.dailyBalances.endingCashBalance : \" \"}"}, {"name": "<PERSON>gin <PERSON>", "type": "currency", "fieldBinding": "${!isEmpty(accountDetail.dailyBalances.endingMarginBalance)? accountDetail.dailyBalances.endingMarginBalance : \" \"}"}, {"name": "MMF Balance", "type": "currency", "fieldBinding": "${!isEmpty(accountDetail.dailyBalances.endingMoneyMarketBalance)? accountDetail.dailyBalances.endingMoneyMarketBalance : \" \"}"}]}, {"name": "Open Tickets For Service Request (SR) Types", "description": "Number Of Open Service Requests Under Each Type", "type": "table", "columns": [{"name": "Type", "fieldBinding": "${!isEmpty(srDetail.srDefinitionType)? srDetail.srDefinitionType : \" \"}"}, {"name": "Count", "fieldBinding": "${srDetail.ticketCount}"}]}], "charts": [{"name": "Portfolio Allocation", "description": "Portfolio allocation or Asset allocation by asset class", "type": "pie", "schema": {"type": "array", "items": {"securityCategory": "string", "totalMarketValue": "number"}}}, {"name": "Assets Over Time/Period", "description": "Assets over a time/period like 3 months, 6 months etc.", "type": "bar", "schema": {"type": "array", "items": {"endingEts": "number", "id": "string", "periodEndDate": "Date (YYYY-MM-DD)", "periodType": "string"}}}], "fieldBindings": [{"name": "Total Accounts", "description": "The total number of accounts for the advisor.", "type": "number", "value": "${pvAdvisorTotalAccounts. Default is 0}", "section": "/Book of Business Summary"}, {"name": "Cash Balance", "description": "The total cash available for the advisor.", "type": "currency", "value": "${pvAdvisorCashAvailable. Default is 0}", "section": "/Book of Business Summary"}, {"name": "<PERSON>gin <PERSON>", "description": "The margin balance for the advisor.", "type": "currency", "value": "${pvAdvisorMarginBalance. Default is 0}", "section": "/Book of Business Summary"}, {"name": "Total Value", "description": "The total value for the advisor.", "type": "currency", "value": "${pvAdvisorTotalValue. Default is 0}", "section": "/Book of Business Summary"}, {"name": "Onboarding Requests", "description": "The total number of open onboarding requests for the advisor.", "type": "number", "value": "${!isEmpty(openOnboardingRequestsList[0])? openOnboardingRequestsList[0] : \" \"}", "section": "/Onboarding Requests"}, {"name": "Open Service Requests", "description": "The total number of open service requests for the advisor.", "type": "number", "value": "${!isEmpty(openServiceRequestsList[0])? openServiceRequestsList[0] : \" \"}", "section": "/Open Service Requests"}, {"name": "ACH In", "description": "The total number of ACH In service requests for the advisor.", "type": "number", "value": "${openTicketsForEachSRType_aggregate.ticketCount for openTicketsForEachSRType_aggregate.srDefinitionType == \"ACH In\"}", "section": "/Service Requests"}, {"name": "ACH Out", "description": "The total number of ACH Out service requests for the advisor.", "type": "number", "value": "${openTicketsForEachSRType_aggregate.ticketCount for openTicketsForEachSRType_aggregate.srDefinitionType == \"ACH Out\"`}", "section": "/Service Requests"}, {"name": "Beneficiary Update", "description": "The total number of Beneficiary Update service requests for the advisor.", "type": "number", "value": "${openTicketsForEachSRType_aggregate.ticketCount for openTicketsForEachSRType_aggregate.srDefinitionType == \"Beneficiary Update\"`}", "section": "/Service Requests"}, {"name": "Invest Cash", "description": "The total number of Invest Cash service requests for the advisor.", "type": "number", "value": "${openTicketsForEachSRType_aggregate.ticketCount for openTicketsForEachSRType_aggregate.srDefinitionType == \"Invest Cash\"`}", "section": "/Service Requests"}, {"name": "Raise Cash", "description": "The total number of Raise Cash service requests for the advisor.", "type": "number", "value": "${openTicketsForEachSRType_aggregate.ticketCount for openTicketsForEachSRType_aggregate.srDefinitionType == \"Raise Cash\"`}", "section": "/Service Requests"}]}, {"id": "accountsListPage", "title": "Accounts List", "url": "/accounts", "parameters": [], "description": "Accounts list for the advisor", "endpoints": [{"description": "Get Accounts list sorted based on ending market value. Note: 1. This is a paginated api and will not return data for all the accounts. 2. This api is not for getting client account (a client can have multiple accounts and client listing api is different) details, it is for getting all the accounts for an advisor.", "url": "/api/domain/wealthdomain/getAccountsFilteredByAccountStatus", "base_url": "http://app-data-manager:8002", "method": "get", "query_params": {"_page": "0", "_size": "${10}", "_sort": "-dailyBalances.endingMarketValue", "_fields": "registrationType.name,primaryOwner.mailingAddress.state.name,dailyBalances.endingMarketValue,dailyBalances.fedCall,primaryOwner.id,dailyBalances.endingBalance,primaryOwner.owner.legalAddress.state.id,registrationType.id,riskTolerance,accountStatus,dailyBalances.maintenanceCall,relatedAccounts.linkedAccounts.dailyBalances.id,dailyBalances.id,dailyBalances.endingMarginBalance,relatedAccounts.id,relatedAccounts.linkedAccounts.id,primaryOwner.owner.legalAddress.id,primaryOwner.mailingAddress.id,registrationType.code,primaryOwner.owner.id,dailyBalances.endingCashBalance,primaryOwner.owner.legalAddress.state.name,accountNumber,investmentObjective,id,primaryOwner.mailingAddress.state.id,repCode,dailyBalances.endingMoneyMarketBalance,name,dailyBalances.periodEndDate", "excludeStatus": "Data%20Capture,Validation%20Failed,Validation%20Successful,Forms%20Generating,Form%20Generation%20Failed,Forms%20Generated,Options%20Pre-approval%20Pending,Options%20Approved,Initiating%20E-Signature,E-Signing,E-Signing%20Cancelled,E-Sign%20Expired,E-Signing%20Failed,E-Signed,Signing,Signed,Supervisor%20Approval%20Pending,Rep%20Action%20Required,Rejected,Approved,Account%20Setup%20In%20Progress,Account%20Setup%20Failure,Account%20Opened,Margin%20Review%20Required,Margin%20Disallowed,Margin%20Review%20Completed,Forms%20Uploading,Forms%20Failed%20to%20Load,Forms%20Successfully%20Uploaded,Wet%20Sign%20Cancelled,Ready%20for%20QC%20Review,Options%20Review%20Pending"}, "path_params": {}, "requestBody": {}, "responseSchema": {"type": "array", "items": {"accountNumber": "string", "accountStatus": "string", "dailyBalances": {"type": "object", "properties": {"endingBalance": "number", "endingCashBalance": "number", "endingMarginBalance": "number", "endingMarketValue": "number", "endingMoneyMarketBalance": "number", "fedCall": "number", "id": "string", "maintenanceCall": "number", "periodEndDate": "string"}}, "id": "string", "investmentObjective": "string", "name": "string", "primaryOwner": {"type": "object", "properties": {"id": "string", "mailingAddress": "string", "owner": {"type": "object", "properties": {"id": "string", "legalAddress": "string"}}}}, "registrationType": "string", "relatedAccounts": {"type": "array", "items": "string"}, "repCode": "string", "riskTolerance": "string"}}, "resultVariable": "accountsDataTable", "references": {"tables": [{"name": "Accounts List", "description": "Accounts list based on the ending market value", "columns": [{"name": "Account Number", "type": "string", "fieldBinding": "${!isEmpty(accountsDataTable[i].accountNumber)? accountsDataTable[i].accountNumber : \" \"}"}, {"name": "Account Name", "type": "string", "fieldBinding": "${!isEmpty(accountsDataTable[i].name)? accountsDataTable[i].name : \" \"}"}, {"name": "Rep Code", "type": "string", "fieldBinding": "${!isEmpty(accountsDataTable[i].repCode)? accountsDataTable[i].repCode : \" \"}"}, {"name": "Registration Type", "type": "string", "fieldBinding": "${!isEmpty(accountsDataTable[i].registrationType.name)? accountsDataTable[i].registrationType.name : \" \"}"}, {"name": "Registration Code", "type": "string", "fieldBinding": "${!isEmpty(accountsDataTable[i].registrationType.custodianCode)? accountsDataTable[i].registrationType.custodianCode : \" \"}"}, {"name": "State", "type": "string", "fieldBinding": "${!isEmpty(accountsDataTable[i].jointTenancyState.name)? accountsDataTable[i].jointTenancyState.name : \" \"}"}, {"name": "Investment Objective", "type": "string", "fieldBinding": "${!isEmpty(accountsDataTable[i].investmentObjective)? accountsDataTable[i].investmentObjective : \" \"}"}, {"name": "Risk Tolerance", "type": "string", "fieldBinding": "${!isEmpty(accountsDataTable[i].riskTolerance)? accountsDataTable[i].riskTolerance : \" \"}"}, {"name": "Account Status", "type": "string", "fieldBinding": "${!isEmpty(accountsDataTable[i].accountStatus)? accountsDataTable[i].accountStatus : \" \"}"}, {"name": "Account Value", "type": "currency", "fieldBinding": "${!isEmpty(accountsDataTable[i].dailyBalances.endingBalance)? accountsDataTable[i].dailyBalances.endingBalance : \" \"}"}, {"name": "Cash Value", "type": "currency", "fieldBinding": "${!isEmpty(accountsDataTable[i].dailyBalances.endingCashBalance)? accountsDataTable[i].dailyBalances.endingCashBalance : \" \"}"}, {"name": "MMF Balance", "type": "currency", "fieldBinding": "${!isEmpty(accountsDataTable[i].dailyBalances.endingMoneyMarketBalance)? accountsDataTable[i].dailyBalances.endingMoneyMarketBalance : \" \"}"}, {"name": "<PERSON>gin <PERSON>", "type": "currency", "fieldBinding": "${!isEmpty(accountsDataTable[i].dailyBalances.endingMarginBalance)? accountsDataTable[i].dailyBalances.endingMarginBalance : \" \"}"}, {"name": "House Surplus", "type": "currency", "fieldBinding": "${!isEmpty(accountsDataTable[i].dailyBalances.maintenanceCall)? accountsDataTable[i].dailyBalances.maintenanceCall : \" \"}"}, {"name": "Open Current Fed Call", "type": "currency", "fieldBinding": "${!isEmpty(accountsDataTable[i].dailyBalances.fedCall)? accountsDataTable[i].dailyBalances.fedCall : \" \"}"}]}]}, "id": "e850f277-beed-495c-8008-ca35aece6d07"}, {"description": "Get account details or insights based on dynamic query. Note: This is a dynamic query API used to query accounts based on select, aggregate, filter, sort, pagination criteria. Note:This is used only if no other endpoint is applicable for the query.", "type": "DYNAMIC_QUERY_TOOL", "url": "/api/domain/wealthdomain/account/dynamic/query", "method": "post", "query_params": {}, "path_params": {}, "requestBody": "${Prepare the dynamic query input based on the user question}", "responseSchema": {"type": "array", "items": {"type": "object", "properties": {}}}, "resultVariable": "accountsInfo", "dynamicQueryData": {"defaultQuery": {"Account": {"select": {"id": true, "name": true, "accountNumber": true, "repCode": true, "registrationType": {"select": {"name": true, "custodianCode": true}}}, "jointTenancyState": {"select": {"name": true}}, "investmentObjective": true, "riskTolerance": true, "accountStatus": true, "dailyBalances": {"select": {"endingMarketValue": true, "endingBalance": true, "endingCashBalance": true, "endingMoneyMarketBalance": true, "endingMarginBalance": true, "maintenanceCall": true, "fedCall": true}}}, "filter": "!includes('Data%20Capture,Validation%20Failed,Validation%20Successful,Forms%20Generating,Form%20Generation%20Failed,Forms%20Generated,Options%20Pre-approval%20Pending,Options%20Approved,Initiating%20E-Signature,E-Signing,E-Signing%20Cancelled,E-Sign%20Expired,E-Signing%20Failed,E-Signed,Signing,Signed,Supervisor%20Approval%20Pending,Rep%20Action%20Required,Rejected,Approved,Account%20Setup%20In%20Progress,Account%20Setup%20Failure,Account%20Opened,Margin%20Review%20Required,Margin%20Disallowed,Margin%20Review%20Completed,Forms%20Uploading,Forms%20Failed%20to%20Load,Forms%20Successfully%20Uploaded,Wet%20Sign%20Cancelled,Ready%20for%20QC%20Review,Options%20Review%20Pending', accountStatus)", "filterOptions": {"coalesce": false}, "orderBy": "desc(.marketValue)", "limit": 5}, "BusinessObjectSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "definitions": {"Account": {"type": "object", "title": "Account", "description": "Account", "properties": {"id": {"title": "id", "type": "string", "format": "uuid"}, "name": {"title": "Name", "type": "string"}, "accountNumber": {"title": "Account Number", "type": "string"}, "repCode": {"title": "Rep Code", "type": "string"}, "registrationType": {"title": "Registration Type", "type": "object", "$ref": "#/definitions/RegistrationType"}, "jointTenancyState": {"title": "Joint Tenancy State", "type": "object", "$ref": "#/definitions/StateOrProvince"}, "investmentObjective": {"title": "Investment Objective", "type": "string"}, "riskTolerance": {"title": "Risk Tolerance", "type": "string"}, "accountStatus": {"title": "Account Status", "type": "string"}, "dailyBalances": {"title": "Daily Balances", "type": "object", "$ref": "#/definitions/AccountBalances"}, "primaryOwnerName": {"title": "Primary Owner Name", "type": "string"}}}, "RegistrationType": {"type": "object", "title": "RegistrationType", "description": "Registration Type", "properties": {"id": {"title": "id", "type": "string", "format": "uuid"}, "name": {"title": "Name", "type": "string"}, "custodianCode": {"title": "Custodian Code", "type": "string"}}}, "StateOrProvince": {"type": "object", "title": "StateOrProvince", "description": "State or Province", "properties": {"id": {"title": "id", "type": "string", "format": "uuid"}, "name": {"title": "Name", "type": "string"}}}, "AccountBalances": {"type": "object", "title": "AccountBalances", "description": "Account Balances", "properties": {"endingBalance": {"title": "Ending Balance", "type": "number"}, "endingCashBalance": {"title": "Ending Cash Balance", "type": "number"}, "endingMarginBalance": {"title": "Ending <PERSON><PERSON>", "type": "number"}, "endingMarketValue": {"title": "Ending Market Value", "type": "number"}, "endingMoneyMarketBalance": {"title": "Ending Money Market Balance", "type": "number"}, "maintenanceCall": {"title": "Maintenance Call", "type": "number"}, "fedCall": {"title": "Fed Call", "type": "number"}}}}}}, "references": {"fieldBindings": [{"name": "Account Number", "type": "string", "value": "accountsInfo[i].accountNumber"}, {"name": "Account Name", "type": "string", "value": "accountsInfo[i].name"}, {"name": "Rep Code", "type": "string", "value": "accountsInfo[i].repCode"}, {"name": "Registration Type", "type": "string", "value": "accountsInfo[i].registrationType.name"}, {"name": "Registration Code", "type": "string", "value": "accountsInfo[i].registrationType.custodianCode"}, {"name": "State", "type": "string", "value": "accountsInfo[i].jointTenancyState.name"}, {"name": "Investment Objective", "type": "string", "value": "accountsInfo[i].investmentObjective"}, {"name": "Risk Tolerance", "type": "string", "value": "accountsInfo[i].riskTolerance"}, {"name": "Account Status", "type": "string", "value": "accountsInfo[i].accountStatus"}, {"name": "Account Value", "type": "currency", "value": "accountsInfo[i].dailyBalances.endingBalance"}, {"name": "Cash Value", "type": "currency", "value": "accountsInfo[i].dailyBalances.endingCashBalance"}, {"name": "MMF Balance", "type": "currency", "value": "accountsInfo[i].dailyBalances.endingMoneyMarketBalance"}, {"name": "<PERSON>gin <PERSON>", "type": "currency", "value": "accountsInfo[i].dailyBalances.endingMarginBalance"}, {"name": "House Surplus", "type": "currency", "value": "accountsInfo[i].dailyBalances.maintenanceCall"}, {"name": "Open Current Fed Call", "type": "currency", "value": "accountsInfo[i].dailyBalances.fedCall"}, {"name": "Market Value", "type": "currency", "value": "accountsInfo[i].dailyBalances.endingMarketValue"}]}, "id": "314befbc-9588-4171-988d-5846314f89b4"}], "tables": [{"name": "Accounts List", "description": "Accounts list based on the ending market value", "columns": [{"name": "Account Number", "type": "string", "fieldBinding": "${!isEmpty(accountsDataTable[i].accountNumber)? accountsDataTable[i].accountNumber : \" \"}"}, {"name": "Account Name", "type": "string", "fieldBinding": "${!isEmpty(accountsDataTable[i].name)? accountsDataTable[i].name : \" \"}"}, {"name": "Rep Code", "type": "string", "fieldBinding": "${!isEmpty(accountsDataTable[i].repCode)? accountsDataTable[i].repCode : \" \"}"}, {"name": "Registration Type", "type": "string", "fieldBinding": "${!isEmpty(accountsDataTable[i].registrationType.name)? accountsDataTable[i].registrationType.name : \" \"}"}, {"name": "Registration Code", "type": "string", "fieldBinding": "${!isEmpty(accountsDataTable[i].registrationType.custodianCode)? accountsDataTable[i].registrationType.custodianCode : \" \"}"}, {"name": "State", "type": "string", "fieldBinding": "${!isEmpty(accountsDataTable[i].jointTenancyState.name)? accountsDataTable[i].jointTenancyState.name : \" \"}"}, {"name": "Investment Objective", "type": "string", "fieldBinding": "${!isEmpty(accountsDataTable[i].investmentObjective)? accountsDataTable[i].investmentObjective : \" \"}"}, {"name": "Risk Tolerance", "type": "string", "fieldBinding": "${!isEmpty(accountsDataTable[i].riskTolerance)? accountsDataTable[i].riskTolerance : \" \"}"}, {"name": "Account Status", "type": "string", "fieldBinding": "${!isEmpty(accountsDataTable[i].accountStatus)? accountsDataTable[i].accountStatus : \" \"}"}, {"name": "Account Value", "type": "currency", "fieldBinding": "${!isEmpty(accountsDataTable[i].dailyBalances.endingBalance)? accountsDataTable[i].dailyBalances.endingBalance : \" \"}"}, {"name": "Cash Value", "type": "currency", "fieldBinding": "${!isEmpty(accountsDataTable[i].dailyBalances.endingCashBalance)? accountsDataTable[i].dailyBalances.endingCashBalance : \" \"}"}, {"name": "MMF Balance", "type": "currency", "fieldBinding": "${!isEmpty(accountsDataTable[i].dailyBalances.endingMoneyMarketBalance)? accountsDataTable[i].dailyBalances.endingMoneyMarketBalance : \" \"}"}, {"name": "<PERSON>gin <PERSON>", "type": "currency", "fieldBinding": "${!isEmpty(accountsDataTable[i].dailyBalances.endingMarginBalance)? accountsDataTable[i].dailyBalances.endingMarginBalance : \" \"}"}, {"name": "House Surplus", "type": "currency", "fieldBinding": "${!isEmpty(accountsDataTable[i].dailyBalances.maintenanceCall)? accountsDataTable[i].dailyBalances.maintenanceCall : \" \"}"}, {"name": "Open Current Fed Call", "type": "currency", "fieldBinding": "${!isEmpty(accountsDataTable[i].dailyBalances.fedCall)? accountsDataTable[i].dailyBalances.fedCall : \" \"}"}]}]}, {"id": "BalancesListingPage", "title": "Balances", "url": "/balances", "parameters": [], "description": "Balances list for the advisor", "endpoints": [{"description": "Get balances list sorted based on ending market value", "url": "/api/domain/wealthdomain/account/dynamic/query/accountsByStatusAndLatestDate", "base_url": "http://app-data-manager:8002", "method": "get", "query_params": {"_page": "0", "_size": "${10}", "_sort": "-dailyBalances.endingMarketValue", "excludeStatus": "Data%20Capture,Validation%20Failed,Validation%20Successful,Forms%20Generating,Form%20Generation%20Failed,Forms%20Generated,Options%20Pre-approval%20Pending,Options%20Approved,Initiating%20E-Signature,E-Signing,E-Signing%20Cancelled,E-Sign%20Expired,E-Signing%20Failed,E-Signed,Signing,Signed,Supervisor%20Approval%20Pending,Rep%20Action%20Required,Rejected,Approved,Account%20Setup%20In%20Progress,Account%20Setup%20Failure,Account%20Opened,Margin%20Review%20Required,Margin%20Disallowed,Margin%20Review%20Completed,Forms%20Uploading,Forms%20Failed%20to%20Load,Forms%20Successfully%20Uploaded,Wet%20Sign%20Cancelled,Ready%20for%20QC%20Review,Options%20Review%20Pending", "latestExecutionHistoryAsOfDate": "${gvGlobalAsOfDate}"}, "path_params": {}, "requestBody": {}, "responseSchema": {"type": "object", "properties": {"accountNumber": {"type": "string"}, "accountStatus": {"type": "string"}, "branch": {"type": "string"}, "client": {"type": "string"}, "dailyBalances": {"type": "object", "properties": {"accruedDividends": {"type": "number"}, "accumulatedFedCall": {"type": "number"}, "availableFundsToWithdraw": {"type": "number"}, "cashAccountCashAvailable": {"type": "number"}, "cashAccountMarginValue": {"type": "number"}, "cashAccountMarketValue": {"type": "number"}, "cashManagementDDANumber": {"type": "string"}, "endingBalance": {"type": "number"}, "endingBuyingPower": {"type": "number"}, "endingCashBalance": {"type": "number"}, "endingMarginBalance": {"type": "number"}, "endingMarketValue": {"type": "number"}, "endingMoneyMarketBalance": {"type": "number"}, "fedCall": {"type": "number"}, "fundsFrozenForChecks": {"type": "number"}, "id": {"type": "string"}, "liquidationValue": {"type": "number"}, "longMarketValue": {"type": "number"}, "maintenanceCall": {"type": "number"}, "marginAccountCashAvailable": {"type": "number"}, "marginEquityAmount": {"type": "number"}, "miscellaneousCreditOrDebit": {"type": "number"}, "previousAuthorizationLimit": {"type": "number"}, "recentDeposits": {"type": "number"}, "settlementDateBalance": {"type": "number"}, "settlementDateCashBalance": {"type": "number"}, "settlementDateFeeBalance": {"type": "number"}, "settlementDateMarginBalance": {"type": "number"}, "shortMarketValue": {"type": "number"}, "smABalance": {"type": "number"}, "tradeDateBalance": {"type": "number"}, "tradeDateCashBalance": {"type": "number"}, "tradeDateMarginBalance": {"type": "number"}, "tradeDateShortBalance": {"type": "number"}}}, "id": {"type": "string"}, "investmentObjective": {"type": "string"}, "name": {"type": "string"}, "ouLevel2": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}}, "required": ["id", "name"]}, "primaryOwner": {"type": "object", "properties": {"id": {"type": "string"}, "mailingAddress": {"type": "string"}}, "required": ["id", "mailingAddress"]}, "registrationType": {"type": "object", "properties": {"code": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}}}, "relatedAccounts": {"type": "array", "items": {}}, "repCode": {"type": "string"}, "riskTolerance": {"type": "string"}}}, "resultVariable": "balancesDataTable", "references": {"tables": [{"name": "Detailed Balances", "description": "Balances list sorted based on the ending market value", "columns": [{"name": "Account Number", "type": "string", "fieldBinding": "${!isEmpty(balancesDataTable[i].accountNumber)? balancesDataTable[i].accountNumber : \" \"}"}, {"name": "Branch Name", "type": "string", "fieldBinding": "${!isEmpty(balancesDataTable[i].ouLevel2.name)? balancesDataTable[i].ouLevel2.name : \" \"}"}, {"name": "Client Name", "type": "string", "fieldBinding": "${!isEmpty(balancesDataTable[i].client.clientName)? balancesDataTable[i].client.clientName : \" \"}"}, {"name": "Account Value", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.endingBalance)? balancesDataTable[i].dailyBalances.endingBalance : \" \"}"}, {"name": "Long Market Value", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.longMarketValue)? balancesDataTable[i].dailyBalances.longMarketValue : \" \"}"}, {"name": "Short Market Value", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.shortMarketValue)? balancesDataTable[i].dailyBalances.shortMarketValue : \" \"}"}, {"name": "MMF Balance", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.endingMoneyMarketBalance)? balancesDataTable[i].dailyBalances.endingMoneyMarketBalance : \" \"}"}, {"name": "Cash Balance", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.endingCashBalance)? balancesDataTable[i].dailyBalances.endingCashBalance : \" \"}"}, {"name": "Equity", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.marginEquityAmount)? balancesDataTable[i].dailyBalances.marginEquityAmount : \" \"}"}, {"name": "<PERSON>gin <PERSON>", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.endingMarginBalance)? balancesDataTable[i].dailyBalances.endingMarginBalance : \" \"}"}, {"name": "Trade Date Balance", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.tradeDateBalance)? balancesDataTable[i].dailyBalances.tradeDateBalance : \" \"}"}, {"name": "Settlement Date Balance", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.settlementDateBalance)? balancesDataTable[i].dailyBalances.settlementDateBalance : \" \"}"}, {"name": "Settlement Date Fee Balance", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.settlementDateFeeBalance)? balancesDataTable[i].dailyBalances.settlementDateFeeBalance : \" \"}"}, {"name": "SMA Balance", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.smABalance)? balancesDataTable[i].dailyBalances.smABalance : \" \"}"}, {"name": "Open Current Fed Call", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.fedCall)? balancesDataTable[i].dailyBalances.fedCall : \" \"}"}, {"name": "Today's Fed Call", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.accumulatedFedCall)? balancesDataTable[i].dailyBalances.accumulatedFedCall : \" \"}"}, {"name": "Funds Available to Trade", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.availableFundsToTrade)? balancesDataTable[i].dailyBalances.availableFundsToTrade : \" \"}"}, {"name": "Funds Available to Withdraw", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.availableFundsToWithdraw)? balancesDataTable[i].dailyBalances.availableFundsToWithdraw : \" \"}"}, {"name": "Market Value Type 1", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.cashAccountMarketValue)? balancesDataTable[i].dailyBalances.cashAccountMarketValue : \" \"}"}, {"name": "Cash Available Type 1", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.cashAccountCashAvailable)? balancesDataTable[i].dailyBalances.cashAccountCashAvailable : \" \"}"}, {"name": "Cash Available Type 2", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.marginAccountCashAvailable)? balancesDataTable[i].dailyBalances.marginAccountCashAvailable : \" \"}"}, {"name": "Recent Deposits", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.recentDeposits)? balancesDataTable[i].dailyBalances.recentDeposits : \" \"}"}, {"name": "Funds Frozen for Checks", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.fundsFrozenForChecks)? balancesDataTable[i].dailyBalances.fundsFrozenForChecks : \" \"}"}, {"name": "Free CR/Misc DR", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.miscellaneousCreditOrDebit)? balancesDataTable[i].dailyBalances.miscellaneousCreditOrDebit : \" \"}"}, {"name": "Accrued Dividends", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.accruedDividends)? balancesDataTable[i].dailyBalances.accruedDividends : \" \"}"}, {"name": "Cash Management - DDA Number", "type": "string", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.cashManagementDDANumber)? balancesDataTable[i].dailyBalances.cashManagementDDANumber : \" \"}"}, {"name": "Previous Authorization Limit", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.previousAuthorizationLimit)? balancesDataTable[i].dailyBalances.previousAuthorizationLimit : \" \"}"}, {"name": "Trade Date Cash Balance", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.tradeDateCashBalance)? balancesDataTable[i].dailyBalances.tradeDateCashBalance : \" \"}"}, {"name": "Settlement Date Cash Balance", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.settlementDateCashBalance)? balancesDataTable[i].dailyBalances.settlementDateCashBalance : \" \"}"}, {"name": "Trade Date Margin Balance", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.tradeDateMarginBalance)? balancesDataTable[i].dailyBalances.tradeDateMarginBalance : \" \"}"}, {"name": "Settlement Date Margin Balance", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.settlementDateMarginBalance)? balancesDataTable[i].dailyBalances.settlementDateMarginBalance : \" \"}"}, {"name": "Trade Date Short Balance", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.tradeDateShortBalance)? balancesDataTable[i].dailyBalances.tradeDateShortBalance : \" \"}"}, {"name": "Settlement Date Short Balance", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.settlementDateShortBalance)? balancesDataTable[i].dailyBalances.settlementDateShortBalance : \" \"}"}, {"name": "Maintenance Call", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.maintenanceCall)? balancesDataTable[i].dailyBalances.maintenanceCall : \" \"}"}]}]}, "id": "********-8507-477f-8461-02510729ae0f"}], "tables": [{"name": "Detailed Balances", "description": "Balances list sorted based on the ending market value", "columns": [{"name": "Account Number", "type": "string", "fieldBinding": "${!isEmpty(balancesDataTable[i].accountNumber)? balancesDataTable[i].accountNumber : \" \"}"}, {"name": "Branch Name", "type": "string", "fieldBinding": "${!isEmpty(balancesDataTable[i].ouLevel2.name)? balancesDataTable[i].ouLevel2.name : \" \"}"}, {"name": "Client Name", "type": "string", "fieldBinding": "${!isEmpty(balancesDataTable[i].client.clientName)? balancesDataTable[i].client.clientName : \" \"}"}, {"name": "Account Value", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.endingBalance)? balancesDataTable[i].dailyBalances.endingBalance : \" \"}"}, {"name": "Long Market Value", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.longMarketValue)? balancesDataTable[i].dailyBalances.longMarketValue : \" \"}"}, {"name": "Short Market Value", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.shortMarketValue)? balancesDataTable[i].dailyBalances.shortMarketValue : \" \"}"}, {"name": "MMF Balance", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.endingMoneyMarketBalance)? balancesDataTable[i].dailyBalances.endingMoneyMarketBalance : \" \"}"}, {"name": "Cash Balance", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.endingCashBalance)? balancesDataTable[i].dailyBalances.endingCashBalance : \" \"}"}, {"name": "Equity", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.marginEquityAmount)? balancesDataTable[i].dailyBalances.marginEquityAmount : \" \"}"}, {"name": "<PERSON>gin <PERSON>", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.endingMarginBalance)? balancesDataTable[i].dailyBalances.endingMarginBalance : \" \"}"}, {"name": "Trade Date Balance", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.tradeDateBalance)? balancesDataTable[i].dailyBalances.tradeDateBalance : \" \"}"}, {"name": "Settlement Date Balance", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.settlementDateBalance)? balancesDataTable[i].dailyBalances.settlementDateBalance : \" \"}"}, {"name": "Settlement Date Fee Balance", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.settlementDateFeeBalance)? balancesDataTable[i].dailyBalances.settlementDateFeeBalance : \" \"}"}, {"name": "SMA Balance", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.smABalance)? balancesDataTable[i].dailyBalances.smABalance : \" \"}"}, {"name": "Open Current Fed Call", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.fedCall)? balancesDataTable[i].dailyBalances.fedCall : \" \"}"}, {"name": "Today's Fed Call", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.accumulatedFedCall)? balancesDataTable[i].dailyBalances.accumulatedFedCall : \" \"}"}, {"name": "Funds Available to Trade", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.availableFundsToTrade)? balancesDataTable[i].dailyBalances.availableFundsToTrade : \" \"}"}, {"name": "Funds Available to Withdraw", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.availableFundsToWithdraw)? balancesDataTable[i].dailyBalances.availableFundsToWithdraw : \" \"}"}, {"name": "Market Value Type 1", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.cashAccountMarketValue)? balancesDataTable[i].dailyBalances.cashAccountMarketValue : \" \"}"}, {"name": "Cash Available Type 1", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.cashAccountCashAvailable)? balancesDataTable[i].dailyBalances.cashAccountCashAvailable : \" \"}"}, {"name": "Cash Available Type 2", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.marginAccountCashAvailable)? balancesDataTable[i].dailyBalances.marginAccountCashAvailable : \" \"}"}, {"name": "Recent Deposits", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.recentDeposits)? balancesDataTable[i].dailyBalances.recentDeposits : \" \"}"}, {"name": "Funds Frozen for Checks", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.fundsFrozenForChecks)? balancesDataTable[i].dailyBalances.fundsFrozenForChecks : \" \"}"}, {"name": "Free CR/Misc DR", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.miscellaneousCreditOrDebit)? balancesDataTable[i].dailyBalances.miscellaneousCreditOrDebit : \" \"}"}, {"name": "Accrued Dividends", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.accruedDividends)? balancesDataTable[i].dailyBalances.accruedDividends : \" \"}"}, {"name": "Cash Management - DDA Number", "type": "string", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.cashManagementDDANumber)? balancesDataTable[i].dailyBalances.cashManagementDDANumber : \" \"}"}, {"name": "Previous Authorization Limit", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.previousAuthorizationLimit)? balancesDataTable[i].dailyBalances.previousAuthorizationLimit : \" \"}"}, {"name": "Trade Date Cash Balance", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.tradeDateCashBalance)? balancesDataTable[i].dailyBalances.tradeDateCashBalance : \" \"}"}, {"name": "Settlement Date Cash Balance", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.settlementDateCashBalance)? balancesDataTable[i].dailyBalances.settlementDateCashBalance : \" \"}"}, {"name": "Trade Date Margin Balance", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.tradeDateMarginBalance)? balancesDataTable[i].dailyBalances.tradeDateMarginBalance : \" \"}"}, {"name": "Settlement Date Margin Balance", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.settlementDateMarginBalance)? balancesDataTable[i].dailyBalances.settlementDateMarginBalance : \" \"}"}, {"name": "Trade Date Short Balance", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.tradeDateShortBalance)? balancesDataTable[i].dailyBalances.tradeDateShortBalance : \" \"}"}, {"name": "Settlement Date Short Balance", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.settlementDateShortBalance)? balancesDataTable[i].dailyBalances.settlementDateShortBalance : \" \"}"}, {"name": "Maintenance Call", "type": "currency", "fieldBinding": "${!isEmpty(balancesDataTable[i].dailyBalances.maintenanceCall)? balancesDataTable[i].dailyBalances.maintenanceCall : \" \"}"}]}]}, {"id": "PositionsListingPage", "title": "Detailed Positions (or holdings) Listing", "url": "/positions", "parameters": [], "description": "Detailed Account Positions (or holdings) list for the advisor", "endpoints": [{"description": "Get account positions (or holdings) list sorted by number of shares (holdings)", "url": "/api/domain/wealthdomain/advisorPositions/dynamic/query/advisorPositionsByPeriodEndDateAndAdvisor", "base_url": "http://app-data-manager:8002", "method": "get", "query_params": {"_page": "0", "_size": "${10}", "_sort": "-numberOfShares", "periodEndDate": "${gvGlobalAsOfDate}", "userId": "${gvGlobalUserId}"}, "path_params": {}, "requestBody": {}, "responseSchema": {"type": "object", "properties": {"id": "string", "marketValue": "number", "numberOfAccounts": "integer", "numberOfShares": "integer", "security": {"type": "object", "properties": {"callIndicator": "string", "cusip": "string", "custodianAssignedID": "string", "endOfMonthPrice": "number", "id": "string", "isin": "string", "lastPrice": "number", "optionExpiryDate": "string", "securityDescription": "string", "sedol": "string", "strikePrice": "number", "symbol": "string"}}}}, "resultVariable": "positionsDataTable", "references": {"tables": [{"name": "Positions", "description": "Account positions list sorted based on period end date", "columns": [{"name": "Ticker / Symbol", "type": "string", "fieldBinding": "${!isEmpty(positionsDataTable[i].security.symbol)? positionsDataTable[i].security.symbol : \" \"}"}, {"name": "CUSIP", "type": "string", "fieldBinding": "${!isEmpty(positionsDataTable[i].security.cusip)? positionsDataTable[i].security.cusip : \" \"}"}, {"name": "Sec ID", "type": "string", "fieldBinding": "${!isEmpty(positionsDataTable[i].security.custodianAssignedID)? positionsDataTable[i].security.custodianAssignedID : \" \"}"}, {"name": "Description", "type": "string", "fieldBinding": "${!isEmpty(positionsDataTable[i].security.securityDescription)? positionsDataTable[i].security.securityDescription : \" \"}"}, {"name": "Accounts", "type": "integer", "fieldBinding": "${!isEmpty(positionsDataTable[i].numberOfAccounts)? positionsDataTable[i].numberOfAccounts : \" \"}"}, {"name": "Quantity", "type": "integer", "fieldBinding": "${!isEmpty(positionsDataTable[i].numberOfShares)? positionsDataTable[i].numberOfShares : \" \"}"}, {"name": "Market Value", "type": "currency", "fieldBinding": "${!isEmpty(positionsDataTable[i].marketValue)? positionsDataTable[i].marketValue : \" \"}"}, {"name": "Price", "type": "currency", "fieldBinding": "${!isEmpty(positionsDataTable[i].security.lastPrice)? positionsDataTable[i].security.lastPrice : \" \"}"}, {"name": "Strike Price", "type": "currency", "fieldBinding": "${!isEmpty(positionsDataTable[i].security.strikePrice)? positionsDataTable[i].security.strikePrice : \" \"}"}, {"name": "Expiry Date", "type": "string", "fieldBinding": "${!isEmpty(positionsDataTable[i].security.optionExpiryDate)? positionsDataTable[i].security.optionExpiryDate : \" \"}"}, {"name": "Option Type", "type": "string", "fieldBinding": "${!isEmpty(positionsDataTable[i].security.callIndicator)? positionsDataTable[i].security.callIndicator : \" \"}"}, {"name": "ISIN", "type": "string", "fieldBinding": "${!isEmpty(positionsDataTable[i].security.isin)? positionsDataTable[i].security.isin : \" \"}"}, {"name": "SEDOL", "type": "string", "fieldBinding": "${!isEmpty(positionsDataTable[i].security.sedol)? positionsDataTable[i].security.sedol : \" \"}"}]}]}, "id": "1ec20187-d46f-420f-b5f1-282445cd3e63"}, {"description": "Search Security By Symbol. This is a prerequisite for Get positions list based on custodian by using the given search string", "title": "Security Search", "url": "/api/domain/wealthdomain/security/custom/textsearch", "base_url": "http://app-data-manager:8002", "method": "get", "query_params": {"_size": "${10}", "searchString": "${searchSymbol}"}, "path_params": {}, "requestBody": {}, "responseSchema": {"type": "array", "items": {"type": "object", "properties": {"cusip": {"type": "string"}, "custodianAssignedID": {"type": "string"}, "id": {"type": "string"}, "securityDescription": {"type": "string"}, "symbol": {"type": "string"}}}}, "resultVariable": "symbolSearchResults", "references": {"fieldBindings": [{"name": "cusip", "description": "The CUSIP number for the security", "type": "string", "value": "${!isEmpty(symbolSearchResults[0].cusip)? symbolSearchResults[0].cusip : \" \"}"}, {"name": "custodianAssignedID", "description": "The custodian assigned ID for the security", "type": "string", "value": "${!isEmpty(symbolSearchResults[0].custodianAssignedID)? symbolSearchResults[0].custodianAssignedID : \" \"}"}, {"name": "id", "description": "The unique identifier for the security", "type": "string", "value": "${!isEmpty(symbolSearchResults[0].id)? symbolSearchResults[0].id : \" \"}"}, {"name": "securityDescription", "description": "The description of the security", "type": "string", "value": "${!isEmpty(symbolSearchResults[0].securityDescription)? symbolSearchResults[0].securityDescription : \" \"}"}, {"name": "symbol", "description": "The trading symbol for the security", "type": "string", "value": "${!isEmpty(symbolSearchResults[0].symbol)? symbolSearchResults[0].symbol : \" \"}"}]}, "id": "7f3eee24-8754-441f-b991-e56bae12050d"}, {"description": "Get positions list based on holdings by given search symbol/string. Prerequisite - 'Search Security By Symbol'", "url": "/api/domain/wealthdomain/position/dynamic/query/getPositionsAsOfDateForCustodian", "base_url": "http://app-data-manager:8002", "method": "get", "query_params": {"_page": "0", "_size": "${10}", "currentUserId": "${userId}", "custodianAssignedID": "${custodianAssignedID}", "asOfDate": "${gvGlobalAsOfDate}"}, "path_params": {}, "requestBody": {}, "responseSchema": {"type": "array", "items": {"type": "object", "properties": {"account": {"type": "object", "description": "Details about the account.", "properties": {"accountNumber": {"type": "string", "description": "The unique identification number for the account."}, "accountStatus": {"type": "string", "description": "The current status of the account (e.g., 'Open', 'Closed')."}}}, "accountTypeCode": {"type": "string", "description": "The type code of the account (e.g., 'IRA', '401K')."}, "asOfDate": {"type": "string", "format": "date", "description": "The date as of which the holding information is valid, in YYYY-MM-DD format."}, "id": {"type": "string", "description": "A unique identifier for this specific holding entry."}, "marketPrice": {"type": "number", "format": "float", "description": "The current market price of a single unit of the security."}, "marketValue": {"type": "number", "format": "float", "description": "The total market value of the security holding."}, "quantity": {"type": "number", "format": "float", "description": "The quantity of the security held in the account."}, "security": {"type": "object", "description": "Details about the held security.", "properties": {"callIndicator": {"type": "string", "description": "Indicates if the security is a callable instrument (e.g., for bonds)."}, "custodianAssignedID": {"type": "string", "description": "An ID assigned to the security by the custodian."}, "optionExpiryDate": {"type": "string", "format": "date", "description": "The expiry date for options, if applicable."}, "securityDescription": {"type": "string", "description": "A detailed description of the security."}, "strikePrice": {"type": "number", "format": "float", "description": "The strike price for options, if applicable. Defaults to 0 for non-options."}, "symbol": {"type": "string", "description": "The ticker symbol of the security."}}}, "underlyingSymbol": {"type": "string", "description": "The symbol of the underlying asset for derivatives."}}}}, "resultVariable": "positionsDataTableForCustodian", "references": {"tables": [{"name": "PositionsByHoldingsBySymbol", "description": "Account Positions by Holdings by Symbol", "columns": [{"name": "Account Number", "type": "string", "fieldBinding": "${!isEmpty(positionsDataTableForCustodian[i].account.accountNumber)? positionsDataTableForCustodian[i].account.accountNumber : \" \"}"}, {"name": "Account Type", "type": "string", "fieldBinding": "${!isEmpty(positionsDataTableForCustodian[i].accountTypeCode)? positionsDataTableForCustodian[i].accountTypeCode : \" \"}"}, {"name": "Quantity", "type": "number", "fieldBinding": "${!isEmpty(positionsDataTableForCustodian[i].quantity)? positionsDataTableForCustodian[i].quantity : \" \"}"}, {"name": "Market Value", "type": "currency", "fieldBinding": "${!isEmpty(positionsDataTableForCustodian[i].marketValue)? positionsDataTableForCustodian[i].marketValue : \" \"}"}, {"name": "Market Price", "type": "currency", "fieldBinding": "${!isEmpty(positionsDataTableForCustodian[i].marketPrice)? positionsDataTableForCustodian[i].marketPrice : \" \"}"}, {"name": "Option Type", "type": "string", "fieldBinding": "${!isEmpty(positionsDataTableForCustodian[i].security.callIndicator)? positionsDataTableForCustodian[i].security.callIndicator : \" \"}"}, {"name": "Expiry Date", "type": "string", "fieldBinding": "${!isEmpty(positionsDataTableForCustodian[i].security.optionExpiryDate)? positionsDataTableForCustodian[i].security.optionExpiryDate : \" \"}"}, {"name": "Strike Price", "type": "currency", "fieldBinding": "${!isEmpty(positionsDataTableForCustodian[i].security.strikePrice)? positionsDataTableForCustodian[i].security.strikePrice : \" \"}"}, {"name": "Underlying Symbol", "type": "string", "fieldBinding": "${!isEmpty(positionsDataTableForCustodian[i].underlyingSymbol)? positionsDataTableForCustodian[i].underlyingSymbol : \" \"}"}, {"name": "Account Status", "type": "string", "fieldBinding": "${!isEmpty(positionsDataTableForCustodian[i].account.accountStatus)? positionsDataTableForCustodian[i].account.accountStatus : \" \"}"}]}]}, "id": "88f854f6-602b-4a9b-ba9b-76d36c036444"}], "fieldBindings": [{"name": "cusip", "description": "The CUSIP number for the security", "type": "string", "value": "${!isEmpty(symbolSearchResults[0].cusip)? symbolSearchResults[0].cusip : \" \"}"}, {"name": "custodianAssignedID", "description": "The custodian assigned ID for the security", "type": "string", "value": "${!isEmpty(symbolSearchResults[0].custodianAssignedID)? symbolSearchResults[0].custodianAssignedID : \" \"}"}, {"name": "id", "description": "The unique identifier for the security", "type": "string", "value": "${!isEmpty(symbolSearchResults[0].id)? symbolSearchResults[0].id : \" \"}"}, {"name": "securityDescription", "description": "The description of the security", "type": "string", "value": "${!isEmpty(symbolSearchResults[0].securityDescription)? symbolSearchResults[0].securityDescription : \" \"}"}, {"name": "symbol", "description": "The trading symbol for the security", "type": "string", "value": "${!isEmpty(symbolSearchResults[0].symbol)? symbolSearchResults[0].symbol : \" \"}"}], "tables": [{"name": "Positions", "description": "Account positions list sorted based on period end date", "columns": [{"name": "Ticker / Symbol", "type": "string", "fieldBinding": "${!isEmpty(positionsDataTable[i].security.symbol)? positionsDataTable[i].security.symbol : \" \"}"}, {"name": "CUSIP", "type": "string", "fieldBinding": "${!isEmpty(positionsDataTable[i].security.cusip)? positionsDataTable[i].security.cusip : \" \"}"}, {"name": "Sec ID", "type": "string", "fieldBinding": "${!isEmpty(positionsDataTable[i].security.custodianAssignedID)? positionsDataTable[i].security.custodianAssignedID : \" \"}"}, {"name": "Description", "type": "string", "fieldBinding": "${!isEmpty(positionsDataTable[i].security.securityDescription)? positionsDataTable[i].security.securityDescription : \" \"}"}, {"name": "Accounts", "type": "integer", "fieldBinding": "${!isEmpty(positionsDataTable[i].numberOfAccounts)? positionsDataTable[i].numberOfAccounts : \" \"}"}, {"name": "Quantity", "type": "integer", "fieldBinding": "${!isEmpty(positionsDataTable[i].numberOfShares)? positionsDataTable[i].numberOfShares : \" \"}"}, {"name": "Market Value", "type": "currency", "fieldBinding": "${!isEmpty(positionsDataTable[i].marketValue)? positionsDataTable[i].marketValue : \" \"}"}, {"name": "Price", "type": "currency", "fieldBinding": "${!isEmpty(positionsDataTable[i].security.lastPrice)? positionsDataTable[i].security.lastPrice : \" \"}"}, {"name": "Strike Price", "type": "currency", "fieldBinding": "${!isEmpty(positionsDataTable[i].security.strikePrice)? positionsDataTable[i].security.strikePrice : \" \"}"}, {"name": "Expiry Date", "type": "string", "fieldBinding": "${!isEmpty(positionsDataTable[i].security.optionExpiryDate)? positionsDataTable[i].security.optionExpiryDate : \" \"}"}, {"name": "Option Type", "type": "string", "fieldBinding": "${!isEmpty(positionsDataTable[i].security.callIndicator)? positionsDataTable[i].security.callIndicator : \" \"}"}, {"name": "ISIN", "type": "string", "fieldBinding": "${!isEmpty(positionsDataTable[i].security.isin)? positionsDataTable[i].security.isin : \" \"}"}, {"name": "SEDOL", "type": "string", "fieldBinding": "${!isEmpty(positionsDataTable[i].security.sedol)? positionsDataTable[i].security.sedol : \" \"}"}]}, {"name": "PositionsByHoldingsBySymbol", "description": "Account Positions by Holdings by Symbol", "columns": [{"name": "Account Number", "type": "string", "fieldBinding": "${!isEmpty(positionsDataTableForCustodian[i].account.accountNumber)? positionsDataTableForCustodian[i].account.accountNumber : \" \"}"}, {"name": "Account Type", "type": "string", "fieldBinding": "${!isEmpty(positionsDataTableForCustodian[i].accountTypeCode)? positionsDataTableForCustodian[i].accountTypeCode : \" \"}"}, {"name": "Quantity", "type": "number", "fieldBinding": "${!isEmpty(positionsDataTableForCustodian[i].quantity)? positionsDataTableForCustodian[i].quantity : \" \"}"}, {"name": "Market Value", "type": "currency", "fieldBinding": "${!isEmpty(positionsDataTableForCustodian[i].marketValue)? positionsDataTableForCustodian[i].marketValue : \" \"}"}, {"name": "Market Price", "type": "currency", "fieldBinding": "${!isEmpty(positionsDataTableForCustodian[i].marketPrice)? positionsDataTableForCustodian[i].marketPrice : \" \"}"}, {"name": "Option Type", "type": "string", "fieldBinding": "${!isEmpty(positionsDataTableForCustodian[i].security.callIndicator)? positionsDataTableForCustodian[i].security.callIndicator : \" \"}"}, {"name": "Expiry Date", "type": "string", "fieldBinding": "${!isEmpty(positionsDataTableForCustodian[i].security.optionExpiryDate)? positionsDataTableForCustodian[i].security.optionExpiryDate : \" \"}"}, {"name": "Strike Price", "type": "currency", "fieldBinding": "${!isEmpty(positionsDataTableForCustodian[i].security.strikePrice)? positionsDataTableForCustodian[i].security.strikePrice : \" \"}"}, {"name": "Underlying Symbol", "type": "string", "fieldBinding": "${!isEmpty(positionsDataTableForCustodian[i].underlyingSymbol)? positionsDataTableForCustodian[i].underlyingSymbol : \" \"}"}, {"name": "Account Status", "type": "string", "fieldBinding": "${!isEmpty(positionsDataTableForCustodian[i].account.accountStatus)? positionsDataTableForCustodian[i].account.accountStatus : \" \"}"}]}]}, {"id": "ActivityListingPage", "title": "Activity", "url": "/activity", "parameters": [], "description": "Activity list for the advisor", "endpoints": [{"description": "Get Account activity list for the advisor for the last 60 days", "url": "/api/domain/wealthdomain/j_Transaction/dynamic/query/accountActivityByAdvisor", "base_url": "http://app-data-manager:8002", "method": "get", "query_params": {"duration": "${Default is 60 days}", "_page": "0", "_size": "${10}", "_sort": "-transactionDate", "asOfDate": "${gvGlobalAsOfDate}", "excludeStatus": "Data%20Capture,Validation%20Failed,Validation%20Successful,Forms%20Generating,Form%20Generation%20Failed,Forms%20Generated,Options%20Pre-approval%20Pending,Options%20Approved,Initiating%20E-Signature,E-Signing,E-Signing%20Cancelled,E-Sign%20Expired,E-Signing%20Failed,E-Signed,Signing,Signed,Supervisor%20Approval%20Pending,Rep%20Action%20Required,Rejected,Approved,Account%20Setup%20In%20Progress,Account%20Setup%20Failure,Account%20Opened,Margin%20Review%20Required,Margin%20Disallowed,Margin%20Review%20Completed,Forms%20Uploading,Forms%20Failed%20to%20Load,Forms%20Successfully%20Uploaded,Wet%20Sign%20Cancelled,Ready%20for%20QC%20"}, "path_params": {}, "requestBody": {}, "responseSchema": {"type": "array", "items": {"type": "object", "properties": {"account": {"type": "object", "properties": {"subType": {"type": "string"}}}, "accountNumber": {"type": "string"}, "accountTypeCode": {"type": "string"}, "amount": {"type": "number"}, "commission": {"type": "number"}, "cusip": {"type": "string"}, "custodianAssignedSecurityID": {"type": "string"}, "id": {"type": "string", "format": "uuid"}, "isCancelled": {"type": "boolean"}, "isRebilled": {"type": "boolean"}, "longDescription": {"type": "string"}, "orderType": {"type": "string"}, "postDate": {"type": "string", "format": "date"}, "price": {"type": "number"}, "quantity": {"type": "number"}, "security": {"type": "object", "properties": {"securityDescription": {"type": "string"}, "isin": {"type": "string"}, "sedol": {"type": "string"}}}, "symbol": {"type": "string"}, "transactionDate": {"type": "string", "format": "date"}, "transactionRecordSource": {"type": "string"}, "transactionType": {"type": "string"}}}}, "resultVariable": "activitiesDataTable", "references": {"tables": [{"name": "Activity", "description": "Account Activity list", "columns": [{"name": "Date", "type": "string", "fieldBinding": "${!isEmpty(activitiesDataTable[i].transactionDate)? activitiesDataTable[i].transactionDate : \" \"}"}, {"name": "Account Number", "type": "string", "fieldBinding": "${!isEmpty(activitiesDataTable[i].accountNumber)? activitiesDataTable[i].accountNumber : \" \"}"}, {"name": "Transaction Description", "type": "string", "fieldBinding": "${!isEmpty(activitiesDataTable[i].longDescription)? activitiesDataTable[i].longDescription : \" \"}"}, {"name": "Transaction Code", "type": "string", "fieldBinding": "${!isEmpty(activitiesDataTable[i].transactionRecordSource)? activitiesDataTable[i].transactionRecordSource : \" \"}"}, {"name": "Transaction Type", "type": "string", "fieldBinding": "${!isEmpty(activitiesDataTable[i].transactionType)? activitiesDataTable[i].transactionType : \" \"}"}, {"name": "Quantity", "type": "integer", "fieldBinding": "${!isEmpty(activitiesDataTable[i].quantity)? activitiesDataTable[i].quantity : \" \"}"}, {"name": "Security Number", "type": "string", "fieldBinding": "${!isEmpty(activitiesDataTable[i].custodianAssignedSecurityID)? activitiesDataTable[i].custodianAssignedSecurityID : \" \"}"}, {"name": "Symbol", "type": "string", "fieldBinding": "${!isEmpty(activitiesDataTable[i].symbol)? activitiesDataTable[i].symbol : \" \"}"}, {"name": "CUSIP", "type": "string", "fieldBinding": "${!isEmpty(activitiesDataTable[i].cusip)? activitiesDataTable[i].cusip : \" \"}"}, {"name": "Security Description", "type": "string", "fieldBinding": "${!isEmpty(activitiesDataTable[i].security.securityDescription)? activitiesDataTable[i].security.securityDescription : \" \"}"}, {"name": "Action", "type": "string", "fieldBinding": "${!isEmpty(activitiesDataTable[i].orderType)? activitiesDataTable[i].orderType : \" \"}"}, {"name": "Order Price", "type": "currency", "fieldBinding": "${!isEmpty(activitiesDataTable[i].price)? activitiesDataTable[i].price : \" \"}"}, {"name": "Price", "type": "currency", "fieldBinding": "${!isEmpty(activitiesDataTable[i].price)? activitiesDataTable[i].price : \" \"}"}, {"name": "Amount", "type": "currency", "fieldBinding": "${!isEmpty(activitiesDataTable[i].amount)? activitiesDataTable[i].amount : \" \"}"}, {"name": "Commission", "type": "currency", "fieldBinding": "${!isEmpty(activitiesDataTable[i].commission)? activitiesDataTable[i].commission : \" \"}"}, {"name": "Account Type", "type": "string", "fieldBinding": "${!isEmpty(activitiesDataTable[i].accountTypeCode)? activitiesDataTable[i].accountTypeCode : \" \"}"}, {"name": "ISIN", "type": "string", "fieldBinding": "${!isEmpty(activitiesDataTable[i].security.isin)? activitiesDataTable[i].security.isin : \" \"}"}, {"name": "SEDOL", "type": "string", "fieldBinding": "${!isEmpty(activitiesDataTable[i].security.sedol)? activitiesDataTable[i].security.sedol : \" \"}"}, {"name": "AO Date", "type": "string", "fieldBinding": "${!isEmpty(activitiesDataTable[i].postDate)? activitiesDataTable[i].postDate : \" \"}"}, {"name": "Is Rebilled", "type": "boolean", "fieldBinding": "${!isEmpty(activitiesDataTable[i].isRebilled)? activitiesDataTable[i].isRebilled : \" \"}"}, {"name": "Is Cancelled", "type": "boolean", "fieldBinding": "${!isEmpty(activitiesDataTable[i].isCancelled)? activitiesDataTable[i].isCancelled : \" \"}"}]}]}, "id": "026950ca-6da7-4313-9b49-e8f99b949af9"}], "tables": [{"name": "Activity", "description": "Account Activity list", "columns": [{"name": "Date", "type": "string", "fieldBinding": "${!isEmpty(activitiesDataTable[i].transactionDate)? activitiesDataTable[i].transactionDate : \" \"}"}, {"name": "Account Number", "type": "string", "fieldBinding": "${!isEmpty(activitiesDataTable[i].accountNumber)? activitiesDataTable[i].accountNumber : \" \"}"}, {"name": "Transaction Description", "type": "string", "fieldBinding": "${!isEmpty(activitiesDataTable[i].longDescription)? activitiesDataTable[i].longDescription : \" \"}"}, {"name": "Transaction Code", "type": "string", "fieldBinding": "${!isEmpty(activitiesDataTable[i].transactionRecordSource)? activitiesDataTable[i].transactionRecordSource : \" \"}"}, {"name": "Transaction Type", "type": "string", "fieldBinding": "${!isEmpty(activitiesDataTable[i].transactionType)? activitiesDataTable[i].transactionType : \" \"}"}, {"name": "Quantity", "type": "integer", "fieldBinding": "${!isEmpty(activitiesDataTable[i].quantity)? activitiesDataTable[i].quantity : \" \"}"}, {"name": "Security Number", "type": "string", "fieldBinding": "${!isEmpty(activitiesDataTable[i].custodianAssignedSecurityID)? activitiesDataTable[i].custodianAssignedSecurityID : \" \"}"}, {"name": "Symbol", "type": "string", "fieldBinding": "${!isEmpty(activitiesDataTable[i].symbol)? activitiesDataTable[i].symbol : \" \"}"}, {"name": "CUSIP", "type": "string", "fieldBinding": "${!isEmpty(activitiesDataTable[i].cusip)? activitiesDataTable[i].cusip : \" \"}"}, {"name": "Security Description", "type": "string", "fieldBinding": "${!isEmpty(activitiesDataTable[i].security.securityDescription)? activitiesDataTable[i].security.securityDescription : \" \"}"}, {"name": "Action", "type": "string", "fieldBinding": "${!isEmpty(activitiesDataTable[i].orderType)? activitiesDataTable[i].orderType : \" \"}"}, {"name": "Order Price", "type": "currency", "fieldBinding": "${!isEmpty(activitiesDataTable[i].price)? activitiesDataTable[i].price : \" \"}"}, {"name": "Price", "type": "currency", "fieldBinding": "${!isEmpty(activitiesDataTable[i].price)? activitiesDataTable[i].price : \" \"}"}, {"name": "Amount", "type": "currency", "fieldBinding": "${!isEmpty(activitiesDataTable[i].amount)? activitiesDataTable[i].amount : \" \"}"}, {"name": "Commission", "type": "currency", "fieldBinding": "${!isEmpty(activitiesDataTable[i].commission)? activitiesDataTable[i].commission : \" \"}"}, {"name": "Account Type", "type": "string", "fieldBinding": "${!isEmpty(activitiesDataTable[i].accountTypeCode)? activitiesDataTable[i].accountTypeCode : \" \"}"}, {"name": "ISIN", "type": "string", "fieldBinding": "${!isEmpty(activitiesDataTable[i].security.isin)? activitiesDataTable[i].security.isin : \" \"}"}, {"name": "SEDOL", "type": "string", "fieldBinding": "${!isEmpty(activitiesDataTable[i].security.sedol)? activitiesDataTable[i].security.sedol : \" \"}"}, {"name": "AO Date", "type": "string", "fieldBinding": "${!isEmpty(activitiesDataTable[i].postDate)? activitiesDataTable[i].postDate : \" \"}"}, {"name": "Is Rebilled", "type": "boolean", "fieldBinding": "${!isEmpty(activitiesDataTable[i].isRebilled)? activitiesDataTable[i].isRebilled : \" \"}"}, {"name": "Is Cancelled", "type": "boolean", "fieldBinding": "${!isEmpty(activitiesDataTable[i].isCancelled)? activitiesDataTable[i].isCancelled : \" \"}"}]}]}, {"id": "clients", "title": "Clients", "url": "/clients", "parameters": [], "description": "Page for client account details", "endpoints": [{"description": "Get Client Accounts. This api is used to get the top client accounts as table. Note: This is a paginated api and will not return data for all the client accounts.", "url": "/api/domain/wealthdomain/client", "base_url": "http://app-data-manager:8002", "title": "Clients List", "method": "get", "query_params": {"_page": "0", "_size": "${10}", "fields": "cashBalance,id,clientName,repCode,marketValue,primaryMember.id,primaryMember.fullName,primaryMember.lastName,primaryMember.firstName,additionalMembers.id,additionalMembers.firstName,additionalMembers.lastName,additionalMembers.fullName,additionalMembers.relatedPersons.id,additionalMembers.relatedPersons.relationship,additionalMembers.relatedPersons.sourcePerson.id,additionalMembers.relatedPersons.sourcePerson.fullName,additionalMembers.relatedPersons.targetPerson.id"}, "path_params": {}, "headers": {"x-jiffy-include-total-count": "true"}, "requestBody": {}, "responseSchema": {"type": "array", "items": {"cashBalance": "currency", "id": "string", "clientName": "string", "repCode": "string", "marketValue": "currency", "primaryMember": {"type": "object", "properties": {"id": "string", "fullName": "string", "lastName": "string", "firstName": "string"}}, "additionalMembers": {"type": "array", "items": {"id": "string", "firstName": "string", "lastName": "string", "fullName": "string", "relatedPersons": {"type": "array", "items": {"id": "string", "relationship": "string", "sourcePerson": {"id": "string", "fullName": "string"}, "targetPerson": {"id": "string", "fullName": "string"}}}}}}}, "resultVariable": "clientDatatable1", "references": {"tables": [{"name": "Clients List", "description": "Clients list", "columns": [{"name": "Client Name", "type": "string", "fieldBinding": "${!isEmpty(clientDatatable1[i].clientName)? clientDatatable1[i].clientName : \" \"}"}, {"name": "Total Accounts", "type": "string", "fieldBinding": "${!isEmpty(clientDatatable1[i].repCode)? clientDatatable1[i].repCode : \" \"}"}, {"name": "Market Value", "type": "currency", "fieldBinding": "${!isEmpty(clientDatatable1[i].marketValue)? clientDatatable1[i].marketValue : \" \"}"}, {"name": "Cash Balance", "type": "currency", "fieldBinding": "${!isEmpty(clientDatatable1[i].cashBalance)? clientDatatable1[i].cashBalance : \" \"}"}]}]}, "id": "11f9061a-9ba3-4f72-86d9-f14429a5f77d"}, {"description": "Get advisor account balances. This api is used to get the client account summary which includes Total Accounts, Total Value, Cash Value for the client accounts", "url": "/api/domain/wealthdomain/advisorAccountBalances/dynamic/query/byAdvisorIdPeriodTypePeriodEndDate", "base_url": "http://app-data-manager:8002", "method": "get", "query_params": {"periodType": "${period type. Default is MTD}", "currentUserId": "${userId}", "periodEndDate": "${gvGlobalAsOfDate}"}, "path_params": {}, "requestBody": {}, "responseSchema": {"type": "array", "properties": {"advisorAccountBalances": {"type": "object", "properties": {"endingAccounts": "number", "endingBalance": "currency", "endingCashBalance": "currency"}}}}, "resultVariable": "outputAdvisorAB2", "references": {"fieldBindings": [{"name": "advisorAccountBalances", "description": "The advisor account balances for the client. This is used to get the total number of accounts, total value, and cash value.", "type": "object", "value": "${!isEmpty(outputAdvisorAB2.advisorAccountBalances[0])? outputAdvisorAB2.advisorAccountBalances[0] : {}}"}, {"name": "Total Accounts", "description": "The total number of client accounts for the advisor.", "type": "number", "value": "${!isEmpty(advisorAccountBalances.endingAccounts)? advisorAccountBalances.endingAccounts : \" \"}"}, {"name": "Total Value", "description": "The total value of client accounts for the advisor.", "type": "currency", "value": "${!isEmpty(advisorAccountBalances.endingBalance)? advisorAccountBalances.endingBalance : \" \"}"}, {"name": "Cash Value", "description": "The cash value of client accounts for the advisor.", "type": "currency", "value": "${!isEmpty(advisorAccountBalances.endingCashBalance)? advisorAccountBalances.endingCashBalance : \" \"}"}, {"name": "Data As Of", "description": "The data as of date.", "type": "string", "value": "${!isEmpty(gvGlobalAsOfDate)? gvGlobalAsOfDate : \" \"}"}]}, "id": "964daef9-c0a4-45bc-a9dc-f0adba3c41c8"}], "tables": [{"name": "Clients List", "description": "Clients list", "columns": [{"name": "Client Name", "type": "string", "fieldBinding": "${!isEmpty(clientDatatable1[i].clientName)? clientDatatable1[i].clientName : \" \"}"}, {"name": "Total Accounts", "type": "string", "fieldBinding": "${!isEmpty(clientDatatable1[i].repCode)? clientDatatable1[i].repCode : \" \"}"}, {"name": "Market Value", "type": "currency", "fieldBinding": "${!isEmpty(clientDatatable1[i].marketValue)? clientDatatable1[i].marketValue : \" \"}"}, {"name": "Cash Balance", "type": "currency", "fieldBinding": "${!isEmpty(clientDatatable1[i].cashBalance)? clientDatatable1[i].cashBalance : \" \"}"}]}], "fieldBindings": [{"name": "advisorAccountBalances", "description": "The advisor account balances for the client. This is used to get the total number of accounts, total value, and cash value.", "type": "object", "value": "${!isEmpty(outputAdvisorAB2.advisorAccountBalances[0])? outputAdvisorAB2.advisorAccountBalances[0] : {}}"}, {"name": "Total Accounts", "description": "The total number of client accounts for the advisor.", "type": "number", "value": "${!isEmpty(advisorAccountBalances.endingAccounts)? advisorAccountBalances.endingAccounts : \" \"}"}, {"name": "Total Value", "description": "The total value of client accounts for the advisor.", "type": "currency", "value": "${!isEmpty(advisorAccountBalances.endingBalance)? advisorAccountBalances.endingBalance : \" \"}"}, {"name": "Cash Value", "description": "The cash value of client accounts for the advisor.", "type": "currency", "value": "${!isEmpty(advisorAccountBalances.endingCashBalance)? advisorAccountBalances.endingCashBalance : \" \"}"}, {"name": "Data As Of", "description": "The data as of date.", "type": "string", "value": "${!isEmpty(gvGlobalAsOfDate)? gvGlobalAsOfDate : \" \"}"}]}, {"id": "singleAccountSearchPage", "title": "Single Account - Search", "url": "/single_account_search", "parameters": [{"name": "actnum", "value": "${Account Number}"}], "description": "Single account search page", "endpoints": [{"description": "Get account id for a given account number or account name. This is a prerequisite for all other single account related queries.", "url": "/api/domain/wealthdomain/account/dynamic/query/searchAccounts", "base_url": "http://app-data-manager:8002", "method": "get", "query_params": {"_size": "${Size of the search results list to be returned. Default is 10}", "searchString": "${Account id or number for which matches are to be found}"}, "path_params": {}, "requestBody": {}, "responseSchema": {"type": "array", "items": {"accountNumber": "string", "id": "string", "name": "string", "score": "number"}}, "resultVariable": "accountList", "id": "c5b495e9-6a2d-4165-9804-3c82ae97160b"}]}, {"id": "SingleAccountOverview", "title": "Single Account Overview", "url": "/single_account_overview", "parameters": [{"name": "actnum", "value": "${Account Number}"}], "description": "Landing page for a single account overview, showing account summary, balance chart, month-end balances, quick links, and asset allocation.", "endpoints": [{"description": "Get account summary for a given account. Prerequisite : Get account id first from the endpoint - Get account id and details for a given account number or account name", "url": "/api/domain/wealthdomain/account/{accountId}", "base_url": "http://app-data-manager:8002", "query_params": {"_fields": "name, dailyBalances.endingCashBalance, accountStatus, dailyBalances.endingBalance, registrationType.name"}, "method": "get", "path_params": {"accountId": "${accountId}"}, "requestBody": {}, "responseSchema": {"type": "object", "properties": {"dailyBalances": {"type": "object", "properties": {"endingCashBalance": {"type": "number"}, "endingMoneyMarketBalance": {"type": "number"}, "tradeDateBalance": {"type": "number"}, "settlementDateBalance": {"type": "number"}, "dayTradeBuyingPower": {"type": "number"}}}}}, "resultVariable": "accountDetail", "references": {"fieldBindings": [{"name": "Account Name", "description": "The name of the account.", "type": "string", "value": "${!isEmpty(accountDetail.name)? accountDetail.name : \" \"}", "section": "/Single Account - Overview/Account Summary"}, {"name": "Cash Value", "description": "The cash value in the account.", "type": "number", "value": "${!isEmpty(accountDetail.dailyBalances.endingCashBalance)? accountDetail.dailyBalances.endingCashBalance : \" \"}", "section": "/Single Account - Overview/Account Summary"}, {"name": "Status", "description": "The status of the account.", "type": "string", "value": "${!isEmpty(accountDetail.accountStatus)? accountDetail.accountStatus : \" \"}", "section": "/Single Account - Overview/Account Summary"}, {"name": "Account <PERSON><PERSON>", "description": "The ending balance of the account.", "type": "currency", "value": "${!isEmpty(accountDetail.dailyBalances.endingBalance)? accountDetail.dailyBalances.endingBalance : \" \"}", "section": "/Single Account - Overview/Account Summary"}, {"name": "Registration Type", "description": "The registration type of the account.", "type": "string", "value": "${!isEmpty(accountDetail.registrationType.name)? accountDetail.registrationType.name : \" \"}", "section": "/Single Account - Overview/Account Summary"}, {"name": "Data As Of", "description": "The data as of date.", "type": "string", "value": "${!isEmpty(gvGlobalAsOfDate)? gvGlobalAsOfDate : \" \"}", "section": "/Single Account - Overview/Account Summary"}]}, "id": "f37b089e-e421-45d8-9af5-13c78f8af4be"}, {"description": "For a single account, Get Monthly Account Balance Or balance Over Time/Period for 3 months, 6 months etc. Default duration is 6 months, but based on the user input decided the duration. Mostly used for a bar chart. Prerequisite : Get account id first from the endpoint - Get account id and details for a given account number or account name", "url": "/api/domain/wealthdomain/accountBalances/dynamic/query/monthlyAccountBalancesForAccount", "base_url": "http://app-data-manager:8002", "method": "get", "query_params": {"accountId": "${accountId}", "periodEndDate": "${gvGlobalAsOfDate}", "startDate": "${subMonths(gvCurrentDate, 6)}", "endDate": "${gvCurrentDate}"}, "path_params": {}, "requestBody": {}, "responseSchema": {"type": "array", "items": {"type": "object", "properties": {"beginningBalance": {"type": "number"}, "contributions": {"type": "number"}, "endingBalance": {"type": "number"}, "endingCashBalance": {"type": "number"}, "id": {"type": "string"}, "periodEndDate": {"type": "string"}, "periodType": {"type": "string"}, "withdrawals": {"type": "number"}}}}, "resultVariable": "assetsOverTimeForASingleAccount", "references": {"charts": [{"name": "Balance", "description": "Single account balance over a time/period like 3 months, 6 months etc for a single account", "type": "bar", "schema": {"type": "array", "items": {"type": "object", "properties": {"beginningBalance": {"type": "number"}, "contributions": {"type": "number"}, "endingBalance": {"type": "number"}, "endingCashBalance": {"type": "number"}, "id": {"type": "string"}, "periodEndDate": {"type": "string"}, "periodType": {"type": "string"}, "withdrawals": {"type": "number"}}}}}]}, "id": "e8bf4464-0d04-4a8c-a301-172875d273ad"}, {"description": "For a single account, Get asset allocation pie chart. Prerequisite : Get account id first from the endpoint - Get account id and details for a given account number or account name", "url": "/api/domain/wealthdomain/position/dynamic/query/assetAllocationByAccount", "base_url": "http://app-data-manager:8002", "method": "get", "query_params": {"accountId": "${accountId}", "asOfDate": "${gvGlobalAsOfDate}"}, "path_params": {}, "requestBody": {}, "responseSchema": {"type": "array", "items": {"securityCategory": "string", "totalMarketValue": "number"}}, "resultVariable": "assetAllocationForASingleAccount", "references": {"charts": [{"name": "Allocation of Assets", "description": "Single account portfolio allocation or Asset allocation by asset class", "type": "pie", "schema": {"type": "array", "items": {"securityCategory": "string", "totalMarketValue": "number"}}}]}, "id": "b746ffc4-0635-445f-9f20-ebf2c9a0725e"}, {"description": "For a single account, Get Month End Balances. It gives the recent month end balances for the account. Prerequisite : Get account id first from the endpoint - Get account id and details for a given account number or account name", "url": "/api/domain/wealthdomain/accountBalances/dynamic/query/monthEndAccountBalancesForAccount", "base_url": "http://app-data-manager:8002", "method": "get", "query_params": {"_page": "0", "_size": "${10}", "accountId": "${accountId}", "periodEndDate": "${gvGlobalAsOfDate}"}, "path_params": {}, "requestBody": {}, "responseSchema": {"type": "array", "items": {"type": "object", "properties": {"beginningBalance": {"type": "number"}, "contributions": {"type": "number"}, "endingBalance": {"type": "number"}, "endingCashBalance": {"type": "number"}, "id": {"type": "string"}, "periodEndDate": {"type": "string"}, "periodType": {"type": "string"}, "withdrawals": {"type": "number"}}}}, "resultVariable": "monthEndBalancesDataTable", "references": {"tables": [{"name": "Month End Balances", "description": "Single account month end balances", "type": "table", "columns": [{"name": "Period End Date", "type": "string", "fieldBinding": "${!isEmpty(monthEndBalancesDataTable[i].periodEndDate)? monthEndBalancesDataTable[i].periodEndDate : \" \"}"}, {"name": "ME Total", "type": "currency", "fieldBinding": "${!isEmpty(monthEndBalancesDataTable[i].endingBalance)? monthEndBalancesDataTable[i].endingBalance : \" \"}"}, {"name": "Cash In", "type": "currency", "fieldBinding": "${!isEmpty(monthEndBalancesDataTable[i].contributions)? monthEndBalancesDataTable[i].contributions : \" \"}"}, {"name": "Cash Out", "type": "currency", "fieldBinding": "${!isEmpty(monthEndBalancesDataTable[i].withdrawals)? monthEndBalancesDataTable[i].withdrawals : \" \"}"}, {"name": "Total Cash", "type": "currency", "fieldBinding": "${!isEmpty(monthEndBalancesDataTable[i].endingCashBalance)? monthEndBalancesDataTable[i].endingCashBalance : \" \"}"}]}]}, "id": "cdb15537-be6e-439b-8a9d-11ad54580faa"}], "charts": [{"name": "Allocation of Assets", "description": "Single account portfolio allocation or Asset allocation by asset class", "type": "pie", "schema": {"type": "array", "items": {"securityCategory": "string", "totalMarketValue": "number"}}}, {"name": "Balance", "description": "Single account balance over a time/period like 3 months, 6 months etc for a single account", "type": "bar", "schema": {"type": "array", "items": {"type": "object", "properties": {"beginningBalance": {"type": "number"}, "contributions": {"type": "number"}, "endingBalance": {"type": "number"}, "endingCashBalance": {"type": "number"}, "id": {"type": "string"}, "periodEndDate": {"type": "string"}, "periodType": {"type": "string"}, "withdrawals": {"type": "number"}}}}}], "tables": [{"name": "Month End Balances", "description": "Single account month end balances", "type": "table", "columns": [{"name": "Period End Date", "type": "string", "fieldBinding": "${!isEmpty(monthEndBalancesDataTable[i].periodEndDate)? monthEndBalancesDataTable[i].periodEndDate : \" \"}"}, {"name": "ME Total", "type": "currency", "fieldBinding": "${!isEmpty(monthEndBalancesDataTable[i].endingBalance)? monthEndBalancesDataTable[i].endingBalance : \" \"}"}, {"name": "Cash In", "type": "currency", "fieldBinding": "${!isEmpty(monthEndBalancesDataTable[i].contributions)? monthEndBalancesDataTable[i].contributions : \" \"}"}, {"name": "Cash Out", "type": "currency", "fieldBinding": "${!isEmpty(monthEndBalancesDataTable[i].withdrawals)? monthEndBalancesDataTable[i].withdrawals : \" \"}"}, {"name": "Total Cash", "type": "currency", "fieldBinding": "${!isEmpty(monthEndBalancesDataTable[i].endingCashBalance)? monthEndBalancesDataTable[i].endingCashBalance : \" \"}"}]}], "fieldBindings": [{"name": "Cash Value", "description": "The cash value in the account.", "type": "number", "value": "${!isEmpty(accountDetail.dailyBalances.endingCashBalance)? accountDetail.dailyBalances.endingCashBalance : \" \"}", "section": "/Single Account - Overview/Account Summary"}, {"name": "Status", "description": "The status of the account.", "type": "string", "value": "${!isEmpty(accountDetail.accountStatus)? accountDetail.accountStatus : \" \"}", "section": "/Single Account - Overview/Account Summary"}, {"name": "Account <PERSON><PERSON>", "description": "The ending balance of the account.", "type": "currency", "value": "${!isEmpty(accountDetail.dailyBalances.endingBalance)? accountDetail.dailyBalances.endingBalance : \" \"}", "section": "/Single Account - Overview/Account Summary"}, {"name": "Registration Type", "description": "The registration type of the account.", "type": "string", "value": "${!isEmpty(accountDetail.registrationType.name)? accountDetail.registrationType.name : \" \"}", "section": "/Single Account - Overview/Account Summary"}, {"name": "Data As Of", "description": "The data as of date.", "type": "string", "value": "${!isEmpty(gvGlobalAsOfDate)? gvGlobalAsOfDate : \" \"}", "section": "/Single Account - Overview/Account Summary"}]}, {"id": "SingleAccountProfilePage", "title": "Single Account Profile", "url": "/single_account_details", "parameters": [{"name": "actnum", "value": "${Account Number}"}], "description": "Single account profile page containing sections - Basic Information, Account Funding, Account Features, Agreements(Account Agreement, Margin Agreement, Options Agreement)", "endpoints": [{"description": "Get account profile for a given account ID contains Basic Information, Account Funding, Account Features, Agreements(Account Agreement, Margin Agreement, Options Agreement). Prerequisite : Get account id first from the endpoint - Get account id and details for a given account number or account name", "url": "/workflow/v1/instances/execute/sync/domain/wealthdomain/d1i09s54q0djsb5r2am0", "base_url": "http://workhorse:8080", "method": "post", "query_params": {}, "path_params": {}, "requestBody": {"arguments": {"accountId": "${accountId}", "shouldSaveToBO": false, "shouldReturnFromBO": false}}, "responseSchema": {"type": "object", "properties": {"ouLevel1": {"type": "object", "properties": {"bulkLoadRunIdJfyApx": {"type": "string"}, "omsId": {"type": "string"}, "alternateEmail": {"type": "string", "format": "email"}, "code": {"type": "string"}, "secondaryPhoneNumber": {"type": "string"}, "primaryPhoneNumber": {"type": "string"}, "description": {"type": "string"}, "bulkLoadRecIdJfyApx": {"type": "string"}, "type": {"type": "string"}, "parentCode": {"type": "string"}, "taxID": {"type": "string"}, "name": {"type": "string"}, "id": {"type": "string", "format": "uuid"}, "primaryEmail": {"type": "string", "format": "email"}, "billingCode": {"type": "string"}}}, "siMPLEIRAAdditionalSalaryReductionElecti": {"type": "string"}, "specialExpensesTimeframe": {"type": "string"}, "nickName": {"type": "string"}, "siMPLEIRASalaryReductionAgreementReceM7iqk": {"type": "string"}, "irAExistingAccountFees": {"type": "string"}, "accountManagementType": {"type": "string"}, "sePIRAIncludeEmployeesUnder450": {"type": "boolean"}, "siMPLEIRACurrentMinimumCompensation": {"type": "string"}, "siMPLEIRAPriorMinimumCompensation": {"type": "string"}, "registrationType": {"type": "object", "properties": {"bulkLoadRunIdJfyApx": {"type": "string"}, "image": {"type": "string"}, "instructions": {"type": "string"}, "code": {"type": "string"}, "isRetirement": {"type": "boolean"}, "sortOrder": {"type": "integer"}, "isCoveredByERISA": {"type": "boolean"}, "description": {"type": "string"}, "bulkLoadRecIdJfyApx": {"type": "string"}, "enabled": {"type": "boolean"}, "baseAccountType": {"type": "string"}, "applicableTo": {"type": "string"}, "custodianCode": {"type": "string"}, "name": {"type": "string"}, "id": {"type": "string", "format": "uuid"}, "category": {"type": "string"}}}, "irAEligibleDesignatedBeneficiaryReason": {"type": "string"}, "siMPLEIRASalaryReductionAmountChoice": {"type": "string"}, "irABeneficiaryType": {"type": "string"}, "name": {"type": "string"}, "subType": {"type": "string"}, "irADistributionInstructions": {"type": "string"}, "annualIncomeExact": {"type": "number"}, "secondaryOwners": {"type": "array", "items": {"type": "object"}}, "otherInitialFundingSource": {"type": "string"}, "irAOriginalAccountNumber": {"type": "string"}, "siMPLEIRAEmployeeMayTerminateSalaryReduc": {"type": "boolean"}, "investmentExperience": {"type": "string"}, "siMPLEIRAExcludeEmployeesCoveredByCollec": {"type": "boolean"}, "irARecommendedAccountFees": {"type": "string"}, "inheritedIRADistributionOption": {"type": "string"}, "annualIncome": {"type": "string"}, "cashDividendOption": {"type": "string"}, "accountStatus": {"type": "string"}, "federalMarginalTaxRate": {"type": "string"}, "wantBeneficiaries": {"type": "boolean"}, "shareOwnerInformationWithOwnedCorporatio": {"type": "boolean"}, "riskTolerance": {"type": "string"}, "siMPLEIRASalaryReductionAgreementReceive": {"type": "boolean"}, "sePIRAIncludeCertainNonResidentAliens": {"type": "boolean"}, "interestedParties": {"type": "array", "items": {"type": "object"}}, "transfers": {"type": "array", "items": {"type": "object"}}, "id": {"type": "string", "format": "uuid"}, "siMPLEIRAGeneralEligibilityRequirement": {"type": "string"}, "beneficiaries": {"type": "array", "items": {"type": "object", "properties": {"relationship": {"type": "string"}, "percentage": {"type": "number"}, "beneficiary": {"type": "object", "properties": {"firstName": {"type": "string"}, "lastName": {"type": "string"}, "middleName": {"type": "string"}, "taxIDType": {"type": "string"}, "dateOfBirth": {"type": "string", "format": "date"}, "ssNOrTaxID": {"type": "string"}, "legalAddress": {"type": "object", "properties": {"country": {"type": "object", "properties": {"bulkLoadRunIdJfyApx": {"type": "string"}, "code3Letters": {"type": "string"}, "code2Letters": {"type": "string"}, "sortOrder": {"type": "integer"}, "fullName": {"type": "string"}, "id": {"type": "string", "format": "uuid"}, "shortName": {"type": "string"}, "bulkLoadRecIdJfyApx": {"type": "string"}}}, "city": {"type": "string"}, "postalCode": {"type": "string"}, "state": {"type": "object", "properties": {"bulkLoadRunIdJfyApx": {"type": "string"}, "code": {"type": "string"}, "name": {"type": "string"}, "id": {"type": "string", "format": "uuid"}, "bulkLoadRecIdJfyApx": {"type": "string"}}}, "line2": {"type": "string"}, "line1": {"type": "string"}}}}}, "beneficiaryType": {"type": "string"}, "isContingentBeneficiary": {"type": "boolean"}, "perStirpes": {"type": "boolean"}, "rmDOption": {"type": "string"}}}}, "accountCustodianStatus": {"type": "string"}, "marginRates": {"type": "array", "items": {"type": "object"}}, "repCodeLink": {"type": "object", "properties": {"bulkLoadRunIdJfyApx": {"type": "string"}, "lastModifiedAt": {"type": "string", "format": "date-time"}, "repCode": {"type": "string"}, "lastModifiedBy": {"type": "string", "format": "uuid"}, "isPersonal": {"type": "boolean"}, "repName": {"type": "string"}, "source": {"type": "string"}, "secondaryEmail": {"type": "string", "format": "email"}, "description": {"type": "string"}, "repNameAndCode": {"type": "string"}, "bulkLoadRecIdJfyApx": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "parentCode": {"type": "string"}, "primaryPhone": {"type": "string"}, "businessPhone": {"type": "string"}, "name": {"type": "string"}, "id": {"type": "string", "format": "uuid"}, "firmName": {"type": "string"}, "primaryEmail": {"type": "string", "format": "email"}, "secondaryPhone": {"type": "string"}, "isSplit": {"type": "boolean"}}}, "startDate": {"type": "string", "format": "date"}, "isInstitutionalAccount": {"type": "boolean"}, "primaryOwner": {"type": "object", "properties": {"isAddressSameAsPrimaryClient": {"type": "boolean"}, "owner": {"type": "object", "properties": {"investmentExperienceOptions": {"type": "string"}, "legalAddressLengthOfStay": {"type": "string"}, "investExperienceMargin": {"type": "string"}, "investmentExperienceFutures": {"type": "string"}, "investExperienceMarginTransactions": {"type": "integer"}, "fullName": {"type": "string"}, "ssNOrTaxID": {"type": "string"}, "firstName": {"type": "string"}, "citizenshipStatus": {"type": "string"}, "investmentExperienceOptionsTransactions": {"type": "string"}, "mailingAddress": {"type": "object", "properties": {"country": {"type": "object", "properties": {"bulkLoadRunIdJfyApx": {"type": "string"}, "code3Letters": {"type": "string"}, "code2Letters": {"type": "string"}, "sortOrder": {"type": "integer"}, "fullName": {"type": "string"}, "id": {"type": "string", "format": "uuid"}, "shortName": {"type": "string"}, "bulkLoadRecIdJfyApx": {"type": "string"}}}, "city": {"type": "string"}, "postalCode": {"type": "string"}, "state": {"type": "object", "properties": {"bulkLoadRunIdJfyApx": {"type": "string"}, "code": {"type": "string"}, "name": {"type": "string"}, "id": {"type": "string", "format": "uuid"}, "bulkLoadRecIdJfyApx": {"type": "string"}}}, "line2": {"type": "string"}, "line1": {"type": "string"}}}, "backupWithholdingExemptPayeeCode": {"type": "string"}, "securitiesIndustryAffiliation": {"type": "object", "properties": {"typeOfEmployer": {"type": "string"}, "firmNameForEmployee": {"type": "string"}}}, "yeMployed": {"type": "string"}, "associatedWithInvestmentAdvisor": {"type": "boolean"}, "investmentExperienceAnnuities": {"type": "string"}, "maritalStatus": {"type": "string"}, "investExperienceFuturesTransactions": {"type": "integer"}, "relatedToPublicCompanyOfficial": {"type": "boolean"}, "occupation": {"type": "string"}, "investmentExperienceAnnuitiesTransactions": {"type": "integer"}, "natureOfBusiness": {"type": "string"}, "investmentExperienceEquitiesTransactions": {"type": "string"}, "countryOfResidence": {"type": "object", "properties": {"bulkLoadRunIdJfyApx": {"type": "string"}, "code3Letters": {"type": "string"}, "code2Letters": {"type": "string"}, "sortOrder": {"type": "integer"}, "fullName": {"type": "string"}, "id": {"type": "string", "format": "uuid"}, "shortName": {"type": "string"}, "bulkLoadRecIdJfyApx": {"type": "string"}}}, "suffix": {"type": "string"}, "investmentExperienceAlternativesTransact": {"type": "string"}, "spouse": {"type": "object"}, "investExperienceAlternatives": {"type": "string"}, "associatedWithOtherBrokerDealer": {"type": "boolean"}, "id": {"type": "string", "format": "uuid"}, "regulatoryDisclosuresV0": {"type": "object"}, "legalAddressIsMailingAddress": {"type": "boolean"}, "employmentDate": {"type": "string", "format": "date"}, "investmentExperienceMutualFunds": {"type": "string"}, "homeOwnership": {"type": "string"}, "taxIDType": {"type": "string"}, "previousLegalAddress": {"type": "object", "properties": {"country": {"type": "object", "properties": {"bulkLoadRunIdJfyApx": {"type": "string"}, "code3Letters": {"type": "string"}, "code2Letters": {"type": "string"}, "sortOrder": {"type": "integer"}, "fullName": {"type": "string"}, "id": {"type": "string", "format": "uuid"}, "shortName": {"type": "string"}, "bulkLoadRecIdJfyApx": {"type": "string"}}}, "city": {"type": "string"}, "postalCode": {"type": "string"}, "state": {"type": "object", "properties": {"bulkLoadRunIdJfyApx": {"type": "string"}, "code": {"type": "string"}, "name": {"type": "string"}, "id": {"type": "string", "format": "uuid"}, "bulkLoadRecIdJfyApx": {"type": "string"}}}, "line2": {"type": "string"}, "line1": {"type": "string"}}}, "dateOfBirth": {"type": "string", "format": "date"}, "primaryPhoneNumber": {"type": "string"}, "proofOfIdentity": {"type": "object", "properties": {"expiryDate": {"type": "string", "format": "date"}, "issuingCountry": {"type": "string"}, "idNumber": {"type": "string"}, "issueDate": {"type": "string", "format": "date"}, "type": {"type": "string"}}}, "investmentExperienceFixedIncomeTransacti": {"type": "string"}, "investmentExperienceMutualFundsTransacti": {"type": "string"}, "associatedWithBrokerDealer": {"type": "boolean"}, "investmentExperienceAnnuitiesTransaction": {"type": "integer"}, "yearsEmployed": {"type": "integer"}, "investmentExperienceMutualFundsTransactions": {"type": "integer"}, "businessPhoneNumber": {"type": "string"}, "publicCompanyOfficial": {"type": "object", "properties": {"firmNameForOfficer": {"type": "string"}, "relationshipOfOfficer": {"type": "string"}, "firmTickerForOfficer": {"type": "string"}}}, "lastName": {"type": "string"}, "employerAddress": {"type": "object", "properties": {"country": {"type": "object", "properties": {"bulkLoadRunIdJfyApx": {"type": "string"}, "code3Letters": {"type": "string"}, "code2Letters": {"type": "string"}, "sortOrder": {"type": "integer"}, "fullName": {"type": "string"}, "id": {"type": "string", "format": "uuid"}, "shortName": {"type": "string"}, "bulkLoadRecIdJfyApx": {"type": "string"}}}, "city": {"type": "string"}, "postalCode": {"type": "string"}, "state": {"type": "object", "properties": {"bulkLoadRunIdJfyApx": {"type": "string"}, "code": {"type": "string"}, "name": {"type": "string"}, "id": {"type": "string", "format": "uuid"}, "bulkLoadRecIdJfyApx": {"type": "string"}}}, "line2": {"type": "string"}, "line1": {"type": "string"}}}, "numberOfDependents": {"type": "integer"}, "gender": {"type": "string"}, "investmentExperienceAlternatives": {"type": "string"}, "middleName": {"type": "string"}, "ein": {"type": "string"}, "faTCAReportingExemptionCode": {"type": "string"}, "accreditedInvestor": {"type": "boolean"}, "secondaryPhoneNumber": {"type": "string"}, "employmentStatus": {"type": "string"}, "legalAddress": {"type": "object", "properties": {"country": {"type": "object", "properties": {"bulkLoadRunIdJfyApx": {"type": "string"}, "code3Letters": {"type": "string"}, "code2Letters": {"type": "string"}, "sortOrder": {"type": "integer"}, "fullName": {"type": "string"}, "id": {"type": "string", "format": "uuid"}, "shortName": {"type": "string"}, "bulkLoadRecIdJfyApx": {"type": "string"}}}, "city": {"type": "string"}, "postalCode": {"type": "string"}, "state": {"type": "object", "properties": {"bulkLoadRunIdJfyApx": {"type": "string"}, "code": {"type": "string"}, "name": {"type": "string"}, "id": {"type": "string", "format": "uuid"}, "bulkLoadRecIdJfyApx": {"type": "string"}}}, "line2": {"type": "string"}, "line1": {"type": "string"}}}, "exemptFromBackupWithholding": {"type": "boolean"}, "investmentExperienceEquities": {"type": "string"}, "investmentExperienceFixedIncome": {"type": "string"}, "investmentExperienceFixedIncomeTransactions": {"type": "integer"}, "investExperienceAlternativesTransactions": {"type": "integer"}, "employer": {"type": "string"}, "employerPhoneNumber": {"type": "string"}, "investmentExperienceMarginTransactions": {"type": "integer"}, "foreignOfficial": {"type": "object", "properties": {"foreignOfficialCountry": {"type": "object", "properties": {"bulkLoadRunIdJfyApx": {"type": "string"}, "code3Letters": {"type": "string"}, "code2Letters": {"type": "string"}, "sortOrder": {"type": "integer"}, "fullName": {"type": "string"}, "id": {"type": "string", "format": "uuid"}, "shortName": {"type": "string"}, "bulkLoadRecIdJfyApx": {"type": "string"}}}}}, "primaryEmail": {"type": "string", "format": "email"}}}, "spouseIsAJointOwner": {"type": "boolean"}, "trustedContactRelationship": {"type": "string"}, "trustedContact": {"type": "object", "properties": {"mailingAddress": {"type": "object", "properties": {"country": {"type": "object", "properties": {"bulkLoadRunIdJfyApx": {"type": "string"}, "code3Letters": {"type": "string"}, "code2Letters": {"type": "string"}, "sortOrder": {"type": "integer"}, "fullName": {"type": "string"}, "id": {"type": "string", "format": "uuid"}, "shortName": {"type": "string"}, "bulkLoadRecIdJfyApx": {"type": "string"}}}, "city": {"type": "string"}, "postalCode": {"type": "string"}, "state": {"type": "object", "properties": {"bulkLoadRunIdJfyApx": {"type": "string"}, "code": {"type": "string"}, "name": {"type": "string"}, "id": {"type": "string", "format": "uuid"}, "bulkLoadRecIdJfyApx": {"type": "string"}}}, "line2": {"type": "string"}, "line1": {"type": "string"}}}, "fullName": {"type": "string"}, "primaryPhoneNumber": {"type": "string"}, "primaryEmail": {"type": "string", "format": "email"}}}, "trustedContactInfoDeclined": {"type": "boolean"}, "id": {"type": "string", "format": "uuid"}, "includeTrustedContact": {"type": "boolean"}}}, "investmentObjective": {"type": "string"}, "liquidAssets": {"type": "string"}, "sePIRAIncludeCollectiveBargaining": {"type": "boolean"}, "accountNumber": {"type": "string"}, "siMPLEIRASalaryReductionPercentage": {"type": "number"}, "siMPLEIRASalaryReductionElectionFrequenc": {"type": "array", "items": {"type": "object"}}, "contingentBeneficiaries": {"type": "array", "items": {"type": "object"}}, "annuityDetails": {"type": "array", "items": {"type": "object"}}, "liquidityNeeds": {"type": "string"}, "siMPLEIRAEmployerContributionPercentage": {"type": "number"}, "siMPLEIRASalaryReductionStartDate": {"type": "string", "format": "date"}, "accountIsSetUpForEDelivery": {"type": "boolean"}, "irARolloverComments": {"type": "string"}, "siMPLEIRAEmployerContributionYear": {"type": "string"}, "annualExpenses": {"type": "number"}, "siMPLEIRASalaryReductionAmount": {"type": "number"}, "employerContact": {"type": "string"}, "advisorTradingDiscretion": {"type": "boolean"}, "irAOriginalAccountType": {"type": "string"}, "sePIRAEmployeeMinimumEmploymentYears": {"type": "integer"}, "siMPLEIRAEmployerNonElectiveContribution": {"type": "boolean"}, "initialFundingSource": {"type": "string"}, "repCode": {"type": "string"}, "moneyFundSweepOptIn": {"type": "boolean"}, "dividendReinvestmentOption": {"type": "string"}, "netWorthExcludingHome": {"type": "string"}, "documents": {"type": "array", "items": {"type": "object"}}, "sePIRAEmployeeMinimumAge": {"type": "integer"}, "netWorthExcludingHomeExact": {"type": "number"}, "siMPLEIRAPriorCompensationYears": {"type": "integer"}, "specialExpenses": {"type": "number"}, "createdAt": {"type": "string", "format": "date-time"}, "liquidAssetsExact": {"type": "number"}, "timeHorizon": {"type": "string"}, "siMPLEIRAEmployerContributions": {"type": "string"}, "estimatedValueOfInvestments": {"type": "string"}, "irAFinancialInstitution": {"type": "string"}, "precedingOwner": {"type": "string"}, "jointTenancyState": {"type": "object", "properties": {"name": {"type": "string"}, "id": {"type": "string", "format": "uuid"}}}, "accountCreationDetail": {"type": "string"}, "optionsRiskLevel": {"type": "string"}, "tradingPrivileges": {"type": "array", "items": {"type": "object"}}}}, "resultVariable": "accountDetail", "references": {"sections": [{"name": "Basic Information", "description": "Contains basic account information for a given account", "type": "section", "fields": [{"name": "Representative Number", "description": "The representative number", "type": "string", "fieldBinding": "${!isEmpty(accountDetail.repCode)? accountDetail.repCode : ''}"}, {"name": "Representative Name", "description": "The representative name", "type": "string", "fieldBinding": "${!isEmpty(accountDetail.repCodeLink.repName)? accountDetail.repCodeLink.repName : ''}"}, {"name": "Registration Type", "description": "The registration type", "type": "string", "fieldBinding": "${!isEmpty(accountDetail.registrationType.name)? accountDetail.registrationType.name : ''}"}, {"name": "Account Management Type", "description": "The account management type", "type": "string", "fieldBinding": "${!isEmpty(accountDetail.accountManagementType)? accountDetail.accountManagementType : ''}"}, {"name": "Account Type", "description": "The account type", "type": "string", "fieldBinding": "${!isEmpty(accountDetail.accountType)? accountDetail.accountType : ''}"}, {"name": "Open Date", "description": "The account open date", "type": "string", "fieldBinding": "${!isEmpty(accountDetail.openDate)? accountDetail.openDate : ''}"}, {"name": "Governing State", "description": "The governing state", "type": "string", "fieldBinding": "${!isEmpty(accountDetail.governingState)? accountDetail.governingState : ''}"}]}, {"name": "Account Funding", "description": "Contains Account Funding Information for a given account", "type": "section", "fields": [{"name": "Source of funds", "description": "Tells the source of funds for the account", "type": "array", "fieldBinding": "${!isEmpty(accountDetail.initialFundingSources)? accountDetail.initialFundingSources : ''}"}]}, {"name": "Account Features", "description": "Contains Account Features", "type": "section", "fields": [{"name": "Money Fund Sweep Opt-in", "description": "Money fund sweep opt-in status", "type": "string", "fieldBinding": "${!isEmpty(accountDetail.moneyFundSweepOptIn)? accountDetail.moneyFundSweepOptIn : ''}"}, {"name": "Cash Dividend Option", "description": "Cash dividend option setting", "type": "string", "fieldBinding": "${!isEmpty(accountDetail.cashDividendOption)? accountDetail.cashDividendOption : ''}"}, {"name": "Dividend Reinvestment Option", "description": "Dividend reinvestment option setting", "type": "string", "fieldBinding": "${!isEmpty(accountDetail.dividendReinvestmentOption)? accountDetail.dividendReinvestmentOption : ''}"}, {"name": "Trading Privileges", "description": "Trading privileges for the account", "type": "string", "fieldBinding": "${!isEmpty(accountDetail.tradingPrivilege)? accountDetail.tradingPrivilege : ''}"}, {"name": "Advisor Trading Discretion", "description": "Advisor trading discretion setting", "type": "string", "fieldBinding": "${!isEmpty(accountDetail.advisorTradingDiscretion)? accountDetail.advisorTradingDiscretion : ''}"}, {"name": "<PERSON>", "description": "Account nickname", "type": "string", "fieldBinding": "${!isEmpty(accountDetail.nickName)? accountDetail.nickName : ''}"}]}, {"name": "Agreements", "description": "Contains Account Agreements", "type": "section", "fields": [{"name": "Margin Agreement Document Revision", "description": "Document revision for margin agreement", "type": "string", "fieldBinding": "${!isEmpty(accountDetail)? accountDetail : ''}"}, {"name": "Margin Agreement Hold E-Signature", "description": "Hold e-signature status for margin agreement", "type": "boolean", "fieldBinding": "${!isEmpty(accountDetail)? accountDetail : ''}"}, {"name": "Options Agreement Document Revision", "description": "Document revision for options agreement", "type": "string", "fieldBinding": "${!isEmpty(accountDetail)? accountDetail : ''}"}, {"name": "Options Agreement Hold E-Signature", "description": "Hold e-signature status for options agreement", "type": "boolean", "fieldBinding": "${!isEmpty(accountDetail)? accountDetail : ''}"}]}]}, "id": "ff043860-ed0d-485f-9fb8-7cc39b6a8160"}], "sections": [{"name": "Basic Information", "description": "Contains basic information about the account", "type": "section", "fields": [{"name": "Representative Number", "description": "The representative number", "type": "string", "fieldBinding": "${!isEmpty(accountDetail.repCode)? accountDetail.repCode : ''}"}, {"name": "Representative Name", "description": "The representative name", "type": "string", "fieldBinding": "${!isEmpty(accountDetail.repCodeLink.repName)? accountDetail.repCodeLink.repName : ''}"}, {"name": "Registration Type", "description": "The registration type", "type": "string", "fieldBinding": "${!isEmpty(accountDetail.registrationType.name)? accountDetail.registrationType.name : ''}"}, {"name": "Account Management Type", "description": "The account management type", "type": "string", "fieldBinding": "${!isEmpty(accountDetail.accountManagementType)? accountDetail.accountManagementType : ''}"}, {"name": "Account Type", "description": "The account type", "type": "string", "fieldBinding": "${!isEmpty(accountDetail.accountType)? accountDetail.accountType : ''}"}, {"name": "Open Date", "description": "The account open date", "type": "string", "fieldBinding": "${!isEmpty(accountDetail.openDate)? accountDetail.openDate : ''}"}, {"name": "Governing State", "description": "The governing state", "type": "string", "fieldBinding": "${!isEmpty(accountDetail.governingState)? accountDetail.governingState : ''}"}]}, {"name": "Account Funding", "description": "Contains Account Funding Information", "type": "section", "fields": [{"name": "Source of funds", "description": "Tells the source of funds for the account", "type": "array", "fieldBinding": "${!isEmpty(accountDetail.initialFundingSources)? accountDetail.initialFundingSources : ''}"}]}, {"name": "Account Features", "description": "Contains Account Features", "type": "section", "fields": [{"name": "Money Fund Sweep Opt-in", "description": "Money fund sweep opt-in status", "type": "string", "fieldBinding": "${!isEmpty(accountDetail.moneyFundSweepOptIn)? accountDetail.moneyFundSweepOptIn : ''}"}, {"name": "Cash Dividend Option", "description": "Cash dividend option setting", "type": "string", "fieldBinding": "${!isEmpty(accountDetail.cashDividendOption)? accountDetail.cashDividendOption : ''}"}, {"name": "Dividend Reinvestment Option", "description": "Dividend reinvestment option setting", "type": "string", "fieldBinding": "${!isEmpty(accountDetail.dividendReinvestmentOption)? accountDetail.dividendReinvestmentOption : ''}"}, {"name": "Trading Privileges", "description": "Trading privileges for the account", "type": "string", "fieldBinding": "${!isEmpty(accountDetail.tradingPrivilege)? accountDetail.tradingPrivilege : ''}"}, {"name": "Advisor Trading Discretion", "description": "Advisor trading discretion setting", "type": "string", "fieldBinding": "${!isEmpty(accountDetail.advisorTradingDiscretion)? accountDetail.advisorTradingDiscretion : ''}"}, {"name": "<PERSON>", "description": "Account nickname", "type": "string", "fieldBinding": "${!isEmpty(accountDetail.nickName)? accountDetail.nickName : ''}"}]}, {"name": "Agreements", "description": "Contains Account Agreements", "type": "section", "fields": [{"name": "Margin Agreement Document Revision", "description": "Document revision for margin agreement", "type": "string", "fieldBinding": "${!isEmpty(accountDetail)? accountDetail : ''}"}, {"name": "Margin Agreement Hold E-Signature", "description": "Hold e-signature status for margin agreement", "type": "boolean", "fieldBinding": "${!isEmpty(accountDetail)? accountDetail : ''}"}, {"name": "Options Agreement Document Revision", "description": "Document revision for options agreement", "type": "string", "fieldBinding": "${!isEmpty(accountDetail)? accountDetail : ''}"}, {"name": "Options Agreement Hold E-Signature", "description": "Hold e-signature status for options agreement", "type": "boolean", "fieldBinding": "${!isEmpty(accountDetail)? accountDetail : ''}"}]}]}, {"id": "SingleAccountBalancesPage", "title": "Single Account Balances", "url": "/single_account_balances", "parameters": [{"name": "actnum", "value": "${Account Number}"}], "description": "Single account balances", "endpoints": [{"description": "Get balances for the account or Get prior-day single account balance details. Prerequisite : Get account id first from the endpoint - Get account id and details for a given account number or account name. If just mentioned about balances then use this endpoint for prior day balances", "url": "/api/domain/wealthdomain/account/{accountId}", "base_url": "http://app-data-manager:8002", "query_params": {"_fields": "dailyBalances.recentDeposits,dailyBalances.fundsFrozenForChecks,dailyBalances.miscellaneousCreditOrDebit,dailyBalances.accruedDividends,dailyBalances.cashManagementDDANumber,dailyBalances.previousAuthorizationLimit,dailyBalances.availableFundsToTrade,dailyBalances.availableFundsToWithdraw,dailyBalances.endingBalance,dailyBalances.longMarketValue,dailyBalances.shortMarketValue,dailyBalances.endingMarginBalance,dailyBalances.accumulatedFedCall,dailyBalances.fedCall,dailyBalances.houseCall,dailyBalances.maintenanceCall,dailyBalances.cashAccountMarketValue,dailyBalances.cashAccountMarginValue,dailyBalances.marginAccountCashAvailable,dailyBalances.endingCashBalance,dailyBalances.endingMoneyMarketBalance,dailyBalances.tradeDateBalance,dailyBalances.settlementDateBalance,dailyBalances.dayTradeBuyingPower"}, "method": "get", "path_params": {"accountId": "${accountId}"}, "requestBody": {}, "responseSchema": {"type": "object", "properties": {"dailyBalances": {"type": "object", "properties": {"endingCashBalance": {"type": "number"}, "endingMoneyMarketBalance": {"type": "number"}, "tradeDateBalance": {"type": "number"}, "settlementDateBalance": {"type": "number"}, "dayTradeBuyingPower": {"type": "number"}}}}}, "resultVariable": "accountDetail", "references": {"sections": [{"name": "Prior Day Detailed Balances or sinle account Detailed Balances", "description": "Balance Overview: Provides various account balances like ending cash balance, ending money market balance, trade Date Balance, settle Date Fee Balance and day Trade Buying Power", "type": "section", "fields": [{"name": "Cash Balance", "fieldBinding": "${accountDetail.dailyBalances.endingCashBalance}"}, {"name": "MMF Balance", "fieldBinding": "${accountDetail.dailyBalances.endingMoneyMarketBalance}"}, {"name": "Trade Date Balance", "fieldBinding": "${accountDetail.dailyBalances.tradeDateBalance}"}, {"name": "Settlement Date Balance", "fieldBinding": "${accountDetail.dailyBalances.settlementDateBalance}"}, {"name": "SMA", "fieldBinding": "${accountDetail.dailyBalances.dayTradeBuyingPower}"}]}, {"name": "Prior Day Margin Summary or single account margin summary", "description": "Margin Summary: Summarizes key margin-related values for the account. Includes account value, long position value, short position value, margin balance, open current fed call, today fed call, house surplus and maintenance call, Market Value Type 1, Margin Security Type 1, Cash Available Type 2 ", "type": "section", "fields": [{"name": "Account Value (<PERSON><PERSON><PERSON>)", "fieldBinding": "${accountDetail.dailyBalances.endingBalance}"}, {"name": "Long Position Value", "fieldBinding": "${accountDetail.dailyBalances.longMarketValue}"}, {"name": "Short Position Value", "fieldBinding": "${accountDetail.dailyBalances.shortMarketValue}"}, {"name": "<PERSON>gin <PERSON>", "fieldBinding": "${accountDetail.dailyBalances.endingMarginBalance}"}, {"name": "Open Current Fed Call", "fieldBinding": "${accountDetail.dailyBalances.accumulatedFedCall}"}, {"name": "Today Fed Call", "fieldBinding": "${accountDetail.dailyBalances.fedCall}"}, {"name": "House Surplus", "fieldBinding": "${accountDetail.dailyBalances.houseCall}"}, {"name": "Maintenance Call", "fieldBinding": "${accountDetail.dailyBalances.maintenanceCall}"}, {"name": "Market Value Type 1", "fieldBinding": "${accountDetail.dailyBalances.cashAccountMarketValue}"}, {"name": "Margin Security Type 1", "fieldBinding": "${accountDetail.dailyBalances.cashAccountMarginValue}"}, {"name": "Cash Available Type 2 ", "fieldBinding": "${accountDetail.dailyBalances.marginAccountCashAvailable}"}]}, {"name": "Prior Day Current Cash or single account current cash", "description": "Shows the current Funds Available to Withdraw and Funds Available to Trade", "type": "section", "fields": [{"name": "Funds Available to Withdraw", "fieldBinding": "${accountDetail.dailyBalances.availableFundsToWithdraw}"}, {"name": "Funds Available to Trade", "fieldBinding": "${accountDetail.dailyBalances.availableFundsToTrade}"}]}, {"name": "Prior Day Other Balances or single account other balances", "description": "Other balance related information related to the account including Recent Deposits, Funds Frozen for Checks, Free CR/Misc DR, Accrued Dividends, Cash Management - DDA Number, and Previous Authorization Limits", "type": "section", "_fields": "dailyBalances.recentDeposits,dailyBalances.fundsFrozenForChecks,dailyBalances.miscellaneousCreditOrDebit,dailyBalances.accruedDividends,dailyBalances.cashManagementDDANumber,dailyBalances.previousAuthorizationLimit", "fields": [{"name": "Recent Deposits", "fieldBinding": "${accountDetail.dailyBalances.recentDeposits}"}, {"name": "Funds Frozen for Checks", "fieldBinding": "${accountDetail.dailyBalances.fundsFrozenForChecks}"}, {"name": "Free CR/Misc DR", "fieldBinding": "${accountDetail.dailyBalances.miscellaneousCreditOrDebit}"}, {"name": "Accrued Dividends", "fieldBinding": "${accountDetail.dailyBalances.accruedDividends}"}, {"name": "Cash Management - DDA Number", "fieldBinding": "${accountDetail.dailyBalances.cashManagementDDANumber}"}, {"name": "Previous Authorization Limits", "fieldBinding": "${accountDetail.dailyBalances.previousAuthorizationLimit}"}]}, {"name": "Prior Day Balance By account type or single account balance by account type", "description": "Settlement Date Balance and Trade Date Balance for different account types such as Cash, Margin and Short", "type": "section", "fields": [{"name": "TD(Trade Date) Cash Balance", "fieldBinding": "${accountDetail.dailyBalances.tradeDateCashBalance}"}, {"name": "SD(Settlement Date) Cash Balance", "fieldBinding": "${accountDetail.dailyBalances.settlementDateCashBalance}"}, {"name": "TD(Trade Date) Mar<PERSON> Balance", "fieldBinding": "${accountDetail.dailyBalances.tradeDateMarginBalance}"}, {"name": "SD(Settlement Date) Margin Balance", "fieldBinding": "${accountDetail.dailyBalances.settlementDateMarginBalance}"}, {"name": "TD(Trade Date) Short Balance", "fieldBinding": "${accountDetail.dailyBalances.tradeDateShortBalance}"}, {"name": "SD(Settlement Date) Short Balance", "fieldBinding": "${accountDetail.dailyBalances.settlementDateShortBalance}"}]}]}, "id": "b4741266-6513-4bf4-a300-66dcc6824970"}, {"description": "Get intra-day single account balance details. Prerequisite : Get account id first from the endpoint - Get account id and details for a given account number or account name", "url": "/workflow/v1/instances/execute/sync/domain/wealthdomain/crhrrq2tv7apgphov6lg", "base_url": "http://workhorse:8080", "query_params": {}, "method": "post", "path_params": {}, "requestBody": {"arguments": {"accountId": "${accountId}", "pageStart": "0", "pageSize": "${10}", "custodianType": "${gorg}"}}, "responseSchema": {"type": "object", "properties": {"tradeDateBalance": {"type": "number", "description": "Balance as of the trade date."}, "settleDateFeeBalance": {"type": "number", "description": "Balance of fees as of the settlement date."}, "cashAccountCashAvailable": {"type": "number", "description": "Available cash in the cash account."}, "endingMoneyMarketBalance": {"type": "number", "description": "Ending balance of money market funds."}, "dayTradeBuyingPower": {"type": "number", "description": "Buying power available for day trading."}, "recentDeposits": {"type": "number", "description": "Amount of recent deposits."}, "miscellaneousCreditorDebit": {"type": "number", "description": "Miscellaneous credit or debit."}, "houseCall": {"type": "number", "description": "House call amount."}, "endingBalance": {"type": "number", "description": "The total ending balance."}, "fundsFrozenForChecks": {"type": "number", "description": "Funds frozen for outstanding checks."}, "shortMarketValue": {"type": "number", "description": "Total market value of short positions."}, "endingCashBalance": {"type": "number", "description": "The ending cash balance."}, "endingMarginBalance": {"type": "number", "description": "The ending margin balance."}, "intraDayTimestamp": {"type": "string", "format": "date-time", "description": "Timestamp for the intraday data."}, "liquidationValue": {"type": "number", "description": "The liquidation value of the account."}, "periodType": {"type": "string", "description": "The type of period for the balance data (e.g., Intraday, Daily, Monthly).", "enum": ["Intraday", "Daily", "Monthly", "Yearly"]}, "fedCall": {"type": "number", "description": "Federal call amount."}, "availableFundsToTrade": {"type": "number", "description": "Funds available for trading."}, "availableFundsToWithdraw": {"type": "number", "description": "Funds available for withdrawal."}, "smABalance": {"type": "number", "description": "Special Memorandum Account (SMA) balance."}, "marginAccountCashAvailable": {"type": "number", "description": "Available cash in the margin account."}, "accumulatedFedCall": {"type": "number", "description": "Accumulated federal call amount."}, "longMarketValue": {"type": "number", "description": "Total market value of long positions."}}}, "resultVariable": "intraDayAccountDetail", "references": {"sections": [{"name": "Intra Day Detailed Balances", "description": "Balance Overview: Provides various account balances like ending cash balance, ending money market balance (MMF Balance), SMA", "type": "section", "fields": [{"name": "Cash Balance", "fieldBinding": "${intraDayAccountDetail.endingCashBalance}"}, {"name": "MMF Balance", "fieldBinding": "${intraDayAccountDetail.endingMoneyMarketBalance}"}, {"name": "SMA", "fieldBinding": "${intraDayAccountDetail.smABalance}"}]}, {"name": "Intra Day Margin Summary", "description": "Margin Summary: Summarizes key margin-related values for the account. Includes account value, long position value, short position value, margin balance, open current fed call, today fed call, house surplus and maintenance call, Market Value Type 1, Margin Security Type 1, Cash Available Type 2,Margin Buying Power", "type": "section", "fields": [{"name": "Account Value (<PERSON><PERSON><PERSON>)", "fieldBinding": "${intraDayAccountDetail.endingBalance}"}, {"name": "Long Position Value", "fieldBinding": "${intraDayAccountDetail.longMarketValue}"}, {"name": "Short Position Value", "fieldBinding": "${intraDayAccountDetail.shortMarketValue}"}, {"name": "<PERSON>gin <PERSON>", "fieldBinding": "${intraDayAccountDetail.endingMarginBalance}"}, {"name": "Open Current Fed Call", "fieldBinding": "${intraDayAccountDetail.accumulatedFedCall}"}, {"name": "Today Fed Call", "fieldBinding": "${intraDayAccountDetail.fedCall}"}, {"name": "House Excess", "fieldBinding": "${intraDayAccountDetail.maintenanceCall}"}, {"name": "Cash Available Type 2", "fieldBinding": "${intraDayAccountDetail.marginAccountCashAvailable}"}, {"name": "Margin Buying Power", "fieldBinding": "${intraDayAccountDetail.dayTradeBuyingPower}"}]}, {"name": "Intra Day Current Cash", "description": "Shows the current Funds Available to Withdraw and Funds Available to Trade", "type": "section", "fields": [{"name": "Funds Available to Withdraw", "fieldBinding": "${intraDayAccountDetail.availableFundsToWithdraw}"}, {"name": "Funds Available to Trade", "fieldBinding": "${intraDayAccountDetail.availableFundsToTrade}"}]}, {"name": "Intra Day Other Balances", "description": "Other balance related information related to the account including Recent Deposits, Funds Frozen for Checks, Free CR/Misc DR, Accrued Dividends, Cash Management - DDA Number, and Previous Authorization Limits", "type": "section", "_fields": "dailyBalances.recentDeposits,dailyBalances.fundsFrozenForChecks,dailyBalances.miscellaneousCreditOrDebit,dailyBalances.accruedDividends,dailyBalances.cashManagementDDANumber,dailyBalances.previousAuthorizationLimit", "fields": [{"name": "Recent Deposits", "fieldBinding": "${intraDayAccountDetail.recentDeposits}"}, {"name": "Funds Frozen for Checks", "fieldBinding": "${intraDayAccountDetail.fundsFrozenForChecks}"}, {"name": "Free CR/Misc DR", "fieldBinding": "${intraDayAccountDetail.miscellaneousCreditOrDebit}"}]}]}, "id": "fb6bfa88-db47-4350-a510-7dbff0724a09"}], "sections": [{"name": "Prior Day Detailed Balances", "description": "Balance Overview: Provides various account balances like ending cash balance, ending money market balance, trade Date Balance, settle Date Fee Balance and day Trade Buying Power", "type": "section", "fields": [{"name": "Cash Balance", "fieldBinding": "${accountDetail.dailyBalances.endingCashBalance}"}, {"name": "MMF Balance", "fieldBinding": "${accountDetail.dailyBalances.endingMoneyMarketBalance}"}, {"name": "Trade Date Balance", "fieldBinding": "${accountDetail.dailyBalances.tradeDateBalance}"}, {"name": "Settlement Date Balance", "fieldBinding": "${accountDetail.dailyBalances.settlementDateBalance}"}, {"name": "SMA", "fieldBinding": "${accountDetail.dailyBalances.dayTradeBuyingPower}"}]}, {"name": "Prior Day Margin Summary", "description": "Margin Summary: Summarizes key margin-related values for the account. Includes account value, long position value, short position value, margin balance, open current fed call, today fed call, house surplus and maintenance call, Market Value Type 1, Margin Security Type 1, Cash Available Type 2 ", "type": "section", "fields": [{"name": "Account Value (<PERSON><PERSON><PERSON>)", "fieldBinding": "${accountDetail.dailyBalances.endingBalance}"}, {"name": "Long Position Value", "fieldBinding": "${accountDetail.dailyBalances.longMarketValue}"}, {"name": "Short Position Value", "fieldBinding": "${accountDetail.dailyBalances.shortMarketValue}"}, {"name": "<PERSON>gin <PERSON>", "fieldBinding": "${accountDetail.dailyBalances.endingMarginBalance}"}, {"name": "Open Current Fed Call", "fieldBinding": "${accountDetail.dailyBalances.accumulatedFedCall}"}, {"name": "Today Fed Call", "fieldBinding": "${accountDetail.dailyBalances.fedCall}"}, {"name": "House Surplus", "fieldBinding": "${accountDetail.dailyBalances.houseCall}"}, {"name": "Maintenance Call", "fieldBinding": "${accountDetail.dailyBalances.maintenanceCall}"}, {"name": "Market Value Type 1", "fieldBinding": "${accountDetail.dailyBalances.cashAccountMarketValue}"}, {"name": "Margin Security Type 1", "fieldBinding": "${accountDetail.dailyBalances.cashAccountMarginValue}"}, {"name": "Cash Available Type 2 ", "fieldBinding": "${accountDetail.dailyBalances.marginAccountCashAvailable}"}]}, {"name": "Prior Day Current Cash", "description": "Shows the current Funds Available to Withdraw and Funds Available to Trade", "type": "section", "fields": [{"name": "Funds Available to Withdraw", "fieldBinding": "${accountDetail.dailyBalances.availableFundsToWithdraw}"}, {"name": "Funds Available to Trade", "fieldBinding": "${accountDetail.dailyBalances.availableFundsToTrade}"}]}, {"name": "Prior Day Other Balances", "description": "Other financial information related to the account including Recent Deposits, Funds Frozen for Checks, Free CR/Misc DR, Accrued Dividends, Cash Management - DDA Number, and Previous Authorization Limits", "type": "section", "_fields": "dailyBalances.recentDeposits,dailyBalances.fundsFrozenForChecks,dailyBalances.miscellaneousCreditOrDebit,dailyBalances.accruedDividends,dailyBalances.cashManagementDDANumber,dailyBalances.previousAuthorizationLimit", "fields": [{"name": "Recent Deposits", "fieldBinding": "${accountDetail.dailyBalances.recentDeposits}"}, {"name": "Funds Frozen for Checks", "fieldBinding": "${accountDetail.dailyBalances.fundsFrozenForChecks}"}, {"name": "Free CR/Misc DR", "fieldBinding": "${accountDetail.dailyBalances.miscellaneousCreditOrDebit}"}, {"name": "Accrued Dividends", "fieldBinding": "${accountDetail.dailyBalances.accruedDividends}"}, {"name": "Cash Management - DDA Number", "fieldBinding": "${accountDetail.dailyBalances.cashManagementDDANumber}"}, {"name": "Previous Authorization Limits", "fieldBinding": "${accountDetail.dailyBalances.previousAuthorizationLimit}"}]}, {"name": "Prior Day Balance By account type", "description": "Settlement Date Balance and Trade Date Balance for different account types such as Cash, Margin and Short", "type": "section", "fields": [{"name": "TD(Trade Date) Cash Balance", "fieldBinding": "${accountDetail.dailyBalances.tradeDateCashBalance}"}, {"name": "SD(Settlement Date) Cash Balance", "fieldBinding": "${accountDetail.dailyBalances.settlementDateCashBalance}"}, {"name": "TD(Trade Date) Mar<PERSON> Balance", "fieldBinding": "${accountDetail.dailyBalances.tradeDateMarginBalance}"}, {"name": "SD(Settlement Date) Margin Balance", "fieldBinding": "${accountDetail.dailyBalances.settlementDateMarginBalance}"}, {"name": "TD(Trade Date) Short Balance", "fieldBinding": "${accountDetail.dailyBalances.tradeDateShortBalance}"}, {"name": "SD(Settlement Date) Short Balance", "fieldBinding": "${accountDetail.dailyBalances.settlementDateShortBalance}"}]}, {"name": "Intra Day Detailed Balances", "description": "Balance Overview: Provides various account balances like ending cash balance, ending money market balance (MMF Balance), SMA", "type": "section", "fields": [{"name": "Cash Balance", "fieldBinding": "${intraDayAccountDetail.endingCashBalance}"}, {"name": "MMF Balance", "fieldBinding": "${intraDayAccountDetail.endingMoneyMarketBalance}"}, {"name": "SMA", "fieldBinding": "${intraDayAccountDetail.smABalance}"}]}, {"name": "Intra Day Margin Summary", "description": "Margin Summary: Summarizes key margin-related values for the account. Includes account value, long position value, short position value, margin balance, open current fed call, today fed call, house surplus and maintenance call, Market Value Type 1, Margin Security Type 1, Cash Available Type 2,Margin Buying Power", "type": "section", "fields": [{"name": "Account Value (<PERSON><PERSON><PERSON>)", "fieldBinding": "${intraDayAccountDetail.endingBalance}"}, {"name": "Long Position Value", "fieldBinding": "${intraDayAccountDetail.longMarketValue}"}, {"name": "Short Position Value", "fieldBinding": "${intraDayAccountDetail.shortMarketValue}"}, {"name": "<PERSON>gin <PERSON>", "fieldBinding": "${intraDayAccountDetail.endingMarginBalance}"}, {"name": "Open Current Fed Call", "fieldBinding": "${intraDayAccountDetail.accumulatedFedCall}"}, {"name": "Today Fed Call", "fieldBinding": "${intraDayAccountDetail.fedCall}"}, {"name": "House Excess", "fieldBinding": "${intraDayAccountDetail.maintenanceCall}"}, {"name": "Cash Available Type 2", "fieldBinding": "${intraDayAccountDetail.marginAccountCashAvailable}"}, {"name": "Margin Buying Power", "fieldBinding": "${intraDayAccountDetail.dayTradeBuyingPower}"}]}, {"name": "Intra Day Current Cash", "description": "Shows the current Funds Available to Withdraw and Funds Available to Trade", "type": "section", "fields": [{"name": "Funds Available to Withdraw", "fieldBinding": "${intraDayAccountDetail.availableFundsToWithdraw}"}, {"name": "Funds Available to Trade", "fieldBinding": "${intraDayAccountDetail.availableFundsToTrade}"}]}, {"name": "Intra Day Other Balances", "description": "Other balance related information related to the account including Recent Deposits, Funds Frozen for Checks, Free CR/Misc DR, Accrued Dividends, Cash Management - DDA Number, and Previous Authorization Limits", "type": "section", "_fields": "dailyBalances.recentDeposits,dailyBalances.fundsFrozenForChecks,dailyBalances.miscellaneousCreditOrDebit,dailyBalances.accruedDividends,dailyBalances.cashManagementDDANumber,dailyBalances.previousAuthorizationLimit", "fields": [{"name": "Recent Deposits", "fieldBinding": "${intraDayAccountDetail.recentDeposits}"}, {"name": "Funds Frozen for Checks", "fieldBinding": "${intraDayAccountDetail.fundsFrozenForChecks}"}, {"name": "Free CR/Misc DR", "fieldBinding": "${intraDayAccountDetail.miscellaneousCreditOrDebit}"}]}]}, {"id": "SingleAccountNotes", "title": "Notes", "url": "/single_account_notes", "parameters": [{"name": "actnum", "value": "${Account Number}"}], "description": "Notes for the single account", "endpoints": [{"description": "Get the list notes for a given account number", "url": "/workflow/v1/instances/execute/sync/domain/wealthdomain/ctb9an8q0ekk1cu1csj0", "base_url": "http://workhorse:8080", "method": "post", "query_params": {}, "path_params": {}, "requestBody": {"arguments": {"accountNumber": "${Account Number}", "date": "", "inquiryType": "", "padType": "", "alphaKey": ""}}, "responseSchema": {"type": "array", "items": {"type": "object", "properties": {"note": {"type": "string"}, "changeWhoCode": {"type": "string"}, "addTimestamp": {"type": "string"}, "padType": {"type": "string"}, "changeDateCYMD": {"type": "string"}, "alphaKey": {"type": "string"}, "effectiveDateCYMD": {"type": "string"}, "accountNumber": {"type": "string"}}}}, "resultVariable": "notesDataTable", "id": "e65af21e-4d48-4ef8-8076-f5654ef27323"}], "tables": [{"name": "Notes", "description": "Account Notes list", "columns": [{"name": "Effective Date", "type": "string", "fieldBinding": "${!isEmpty(notesDataTable[i].effectiveDateCYMD)? notesDataTable[i].effectiveDateCYMD : ''}"}, {"name": "Account Number", "type": "string", "fieldBinding": "${!isEmpty(notesDataTable[i].accountNumber)? notesDataTable[i].accountNumber : \" \"}"}, {"name": "Note", "type": "string", "fieldBinding": "${!isEmpty(notesDataTable[i].note)? notesDataTable[i].note : \" \"}"}, {"name": "Created Date", "type": "string", "fieldBinding": "${!isEmpty(notesDataTable[i].addTimestamp)? notesDataTable[i].addTimestamp : \" \"}"}, {"name": "Change Date", "type": "string", "fieldBinding": "${!isEmpty(notesDataTable[i].changeDateCYMD)? notesDataTable[i].changeDateCYMD : \" \"}"}]}]}, {"id": "SingleAccountDocuments", "title": "Documents", "url": "/single_account_documents", "parameters": [{"name": "actnum", "value": "${Account Number}"}], "description": "List of documents for the single account", "endpoints": [{"description": "Get the list of documents for the given account number", "url": "/workflow/v1/instances/execute/sync/domain/wealthdomain/ct000bbavopq04o4a1n0", "base_url": "http://workhorse:8080", "method": "post", "query_params": {}, "path_params": {}, "requestBody": {"arguments": {"documentMetadata": [{"key": "Account ID", "value": "${Account Number}"}], "_fields": "id,0.id,0.key,0.value"}}, "responseSchema": {"type": "array", "items": {"type": "object", "properties": {"createdAt": {"type": "string"}, "documentType": {"type": "string"}, "createdBy": {"type": "string"}, "document": {"type": "string"}, "tag": {"type": "string"}, "id": {"type": "integer"}, "externalId": {"type": "integer"}}}}, "resultVariable": "documentsDataTable", "id": "70a47d3f-1627-4c4d-af02-7b182e0f51c9"}], "tables": [{"name": "Documents", "description": "Account documents list", "columns": [{"name": "Document Type", "type": "string", "fieldBinding": "${!isEmpty(documentsDataTable[i].documentType)? documentsDataTable[i].documentType : \" \"}"}, {"name": "Document Status", "type": "string", "fieldBinding": "${!isEmpty(documentsDataTable[i].documentStatus)? documentsDataTable[i].documentStatus : \" \"}"}, {"name": "Description", "type": "string", "fieldBinding": "${!isEmpty(documentsDataTable[i].description)? documentsDataTable[i].description : \" \"}"}, {"name": "Created By", "type": "string", "fieldBinding": "${!isEmpty(documentsDataTable[i].createdBy)? documentsDataTable[i].createdBy : \" \"}"}, {"name": "Created At", "type": "string", "fieldBinding": "${!isEmpty(documentsDataTable[i].createdAt)? documentsDataTable[i].createdAt : \" \"}"}]}]}, {"id": "SingleAccountServiceRequests", "title": "Service Requests", "url": "/single_account_service_requests", "parameters": [{"name": "actnum", "value": "${Account Number}"}], "description": "List of service requests for the single account", "endpoints": [{"description": "Get the list of service requests (SR) for the given account number", "url": "/api/domain/wealthdomain/srInstance/dynamic/query/serviceRequestsOfAccount", "base_url": "http://app-data-manager:8002", "method": "get", "query_params": {"_page": "0", "_size": "${10}", "accountNumber": "${accountNumber}"}, "path_params": {}, "requestBody": {}, "responseSchema": {"type": "array", "items": {"type": "object", "properties": {"accountNumber": {"type": "string"}, "boInstanceID": {"type": "string"}, "clientName": {"type": "string"}, "createdAt": {"type": "string"}, "createdBy": {"type": "string"}, "currentTask": {"type": "object", "properties": {"assignedTo": {"type": "string"}, "currentStatus": {"type": "object", "properties": {"status": {"type": "object", "properties": {"description": {"type": "string"}}}}}, "id": {"type": "string"}, "status1": {"type": "object", "properties": {"id": {"type": "string"}}}}}, "id": {"type": "string"}, "lastModifiedAt": {"type": "string"}, "srDef": {"type": "object", "properties": {"category": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}}}, "id": {"type": "string"}, "priority": {"type": "string"}, "subCategory": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}}}}}, "srId": {"type": "string"}}}}, "resultVariable": "serviceRequestsDataTable", "id": "1c80fde6-f1c0-49f0-9385-dab4bb3920e7"}], "tables": [{"name": "Service Requests", "description": "Service requests list for the account", "columns": [{"name": "SR ID", "type": "string", "fieldBinding": "${!isEmpty(serviceRequestsDataTable[i].srId)? serviceRequestsDataTable[i].srId : \" \"}"}, {"name": "Category", "type": "string", "fieldBinding": "${!isEmpty(serviceRequestsDataTable[i].srDef.category.name)? serviceRequestsDataTable[i].srDef.category.name : \" \"}"}, {"name": "Sub Category", "type": "string", "fieldBinding": "${!isEmpty(serviceRequestsDataTable[i].srDef.subCategory.name)? serviceRequestsDataTable[i].srDef.subCategory.name : \" \"}"}, {"name": "Description", "type": "string", "fieldBinding": "${!isEmpty(serviceRequestsDataTable[i].currentTask.currentStatus.status.description)? serviceRequestsDataTable[i].currentTask.currentStatus.status.description : \" \"}"}, {"name": "Priority", "type": "string", "fieldBinding": "${!isEmpty(serviceRequestsDataTable[i].srDef.priority)? serviceRequestsDataTable[i].srDef.priority : \" \"}"}, {"name": "Assigned To", "type": "string", "fieldBinding": "${!isEmpty(serviceRequestsDataTable[i].currentTask.assignedTo.firstName)? serviceRequestsDataTable[i].currentTask.assignedTo.firstName : \" \"}"}, {"name": "Created Date", "type": "string", "fieldBinding": "${!isEmpty(serviceRequestsDataTable[i].createdAt)? serviceRequestsDataTable[i].createdAt : \" \"}"}, {"name": "Last Updated", "type": "string", "fieldBinding": "${!isEmpty(serviceRequestsDataTable[i].lastModifiedAt)? serviceRequestsDataTable[i].lastModifiedAt : \" \"}"}]}]}, {"id": "SingleAccountActivityPriorDay", "title": "Activity", "url": "/single_account_activity", "parameters": [{"name": "actnum", "value": "${Account Number}"}], "description": "Single Account Activity - prior day", "endpoints": [{"description": "Get prior day activity list for a given account id. Default duration is 24 months, but based on the user input decided the duration. Prerequisite : Get account id first from the endpoint - Get account id and details for a given account number or account name", "url": "/api/domain/wealthdomain/j_Transaction/dynamic/query/accountActivityByAccountId", "base_url": "http://app-data-manager:8002", "method": "get", "query_params": {"_page": "0", "_size": "${10}", "_sort": "-transactionDate", "accountId": "${accountId}", "latestAsOfDate": "${gvGlobalAsOfDate}", "startDate": "${subMonths(gvGlobalAsOfDate, 24)}"}, "path_params": {}, "requestBody": {}, "responseSchema": {"type": "array", "items": {"type": "object", "properties": {"account": {"type": "object", "properties": {"id": {"type": "string"}, "subType": {"type": "string"}}}, "accountNumber": {"type": "string"}, "accountTypeCode": {"type": "string"}, "amount": {"type": "number"}, "code": {"type": "string"}, "commission": {"type": "number"}, "cusip": {"type": "string"}, "custodianAssignedSecurityID": {"type": "string"}, "id": {"type": "string"}, "isCancelled": {"type": "boolean"}, "isRebilled": {"type": "boolean"}, "longDescription": {"type": "string"}, "orderType": {"type": "string"}, "originalCommission": {"type": "number"}, "postDate": {"type": "string"}, "price": {"type": "number"}, "quantity": {"type": "number"}, "security": {"type": "object", "properties": {"custodianAssignedID": {"type": "string"}, "id": {"type": "string"}, "isin": {"type": "string"}, "securityDescription": {"type": "string"}, "sedol": {"type": "string"}, "symbol": {"type": "string"}, "underlyingSecurityNumber": {"type": "string"}}}, "securityDescription": {"type": "string"}, "symbol": {"type": "string"}, "transactionDate": {"type": "string"}, "transactionRecordSource": {"type": "string"}, "transactionType": {"type": "string"}}}}, "resultVariable": "singleAccountActivityData", "id": "346afbbf-121f-43da-ab95-17f2d1d9dcc3"}], "tables": [{"name": "Activity", "description": "Single account activity data table - prior day", "columns": [{"name": "Date", "type": "string", "fieldBinding": "${!isEmpty(singleAccountActivityData[i].transactionDate)? singleAccountActivityData[i].transactionDate : \" \"}"}, {"name": "Transaction Description", "type": "string", "fieldBinding": "${!isEmpty(singleAccountActivityData[i].longDescription)? singleAccountActivityData[i].longDescription : \" \"}"}, {"name": "Transaction Code", "type": "string", "fieldBinding": "${!isEmpty(singleAccountActivityData[i].transactionRecordSource)? singleAccountActivityData[i].transactionRecordSource : \" \"}"}, {"name": "Transaction Type", "type": "string", "fieldBinding": "${!isEmpty(singleAccountActivityData[i].transactionType)? singleAccountActivityData[i].transactionType : \" \"}"}, {"name": "Security Number", "type": "string", "fieldBinding": "${!isEmpty(singleAccountActivityData[i].security.underlyingSecurityNumber)? singleAccountActivityData[i].security.underlyingSecurityNumber : \" \"}"}, {"name": "Security Description", "type": "string", "fieldBinding": "${!isEmpty(singleAccountActivityData[i].security.securityDescription)? singleAccountActivityData[i].security.securityDescription : \" \"}"}, {"name": "Quantity", "type": "number", "fieldBinding": "${!isEmpty(singleAccountActivityData[i].quantity)? singleAccountActivityData[i].quantity : \" \"}"}, {"name": "Symbol", "type": "string", "fieldBinding": "${!isEmpty(singleAccountActivityData[i].security.symbol)? singleAccountActivityData[i].security.symbol : \" \"}"}, {"name": "Action", "type": "string", "fieldBinding": "${!isEmpty(singleAccountActivityData[i].orderType)? singleAccountActivityData[i].orderType : \" \"}"}, {"name": "Execution Price", "type": "currency", "fieldBinding": "${!isEmpty(singleAccountActivityData[i].price)? singleAccountActivityData[i].price : \" \"}"}, {"name": "Amount", "type": "currency", "fieldBinding": "${!isEmpty(singleAccountActivityData[i].amount)? singleAccountActivityData[i].amount : \" \"}"}, {"name": "Commission", "type": "currency", "fieldBinding": "${!isEmpty(singleAccountActivityData[i].originalCommission)? singleAccountActivityData[i].originalCommission : \" \"}"}, {"name": "Account Type", "type": "string", "fieldBinding": "${!isEmpty(singleAccountActivityData[i].accountTypeCode)? singleAccountActivityData[i].accountTypeCode : \" \"}"}, {"name": "ISIN", "type": "string", "fieldBinding": "${!isEmpty(singleAccountActivityData[i].security.isin)? singleAccountActivityData[i].security.isin : \" \"}"}, {"name": "SEDOL", "type": "string", "fieldBinding": "${!isEmpty(singleAccountActivityData[i].security.sedol)? singleAccountActivityData[i].security.sedol : \" \"}"}, {"name": "AO Date", "type": "string", "fieldBinding": "${!isEmpty(singleAccountActivityData[i].postDate)? singleAccountActivityData[i].postDate : \" \"}"}, {"name": "Is Rebilled", "type": "boolean", "fieldBinding": "${!isEmpty(singleAccountActivityData[i].isRebilled)? singleAccountActivityData[i].isRebilled : false}"}, {"name": "Is Cancelled", "type": "boolean", "fieldBinding": "${!isEmpty(singleAccountActivityData[i].isCancelled)? singleAccountActivityData[i].isCancelled : false}"}]}]}, {"id": "SingleAccountActivityIntraDay", "title": "Activity", "url": "/single_account_activity", "parameters": [{"name": "actnum", "value": "${Account Number}"}], "description": "Single Account Activity - Intra-day", "endpoints": [{"description": "Get intra day activity list for a given account id.Prerequisite : Get account id first from the endpoint - Get account id and details for a given account number or account name", "url": "/workflow/v1/instances/execute/sync/domain/wealthdomain/ctijfog8cu7jheq9potg", "base_url": "http://workhorse:8080", "method": "post", "requestBody": {"arguments": {"accountId": "${accountId}", "pageStart": "0", "pageSize": "1000", "fromDate": "2025-07-04", "providerName": ""}}, "path_params": {}, "responseSchema": {"type": "array", "items": {"type": "object", "properties": {"transactionDate": {"type": "string"}, "longDescription": {"type": "string"}, "transactionRecordSource": {"type": "string"}, "transactionType": {"type": "string"}, "security": {"type": "object", "properties": {"underlyingSecurityNumber": {"type": "string"}, "securityDescription": {"type": "string"}, "symbol": {"type": "string"}, "isin": {"type": "string"}, "sedol": {"type": "string"}}}, "quantity": {"type": "number"}, "orderType": {"type": "string"}, "price": {"type": "number", "format": "float"}, "amount": {"type": "number", "format": "float"}, "originalCommission": {"type": "number", "format": "float"}, "accountTypeCode": {"type": "string"}, "postDate": {"type": "string"}, "isRebilled": {"type": "boolean"}, "isCancelled": {"type": "boolean"}}}}, "resultVariable": "singleAccountActivityDataIntraDay", "id": "c1ea1914-928f-4031-9f97-4d7e5a614f5d"}], "tables": [{"name": "Activity", "description": "Single account activity data table - Intra-day", "columns": [{"name": "Date", "type": "string", "fieldBinding": "${!isEmpty(singleAccountActivityDataIntraDay[i].transactionDate)? singleAccountActivityDataIntraDay[i].transactionDate : \" \"}"}, {"name": "Transaction Description", "type": "string", "fieldBinding": "${!isEmpty(singleAccountActivityDataIntraDay[i].longDescription)? singleAccountActivityDataIntraDay[i].longDescription : \" \"}"}, {"name": "Transaction Code", "type": "string", "fieldBinding": "${!isEmpty(singleAccountActivityDataIntraDay[i].transactionRecordSource)? singleAccountActivityDataIntraDay[i].transactionRecordSource : \" \"}"}, {"name": "Transaction Type", "type": "string", "fieldBinding": "${!isEmpty(singleAccountActivityDataIntraDay[i].transactionType)? singleAccountActivityDataIntraDay[i].transactionType : \" \"}"}, {"name": "Security Number", "type": "string", "fieldBinding": "${!isEmpty(singleAccountActivityDataIntraDay[i].security.underlyingSecurityNumber)? singleAccountActivityDataIntraDay[i].security.underlyingSecurityNumber : \" \"}"}, {"name": "Security Description", "type": "string", "fieldBinding": "${!isEmpty(singleAccountActivityDataIntraDay[i].security.securityDescription)? singleAccountActivityDataIntraDay[i].security.securityDescription : \" \"}"}, {"name": "Quantity", "type": "number", "fieldBinding": "${!isEmpty(singleAccountActivityDataIntraDay[i].quantity)? singleAccountActivityDataIntraDay[i].quantity : \" \"}"}, {"name": "Symbol", "type": "string", "fieldBinding": "${!isEmpty(singleAccountActivityDataIntraDay[i].security.symbol)? singleAccountActivityDataIntraDay[i].security.symbol : \" \"}"}, {"name": "Action", "type": "string", "fieldBinding": "${!isEmpty(singleAccountActivityDataIntraDay[i].orderType)? singleAccountActivityDataIntraDay[i].orderType : \" \"}"}, {"name": "Execution Price", "type": "currency", "fieldBinding": "${!isEmpty(singleAccountActivityDataIntraDay[i].price)? singleAccountActivityDataIntraDay[i].price : \" \"}"}, {"name": "Amount", "type": "currency", "fieldBinding": "${!isEmpty(singleAccountActivityDataIntraDay[i].amount)? singleAccountActivityDataIntraDay[i].amount : \" \"}"}, {"name": "Commission", "type": "currency", "fieldBinding": "${!isEmpty(singleAccountActivityDataIntraDay[i].originalCommission)? singleAccountActivityDataIntraDay[i].originalCommission : \" \"}"}, {"name": "Account Type", "type": "string", "fieldBinding": "${!isEmpty(singleAccountActivityDataIntraDay[i].accountTypeCode)? singleAccountActivityDataIntraDay[i].accountTypeCode : \" \"}"}, {"name": "ISIN", "type": "string", "fieldBinding": "${!isEmpty(singleAccountActivityDataIntraDay[i].security.isin)? singleAccountActivityDataIntraDay[i].security.isin : \" \"}"}, {"name": "SEDOL", "type": "string", "fieldBinding": "${!isEmpty(singleAccountActivityDataIntraDay[i].security.sedol)? singleAccountActivityDataIntraDay[i].security.sedol : \" \"}"}, {"name": "AO Date", "type": "string", "fieldBinding": "${!isEmpty(singleAccountActivityDataIntraDay[i].postDate)? singleAccountActivityDataIntraDay[i].postDate : \" \"}"}, {"name": "Is Rebilled", "type": "boolean", "fieldBinding": "${!isEmpty(singleAccountActivityDataIntraDay[i].isRebilled)? singleAccountActivityDataIntraDay[i].isRebilled : false}"}, {"name": "Is Cancelled", "type": "boolean", "fieldBinding": "${!isEmpty(singleAccountActivityDataIntraDay[i].isCancelled)? singleAccountActivityDataIntraDay[i].isCancelled : false}"}]}]}, {"id": "SingleAccountCashFlow", "title": "Single Account Cash Flow", "url": "/single_projected_cash_flow", "parameters": [], "description": "Single Account Cash Flow", "endpoints": [{"description": "Get cash flow summary for a given account id.Prerequisite : Get account id first from the endpoint - Get account id and details for a given account number or account name", "url": "/api/domain/wealthdomain/account/dynamic/query/cashFlowDetailsOfAccount", "base_url": "http://app-data-manager:8002", "method": "get", "query_params": {"accountId": "${accountId}"}, "path_params": {}, "requestBody": {}, "responseSchema": {"type": "array", "items": {"type": "object", "properties": {"cashFlow": {"type": "array", "items": {"type": "object", "properties": {"accruedInterestPurchaseMTD": {"type": "number"}, "accruedInterestPurchaseYTD": {"type": "number"}, "accruedInterestSalesMTD": {"type": "number"}, "accruedInterestSalesYTD": {"type": "number"}, "asOfDate": {"type": "string", "format": "date"}, "bulkLoadRecIdJfyApx": {"type": "string"}, "bulkLoadRunIdJfyApx": {"type": "string"}, "corporateInterestMTD": {"type": "number"}, "corporateInterestYTD": {"type": "number"}, "creditInterestMTD": {"type": "number"}, "creditInterestYTD": {"type": "number"}, "governmentInterestMTD": {"type": "number"}, "governmentInterestYTD": {"type": "number"}, "id": {"type": "string"}, "liquidationsMTD": {"type": "number"}, "liquidationsYTD": {"type": "number"}, "longTermCapitalGainsMTD": {"type": "number"}, "longTermCapitalGainsYTD": {"type": "number"}, "moneyMarketMTD": {"type": "number"}, "moneyMarketYTD": {"type": "number"}, "municipalInterestTaxMTD": {"type": "number"}, "municipalInterestTaxYTD": {"type": "number"}, "nonQualifiedDividendsMTD": {"type": "number"}, "nonQualifiedDividendsYTD": {"type": "number"}, "otherIncomeMTD": {"type": "number"}, "otherIncomeYTD": {"type": "number"}, "partnershipDistributionsMTD": {"type": "number"}, "partnershipDistributionsYTD": {"type": "number"}, "principalPaymentsMTD": {"type": "number"}, "principalPaymentsYTD": {"type": "number"}, "qualifiedDividendsMTD": {"type": "number"}, "qualifiedDividendsYTD": {"type": "number"}, "repurchaseInterestMTD": {"type": "number"}, "repurchaseInterestYTD": {"type": "number"}, "returnOfCapitalMTD": {"type": "number"}, "returnOfCapitalYTD": {"type": "number"}, "royaltyPaymentsMTD": {"type": "number"}, "royaltyPaymentsYTD": {"type": "number"}, "shortTermCapitalGainsMTD": {"type": "number"}, "shortTermCapitalGainsYTD": {"type": "number"}, "substitutePaymentsMTD": {"type": "number"}, "substitutePaymentsYTD": {"type": "number"}, "totalIncomeMTD": {"type": "number"}, "totalIncomeYTD": {"type": "number"}}}}, "id": {"type": "string"}}}}, "resultVariable": "cashFlowDetailsData", "id": "f913d7cb-ae6e-48d2-b875-ec4debcb8d02"}], "tables": [{"name": "Account Position Details", "description": "Account Position Details Table", "columns": [{"name": "Description", "type": "string"}, {"name": "Month to Date", "type": "string"}, {"name": "Year to Date", "type": "string"}], "rows": [{"Description": "Non Qual Dividends", "Month to Date": {"fieldBinding": "${!isEmpty(cashFlowDetailsData[0]?.nonQualifiedDividendsMTD) ? cashFlowDetailsData[0].nonQualifiedDividendsMTD : ''}"}, "Year to Date": {"fieldBinding": "${!isEmpty(cashFlowDetailsData[0]?.nonQualifiedDividendsYTD) ? cashFlowDetailsData[0].nonQualifiedDividendsYTD : ''}"}}, {"Description": "Qualified Dividends", "Month to Date": {"fieldBinding": "${!isEmpty(cashFlowDetailsData[0]?.qualifiedDividendsMTD) ? cashFlowDetailsData[0].qualifiedDividendsMTD : ''}"}, "Year to Date": {"fieldBinding": "${!isEmpty(cashFlowDetailsData[0]?.qualifiedDividendsYTD) ? cashFlowDetailsData[0].qualifiedDividendsYTD : ''}"}}, {"Description": "ST Capital Gains", "Month to Date": {"fieldBinding": "${!isEmpty(cashFlowDetailsData[0]?.shortTermCapitalGainsMTD) ? cashFlowDetailsData[0].shortTermCapitalGainsMTD : ''}"}, "Year to Date": {"fieldBinding": "${!isEmpty(cashFlowDetailsData[0]?.shortTermCapitalGainsYTD) ? cashFlowDetailsData[0].shortTermCapitalGainsYTD : ''}"}}, {"Description": "LT Capital Gains", "Month to Date": {"fieldBinding": "${!isEmpty(cashFlowDetailsData[0]?.longTermCapitalGainsMTD) ? cashFlowDetailsData[0].longTermCapitalGainsMTD : ''}"}, "Year to Date": {"fieldBinding": "${!isEmpty(cashFlowDetailsData[0]?.longTermCapitalGainsYTD) ? cashFlowDetailsData[0].longTermCapitalGainsYTD : ''}"}}, {"Description": "Return of Capital", "Month to Date": {"fieldBinding": "${!isEmpty(cashFlowDetailsData[0]?.returnOfCapitalMTD) ? cashFlowDetailsData[0].returnOfCapitalMTD : ''}"}, "Year to Date": {"fieldBinding": "${!isEmpty(cashFlowDetailsData[0]?.returnOfCapitalYTD) ? cashFlowDetailsData[0].returnOfCapitalYTD : ''}"}}, {"Description": "Liquidations", "Month to Date": {"fieldBinding": "${!isEmpty(cashFlowDetailsData[0]?.liquidationsMTD) ? cashFlowDetailsData[0].liquidationsMTD : ''}"}, "Year to Date": {"fieldBinding": "${!isEmpty(cashFlowDetailsData[0]?.liquidationsYTD) ? cashFlowDetailsData[0].liquidationsYTD : ''}"}}, {"Description": "Partnership Distributions", "Month to Date": {"fieldBinding": "${!isEmpty(cashFlowDetailsData[0]?.partnershipDistributionsMTD) ? cashFlowDetailsData[0].partnershipDistributionsMTD : ''}"}, "Year to Date": {"fieldBinding": "${!isEmpty(cashFlowDetailsData[0]?.partnershipDistributionsYTD) ? cashFlowDetailsData[0].partnershipDistributionsYTD : ''}"}}, {"Description": "Principal Payments", "Month to Date": {"fieldBinding": "${!isEmpty(cashFlowDetailsData[0]?.principalPaymentsMTD) ? cashFlowDetailsData[0].principalPaymentsMTD : ''}"}, "Year to Date": {"fieldBinding": "${!isEmpty(cashFlowDetailsData[0]?.principalPaymentsYTD) ? cashFlowDetailsData[0].principalPaymentsYTD : ''}"}}, {"Description": "Substitute Payments", "Month to Date": {"fieldBinding": "${!isEmpty(cashFlowDetailsData[0]?.substitutePaymentsMTD) ? cashFlowDetailsData[0].substitutePaymentsMTD : ''}"}, "Year to Date": {"fieldBinding": "${!isEmpty(cashFlowDetailsData[0]?.substitutePaymentsYTD) ? cashFlowDetailsData[0].substitutePaymentsYTD : ''}"}}, {"Description": "Royal Payments", "Month to Date": {"fieldBinding": "${!isEmpty(cashFlowDetailsData[0]?.royaltyPaymentsMTD) ? cashFlowDetailsData[0].royaltyPaymentsMTD : ''}"}, "Year to Date": {"fieldBinding": "${!isEmpty(cashFlowDetailsData[0]?.royaltyPaymentsYTD) ? cashFlowDetailsData[0].royaltyPaymentsYTD : ''}"}}, {"Description": "Municipal Interest (tax)", "Month to Date": {"fieldBinding": "${!isEmpty(cashFlowDetailsData[0]?.municipalInterestTaxMTD) ? cashFlowDetailsData[0].municipalInterestTaxMTD : ''}"}, "Year to Date": {"fieldBinding": "${!isEmpty(cashFlowDetailsData[0]?.municipalInterestTaxYTD) ? cashFlowDetailsData[0].municipalInterestTaxYTD : ''}"}}, {"Description": "Corporate Interest", "Month to Date": {"fieldBinding": "${!isEmpty(cashFlowDetailsData[0]?.corporateInterestMTD) ? cashFlowDetailsData[0].corporateInterestMTD : ''}"}, "Year to Date": {"fieldBinding": "${!isEmpty(cashFlowDetailsData[0]?.corporateInterestYTD) ? cashFlowDetailsData[0].corporateInterestYTD : ''}"}}, {"Description": "Government Interest", "Month to Date": {"fieldBinding": "${!isEmpty(cashFlowDetailsData[0]?.governmentInterestMTD) ? cashFlowDetailsData[0].governmentInterestMTD : ''}"}, "Year to Date": {"fieldBinding": "${!isEmpty(cashFlowDetailsData[0]?.governmentInterestYTD) ? cashFlowDetailsData[0].governmentInterestYTD : ''}"}}, {"Description": "Accrued Interest Purchase", "Month to Date": {"fieldBinding": "${!isEmpty(cashFlowDetailsData[0]?.accruedInterestPurchaseMTD) ? cashFlowDetailsData[0].accruedInterestPurchaseMTD : ''}"}, "Year to Date": {"fieldBinding": "${!isEmpty(cashFlowDetailsData[0]?.accruedInterestPurchaseYTD) ? cashFlowDetailsData[0].accruedInterestPurchaseYTD : ''}"}}, {"Description": "Accrued Interest Sales", "Month to Date": {"fieldBinding": "${!isEmpty(cashFlowDetailsData[0]?.accruedInterestSalesMTD) ? cashFlowDetailsData[0].accruedInterestSalesMTD : ''}"}, "Year to Date": {"fieldBinding": "${!isEmpty(cashFlowDetailsData[0]?.accruedInterestSalesYTD) ? cashFlowDetailsData[0].accruedInterestSalesYTD : ''}"}}, {"Description": "Money Market", "Month to Date": {"fieldBinding": "${!isEmpty(cashFlowDetailsData[0]?.moneyMarketMTD) ? cashFlowDetailsData[0].moneyMarketMTD : ''}"}, "Year to Date": {"fieldBinding": "${!isEmpty(cashFlowDetailsData[0]?.moneyMarketYTD) ? cashFlowDetailsData[0].moneyMarketYTD : ''}"}}, {"Description": "Credit Interest", "Month to Date": {"fieldBinding": "${!isEmpty(cashFlowDetailsData[0]?.creditInterestMTD) ? cashFlowDetailsData[0].creditInterestMTD : ''}"}, "Year to Date": {"fieldBinding": "${!isEmpty(cashFlowDetailsData[0]?.creditInterestYTD) ? cashFlowDetailsData[0].creditInterestYTD : ''}"}}, {"Description": "Repurchase Interest", "Month to Date": {"fieldBinding": "${!isEmpty(cashFlowDetailsData[0]?.repurchaseInterestMTD) ? cashFlowDetailsData[0].repurchaseInterestMTD : ''}"}, "Year to Date": {"fieldBinding": "${!isEmpty(cashFlowDetailsData[0]?.repurchaseInterestYTD) ? cashFlowDetailsData[0].repurchaseInterestYTD : ''}"}}, {"Description": "Other Income", "Month to Date": {"fieldBinding": "${!isEmpty(cashFlowDetailsData[0]?.otherIncomeMTD) ? cashFlowDetailsData[0].otherIncomeMTD : ''}"}, "Year to Date": {"fieldBinding": "${!isEmpty(cashFlowDetailsData[0]?.otherIncomeYTD) ? cashFlowDetailsData[0].otherIncomeYTD : ''}"}}, {"Description": "Total", "Month to Date": {"fieldBinding": "${!isEmpty(cashFlowDetailsData[0]?.totalIncomeMTD) ? cashFlowDetailsData[0].totalIncomeMTD : ''}"}, "Year to Date": {"fieldBinding": "${!isEmpty(cashFlowDetailsData[0]?.totalIncomeYTD) ? cashFlowDetailsData[0].totalIncomeYTD : ''}"}}]}]}, {"id": "SingleAccountPositionDetails", "title": "Single Account Position Details", "url": "/single_account_holdings", "parameters": [{"name": "actnum", "value": "${Account Number}"}], "description": "Single Account Position or Holdings details", "endpoints": [{"description": "Get prior day positions list for a given account id or get positions for a given account id.Prerequisite : Get account id first from the endpoint - Get account id and details for a given account number or account name", "url": "/api/domain/wealthdomain/position/dynamic/query/positionsByAsOfDateForAccount", "base_url": "http://app-data-manager:8002", "method": "get", "query_params": {"_page": "0", "_size": "${10}", "accountId": "${accountId}", "asOfDate": "2025-05-27"}, "path_params": {}, "requestBody": {}, "responseSchema": {"type": "array", "items": {"type": "object", "properties": {"account": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string"}}}, "accountTypeCode": {"type": "string"}, "annuityProvider": {"type": "string"}, "asOfDate": {"type": "string"}, "blendedUnitCost": {"type": "number"}, "contractValue": {"type": "number"}, "costBasis": {"type": "number"}, "dividendRate": {"type": "number"}, "duration": {"type": "string"}, "id": {"type": "string"}, "isMarginable": {"type": "boolean"}, "location": {"type": "string"}, "marketPrice": {"type": "number"}, "marketValue": {"type": "number"}, "maturityDate": {"type": "string"}, "optionType": {"type": "string"}, "parValue": {"type": "number"}, "purchaseDate": {"type": "string"}, "purchasePrice": {"type": "number"}, "quantity": {"type": "number"}, "security": {"type": "object", "properties": {"callIndicator": {"type": "string"}, "couponRate": {"type": "number"}, "cusip": {"type": "string"}, "custodianAssignedID": {"type": "string"}, "dividendYield": {"type": "number"}, "expenseRatio": {"type": "number"}, "id": {"type": "string"}, "isin": {"type": "string"}, "lastPrice": {"type": "number"}, "optionExpiryDate": {"type": "string"}, "securityCategory": {"type": "string"}, "securityDescription": {"type": "string"}, "securityType": {"type": "object", "properties": {"category": {"type": "string"}, "id": {"type": "string"}}}, "sedol": {"type": "string"}, "strikePrice": {"type": "number"}, "symbol": {"type": "string"}, "valoren": {"type": "string"}}}, "securityCategory": {"type": "string"}, "settlementDateQuantity": {"type": "number"}, "term": {"type": "string"}, "totalPremium": {"type": "number"}, "underlyingSymbol": {"type": "string"}, "underlyingSymbolPrice": {"type": "number"}, "unitCost": {"type": "number"}, "unrealizedGainLoss": {"type": "number"}, "yieldToCall": {"type": "number"}, "yieldToMaturity": {"type": "number"}}}}, "resultVariable": "accountPositionDetailsData", "references": {"tables": [{"name": "Prior Day Account Position Details", "description": "Prior Day Account Position Details Table", "columns": [{"name": "Description", "type": "string", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].security.securityDescription)? accountPositionDetailsData[i].security.securityDescription : \" \"}"}, {"name": "Sec ID", "type": "string", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].security.custodianAssignedID)? accountPositionDetailsData[i].security.custodianAssignedID : \" \"}"}, {"name": "Ticker", "type": "string", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].security.symbol)? accountPositionDetailsData[i].security.symbol : \" \"}"}, {"name": "CUSIP", "type": "string", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].security.cusip)? accountPositionDetailsData[i].security.cusip : \" \"}"}, {"name": "Underlying Symbol", "type": "string", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].underlyingSymbol)? accountPositionDetailsData[i].underlyingSymbol : \" \"}"}, {"name": "Security Type", "type": "string", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].security.securityType.category)? accountPositionDetailsData[i].security.securityType.category : \" \"}"}, {"name": "Account Type", "type": "string", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].accountTypeCode)? accountPositionDetailsData[i].accountTypeCode : \" \"}"}, {"name": "TD Quantity", "type": "number", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].quantity)? accountPositionDetailsData[i].quantity : \" \"}"}, {"name": "SD Quantity", "type": "number", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].settlementDateQuantity)? accountPositionDetailsData[i].settlementDateQuantity : \" \"}"}, {"name": "Market Value", "type": "currency", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].marketValue)? accountPositionDetailsData[i].marketValue : \" \"}"}, {"name": "<PERSON><PERSON>", "type": "currency", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].costBasis)? accountPositionDetailsData[i].costBasis : \" \"}"}, {"name": "Term", "type": "string", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].term)? accountPositionDetailsData[i].term : \" \"}"}, {"name": "Purchase Date", "type": "string", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].purchaseDate)? accountPositionDetailsData[i].purchaseDate : \" \"}"}, {"name": "Maturity Date", "type": "string", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].maturityDate)? accountPositionDetailsData[i].maturityDate : \" \"}"}, {"name": "Price Date", "type": "string", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].asOfDate)? accountPositionDetailsData[i].asOfDate : \" \"}"}, {"name": "Account Type", "type": "string", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].account.type)? accountPositionDetailsData[i].account.type : \" \"}"}, {"name": "Type (Call/Put)", "type": "string", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].callOrPut)? accountPositionDetailsData[i].callOrPut : \" \"}"}, {"name": "Option Type", "type": "string", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].security.callIndicator)? accountPositionDetailsData[i].security.callIndicator : \" \"}"}, {"name": "Expiry Date", "type": "string", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].security.optionExpiryDate)? accountPositionDetailsData[i].security.optionExpiryDate : \" \"}"}, {"name": "Strike Price", "type": "currency", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].security.strikePrice)? accountPositionDetailsData[i].security.strikePrice : \" \"}"}, {"name": "Unit Cost", "type": "currency", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].unitCost)? accountPositionDetailsData[i].unitCost : \" \"}"}, {"name": "Blended Unit Cost", "type": "currency", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].blendedUnitCost)? accountPositionDetailsData[i].blendedUnitCost : \" \"}"}, {"name": "Purchase Price", "type": "currency", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].purchasePrice)? accountPositionDetailsData[i].purchasePrice : \" \"}"}, {"name": "Price", "type": "currency", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].marketPrice)? accountPositionDetailsData[i].marketPrice : \" \"}"}, {"name": "Underlying Symbol Price", "type": "currency", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].underlyingSymbolPrice)? accountPositionDetailsData[i].underlyingSymbolPrice : \" \"}"}, {"name": "Unrealized Gain / Loss", "type": "currency", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].unrealizedGainLoss)? accountPositionDetailsData[i].unrealizedGainLoss : \" \"}"}, {"name": "Premium(Amount Paid)", "type": "currency", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].totalPremium)? accountPositionDetailsData[i].totalPremium : \" \"}"}, {"name": "Contract Value(Current Value)", "type": "currency", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].contractValue)? accountPositionDetailsData[i].contractValue : \" \"}"}, {"name": "Exp Ratio", "type": "number", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].security.expenseRatio)? accountPositionDetailsData[i].security.expenseRatio : \" \"}"}, {"name": "Dividend Yield", "type": "number", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].security.dividendYield)? accountPositionDetailsData[i].security.dividendYield : \" \"}"}, {"name": "ISIN", "type": "string", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].security.isin)? accountPositionDetailsData[i].security.isin : \" \"}"}, {"name": "SEDOL", "type": "string", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].security.sedol)? accountPositionDetailsData[i].security.sedol : \" \"}"}, {"name": "Contract Number", "type": "string", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].contractNumber)? accountPositionDetailsData[i].contractNumber : \" \"}"}, {"name": "Asset Category", "type": "string", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].security.assetClass)? accountPositionDetailsData[i].security.assetClass : \" \"}"}, {"name": "Is Marginable", "type": "string", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].isMarginable)? accountPositionDetailsData[i].isMarginable : \" \"}"}, {"name": "Location", "type": "string", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].location)? accountPositionDetailsData[i].location : \" \"}"}, {"name": "Annuity Provider", "type": "string", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].annuityProvider)? accountPositionDetailsData[i].annuityProvider : \" \"}"}, {"name": "Dividend Rate", "type": "number", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].dividendRate)? accountPositionDetailsData[i].dividendRate : \" \"}"}, {"name": "Coupon Rate", "type": "number", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].security.couponRate)? accountPositionDetailsData[i].security.couponRate : \" \"}"}]}]}, "id": "a5de1ab9-6ad1-4bc4-895c-e023c2cf5aff"}, {"description": "Get intra day positions list for a given account id.Prerequisite : Get account id first from the endpoint - Get account id and details for a given account number or account name", "url": "/workflow/v1/instances/execute/sync/domain/wealthdomain/crt2pt948trtqoiscl4g", "base_url": "http://workhorse:8080", "method": "post", "query_params": {}, "path_params": {}, "requestBody": {"arguments": {"accountId": "${accountId}", "pageStart": "0", "pageSize": "${10}", "custodianType": "${gorg}"}}, "responseSchema": {"type": "array", "items": {"type": "object", "properties": {"accountTypeCode": {"type": "string", "description": "Type of account holding the security (e.g., CASH, MARGIN)."}, "intraDayPriceChange": {"type": "number", "description": "Change in price during the current trading day."}, "quantity": {"type": "integer", "description": "Number of units of the security held."}, "marketPrice": {"type": "number", "description": "Current market price of the security."}, "unrealizedGainLoss": {"type": "number", "description": "Unrealized gain or loss on the security."}, "costBasis": {"type": "number", "description": "The original cost of the security for tax purposes."}, "marketValue": {"type": "number", "description": "Current market value of the security holding (quantity * marketPrice)."}, "security": {"type": "object", "properties": {"priorClosePrice": {"type": "number", "description": "The closing price of the security on the prior trading day."}, "cusip": {"type": "string", "description": "CUSIP (Committee on Uniform Security Identification Procedures) identifier for the security."}, "symbol": {"type": "string", "description": "Stock ticker symbol for the security."}, "securityDescription": {"type": "string", "description": "Description or full name of the security."}, "custodianAssignedID": {"type": "integer", "description": "An ID assigned by the custodian for the security."}}}, "intraDayTimestamp": {"type": "string", "format": "date-time", "description": "Timestamp indicating when the intraday data was last updated."}}}}, "resultVariable": "intraDayAccountPositionDetailsData", "references": {"tables": [{"name": "Intra Day Account Position Details", "description": "Intra Day Account Position Details Table", "dataSource": "${intraDayAccountPositionDetailsData}", "columns": [{"name": "Symbol", "type": "string", "fieldBinding": "${!isEmpty(intraDayAccountPositionDetailsData[i].security.symbol)? intraDayAccountPositionDetailsData[i].security.symbol : \" \"}"}, {"name": "Quantity", "type": "number", "fieldBinding": "${!isEmpty(intraDayAccountPositionDetailsData[i].quantity)? intraDayAccountPositionDetailsData[i].quantity : \" \"}"}, {"name": "Market Value", "type": "currency", "fieldBinding": "${!isEmpty(intraDayAccountPositionDetailsData[i].marketValue)? intraDayAccountPositionDetailsData[i].marketValue : \" \"}"}, {"name": "CUSIP", "type": "string", "fieldBinding": "${!isEmpty(intraDayAccountPositionDetailsData[i].security.cusip)? intraDayAccountPositionDetailsData[i].security.cusip : \" \"}"}, {"name": "Custodian Assigned ID", "type": "string", "fieldBinding": "${!isEmpty(intraDayAccountPositionDetailsData[i].security.custodianAssignedID)? intraDayAccountPositionDetailsData[i].security.custodianAssignedID : \" \"}"}, {"name": "Security Description", "type": "string", "fieldBinding": "${!isEmpty(intraDayAccountPositionDetailsData[i].security.securityDescription)? intraDayAccountPositionDetailsData[i].security.securityDescription : \" \"}"}, {"name": "Account Type", "type": "string", "fieldBinding": "${!isEmpty(intraDayAccountPositionDetailsData[i].accountTypeCode)? intraDayAccountPositionDetailsData[i].accountTypeCode : \" \"}"}, {"name": "Market Price", "type": "number", "fieldBinding": "${!isEmpty(intraDayAccountPositionDetailsData[i].marketPrice)? intraDayAccountPositionDetailsData[i].marketPrice : \" \"}"}, {"name": "<PERSON><PERSON>", "type": "currency", "fieldBinding": "${!isEmpty(intraDayAccountPositionDetailsData[i].costBasis)? intraDayAccountPositionDetailsData[i].costBasis : \" \"}"}, {"name": "Unrealized Gain / Loss", "type": "currency", "fieldBinding": "${!isEmpty(intraDayAccountPositionDetailsData[i].unrealizedGainLoss)? intraDayAccountPositionDetailsData[i].unrealizedGainLoss : \" \"}"}, {"name": "Day Price Change", "type": "number", "fieldBinding": "${!isEmpty(intraDayAccountPositionDetailsData[i].intraDayPriceChang)? intraDayAccountPositionDetailsData[i].intraDayPriceChang : \" \"}"}]}]}, "id": "be0c1ed6-b216-4920-b806-717e68fd075d"}], "tables": [{"name": "Prior Day Account Position Details", "description": "Prior Day Account Position Details Table", "columns": [{"name": "Description", "type": "string", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].security.securityDescription)? accountPositionDetailsData[i].security.securityDescription : \" \"}"}, {"name": "Sec ID", "type": "string", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].security.custodianAssignedID)? accountPositionDetailsData[i].security.custodianAssignedID : \" \"}"}, {"name": "Ticker", "type": "string", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].security.symbol)? accountPositionDetailsData[i].security.symbol : \" \"}"}, {"name": "CUSIP", "type": "string", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].security.cusip)? accountPositionDetailsData[i].security.cusip : \" \"}"}, {"name": "Underlying Symbol", "type": "string", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].underlyingSymbol)? accountPositionDetailsData[i].underlyingSymbol : \" \"}"}, {"name": "Security Type", "type": "string", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].security.securityType.category)? accountPositionDetailsData[i].security.securityType.category : \" \"}"}, {"name": "Account Type", "type": "string", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].accountTypeCode)? accountPositionDetailsData[i].accountTypeCode : \" \"}"}, {"name": "TD Quantity", "type": "number", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].quantity)? accountPositionDetailsData[i].quantity : \" \"}"}, {"name": "SD Quantity", "type": "number", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].settlementDateQuantity)? accountPositionDetailsData[i].settlementDateQuantity : \" \"}"}, {"name": "Market Value", "type": "currency", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].marketValue)? accountPositionDetailsData[i].marketValue : \" \"}"}, {"name": "<PERSON><PERSON>", "type": "currency", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].costBasis)? accountPositionDetailsData[i].costBasis : \" \"}"}, {"name": "Term", "type": "string", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].term)? accountPositionDetailsData[i].term : \" \"}"}, {"name": "Purchase Date", "type": "string", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].purchaseDate)? accountPositionDetailsData[i].purchaseDate : \" \"}"}, {"name": "Maturity Date", "type": "string", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].maturityDate)? accountPositionDetailsData[i].maturityDate : \" \"}"}, {"name": "Price Date", "type": "string", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].asOfDate)? accountPositionDetailsData[i].asOfDate : \" \"}"}, {"name": "Account Type", "type": "string", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].account.type)? accountPositionDetailsData[i].account.type : \" \"}"}, {"name": "Type (Call/Put)", "type": "string", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].callOrPut)? accountPositionDetailsData[i].callOrPut : \" \"}"}, {"name": "Option Type", "type": "string", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].security.callIndicator)? accountPositionDetailsData[i].security.callIndicator : \" \"}"}, {"name": "Expiry Date", "type": "string", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].security.optionExpiryDate)? accountPositionDetailsData[i].security.optionExpiryDate : \" \"}"}, {"name": "Strike Price", "type": "currency", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].security.strikePrice)? accountPositionDetailsData[i].security.strikePrice : \" \"}"}, {"name": "Unit Cost", "type": "currency", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].unitCost)? accountPositionDetailsData[i].unitCost : \" \"}"}, {"name": "Blended Unit Cost", "type": "currency", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].blendedUnitCost)? accountPositionDetailsData[i].blendedUnitCost : \" \"}"}, {"name": "Purchase Price", "type": "currency", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].purchasePrice)? accountPositionDetailsData[i].purchasePrice : \" \"}"}, {"name": "Price", "type": "currency", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].marketPrice)? accountPositionDetailsData[i].marketPrice : \" \"}"}, {"name": "Underlying Symbol Price", "type": "currency", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].underlyingSymbolPrice)? accountPositionDetailsData[i].underlyingSymbolPrice : \" \"}"}, {"name": "Unrealized Gain / Loss", "type": "currency", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].unrealizedGainLoss)? accountPositionDetailsData[i].unrealizedGainLoss : \" \"}"}, {"name": "Premium(Amount Paid)", "type": "currency", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].totalPremium)? accountPositionDetailsData[i].totalPremium : \" \"}"}, {"name": "Contract Value(Current Value)", "type": "currency", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].contractValue)? accountPositionDetailsData[i].contractValue : \" \"}"}, {"name": "Exp Ratio", "type": "number", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].security.expenseRatio)? accountPositionDetailsData[i].security.expenseRatio : \" \"}"}, {"name": "Dividend Yield", "type": "number", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].security.dividendYield)? accountPositionDetailsData[i].security.dividendYield : \" \"}"}, {"name": "ISIN", "type": "string", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].security.isin)? accountPositionDetailsData[i].security.isin : \" \"}"}, {"name": "SEDOL", "type": "string", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].security.sedol)? accountPositionDetailsData[i].security.sedol : \" \"}"}, {"name": "Contract Number", "type": "string", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].contractNumber)? accountPositionDetailsData[i].contractNumber : \" \"}"}, {"name": "Asset Category", "type": "string", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].security.assetClass)? accountPositionDetailsData[i].security.assetClass : \" \"}"}, {"name": "Is Marginable", "type": "string", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].isMarginable)? accountPositionDetailsData[i].isMarginable : \" \"}"}, {"name": "Location", "type": "string", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].location)? accountPositionDetailsData[i].location : \" \"}"}, {"name": "Annuity Provider", "type": "string", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].annuityProvider)? accountPositionDetailsData[i].annuityProvider : \" \"}"}, {"name": "Dividend Rate", "type": "number", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].dividendRate)? accountPositionDetailsData[i].dividendRate : \" \"}"}, {"name": "Coupon Rate", "type": "number", "fieldBinding": "${!isEmpty(accountPositionDetailsData[i].security.couponRate)? accountPositionDetailsData[i].security.couponRate : \" \"}"}]}, {"name": "Intra Day Account Position Details", "description": "Intra Day Account Position Details Table", "dataSource": "${intraDayAccountPositionDetailsData}", "columns": [{"name": "Symbol", "type": "string", "fieldBinding": "${!isEmpty(intraDayAccountPositionDetailsData[i].security.symbol)? intraDayAccountPositionDetailsData[i].security.symbol : \" \"}"}, {"name": "Quantity", "type": "number", "fieldBinding": "${!isEmpty(intraDayAccountPositionDetailsData[i].quantity)? intraDayAccountPositionDetailsData[i].quantity : \" \"}"}, {"name": "Market Value", "type": "currency", "fieldBinding": "${!isEmpty(intraDayAccountPositionDetailsData[i].marketValue)? intraDayAccountPositionDetailsData[i].marketValue : \" \"}"}, {"name": "CUSIP", "type": "string", "fieldBinding": "${!isEmpty(intraDayAccountPositionDetailsData[i].security.cusip)? intraDayAccountPositionDetailsData[i].security.cusip : \" \"}"}, {"name": "Custodian Assigned ID", "type": "string", "fieldBinding": "${!isEmpty(intraDayAccountPositionDetailsData[i].security.custodianAssignedID)? intraDayAccountPositionDetailsData[i].security.custodianAssignedID : \" \"}"}, {"name": "Security Description", "type": "string", "fieldBinding": "${!isEmpty(intraDayAccountPositionDetailsData[i].security.securityDescription)? intraDayAccountPositionDetailsData[i].security.securityDescription : \" \"}"}, {"name": "Account Type", "type": "string", "fieldBinding": "${!isEmpty(intraDayAccountPositionDetailsData[i].accountTypeCode)? intraDayAccountPositionDetailsData[i].accountTypeCode : \" \"}"}, {"name": "Market Price", "type": "number", "fieldBinding": "${!isEmpty(intraDayAccountPositionDetailsData[i].marketPrice)? intraDayAccountPositionDetailsData[i].marketPrice : \" \"}"}, {"name": "<PERSON><PERSON>", "type": "currency", "fieldBinding": "${!isEmpty(intraDayAccountPositionDetailsData[i].costBasis)? intraDayAccountPositionDetailsData[i].costBasis : \" \"}"}, {"name": "Unrealized Gain / Loss", "type": "currency", "fieldBinding": "${!isEmpty(intraDayAccountPositionDetailsData[i].unrealizedGainLoss)? intraDayAccountPositionDetailsData[i].unrealizedGainLoss : \" \"}"}, {"name": "Day Price Change", "type": "number", "fieldBinding": "${!isEmpty(intraDayAccountPositionDetailsData[i].intraDayPriceChang)? intraDayAccountPositionDetailsData[i].intraDayPriceChang : \" \"}"}]}]}, {"id": "SingleAccountPositionTaxLotsOpen", "title": "Single Account Position Tax Lots Open", "url": "/single_account_holdings", "parameters": [{"name": "actnum", "value": "${Account Number}"}], "description": "Single Account Position or Holdings - Tax Lots Open", "endpoints": [{"description": "Get the list of open tax lots for the given account id.Prerequisite : Get account id first from the endpoint - Get account id and details for a given account number or account name", "url": "/api/domain/wealthdomain/taxLot/dynamic/query/taxLotsByClosedStatusForAccount", "base_url": "http://app-data-manager:8002", "method": "get", "query_params": {"_page": "0", "_size": "${10}", "accountId": "${accountId}", "isClosed": "false"}, "path_params": {}, "requestBody": {}, "responseSchema": {"type": "array", "items": {"type": "object", "properties": {"blendedUnitCost": {"type": "number"}, "held": {"type": "string"}, "id": {"type": "string"}, "isClosed": {"type": "boolean"}, "isVoid": {"type": "boolean"}, "longTermRealizedGainOrLoss": {"type": "number"}, "longTermUnrealizedGainOrLoss": {"type": "number"}, "marketPrice": {"type": "number"}, "marketValue": {"type": "number"}, "openDate": {"type": "string"}, "quantity": {"type": "number"}, "security": {"type": "object", "properties": {"cusip": {"type": "string"}, "custodianAssignedID": {"type": "string"}, "endOfMonthPrice": {"type": "number"}, "id": {"type": "string"}, "securityDescription": {"type": "string"}, "strikePrice": {"type": "number"}, "symbol": {"type": "string"}, "underlyingSecurityNumber": {"type": "string"}}}, "shortTermRealizedGainOrLoss": {"type": "number"}, "shortTermUnrealizedGainOrLoss": {"type": "number"}, "term": {"type": "string"}, "totalCost": {"type": "number"}, "totalRealizedGainOrLoss": {"type": "number"}, "totalUnrealizedGainOrLoss": {"type": "number"}}}}, "resultVariable": "accountPositionTaxLotsData", "id": "572939d8-2c94-487b-ba43-5afc5435e0f8"}], "tables": [{"name": "Account Position Tax Lots", "description": "Account Position Tax Lots Table", "columns": [{"name": "Description", "type": "string", "fieldBinding": "${!isEmpty(accountPositionTaxLotsData[i].security.securityDescription)? accountPositionTaxLotsData[i].security.securityDescription : \" \"}"}, {"name": "Symbol", "type": "string", "fieldBinding": "${!isEmpty(accountPositionTaxLotsData[i].security.symbol)? accountPositionTaxLotsData[i].security.symbol : \" \"}"}, {"name": "Security Id", "type": "string", "fieldBinding": "${!isEmpty(accountPositionTaxLotsData[i].security.custodianAssignedID)? accountPositionTaxLotsData[i].security.custodianAssignedID : \" \"}"}, {"name": "CUSIP", "type": "string", "fieldBinding": "${!isEmpty(accountPositionTaxLotsData[i].security.cusip)? accountPositionTaxLotsData[i].security.cusip : \" \"}"}, {"name": "Quantity", "type": "number", "fieldBinding": "${!isEmpty(accountPositionTaxLotsData[i].quantity)? accountPositionTaxLotsData[i].quantity : \" \"}"}, {"name": "Open Date", "type": "string", "fieldBinding": "${!isEmpty(accountPositionTaxLotsData[i].openDate)? accountPositionTaxLotsData[i].openDate : \" \"}"}, {"name": "Market Price", "type": "number", "fieldBinding": "${!isEmpty(accountPositionTaxLotsData[i].marketPrice)? accountPositionTaxLotsData[i].marketPrice : \" \"}"}, {"name": "Blended Unit Cost", "type": "number", "fieldBinding": "${!isEmpty(accountPositionTaxLotsData[i].blendedUnitCost)? accountPositionTaxLotsData[i].blendedUnitCost : \" \"}"}, {"name": "Cost Amount", "type": "number", "fieldBinding": "${!isEmpty(accountPositionTaxLotsData[i].totalCost)? accountPositionTaxLotsData[i].totalCost : \" \"}"}, {"name": "Unrealized Gain / Loss", "type": "number", "fieldBinding": "${!isEmpty(accountPositionTaxLotsData[i].totalUnrealizedGainOrLoss)? accountPositionTaxLotsData[i].totalUnrealizedGainOrLoss : \" \"}"}, {"name": "Unrealized Gain/Loss (Short)", "type": "number", "fieldBinding": "${!isEmpty(accountPositionTaxLotsData[i].shortTermUnrealizedGainOrLoss)? accountPositionTaxLotsData[i].shortTermUnrealizedGainOrLoss : \" \"}"}, {"name": "Unrealized Gain/Loss (Long)", "type": "number", "fieldBinding": "${!isEmpty(accountPositionTaxLotsData[i].longTermUnrealizedGainOrLoss)? accountPositionTaxLotsData[i].longTermUnrealizedGainOrLoss : \" \"}"}, {"name": "Total Unrealized Gain/Loss", "type": "number", "fieldBinding": "${!isEmpty(accountPositionTaxLotsData[i].totalUnrealizedGainOrLoss)? accountPositionTaxLotsData[i].totalUnrealizedGainOrLoss : \" \"}"}, {"name": "Term", "type": "string", "fieldBinding": "${!isEmpty(accountPositionTaxLotsData[i].term)? accountPositionTaxLotsData[i].term : \" \"}"}, {"name": "Held", "type": "string", "fieldBinding": "${!isEmpty(accountPositionTaxLotsData[i].held)? accountPositionTaxLotsData[i].held : \" \"}"}]}]}, {"id": "SingleAccountPositionTaxLotsClosed", "title": "Single Account Position Tax Lots Closed", "url": "/single_account_holdings", "parameters": [{"name": "actnum", "value": "${Account Number}"}], "description": "Single Account Position or Holdings - Tax Lots Closed", "endpoints": [{"description": "Get the list of closed tax lots for the given account id.Prerequisite : Get account id first from the endpoint - Get account id and details for a given account number or account name", "url": "/api/domain/wealthdomain/taxLot/dynamic/query/taxLotsByClosedStatusForAccount", "base_url": "http://app-data-manager:8002", "method": "get", "query_params": {"_page": "0", "_size": "${10}", "accountId": "${accountId}", "isClosed": "true"}, "path_params": {}, "requestBody": {}, "responseSchema": {"type": "array", "items": {"type": "object", "properties": {"blendedUnitCost": {"type": "number"}, "held": {"type": "string"}, "id": {"type": "string"}, "isClosed": {"type": "boolean"}, "isVoid": {"type": "boolean"}, "longTermRealizedGainOrLoss": {"type": "number"}, "longTermUnrealizedGainOrLoss": {"type": "number"}, "marketPrice": {"type": "number"}, "marketValue": {"type": "number"}, "openDate": {"type": "string"}, "quantity": {"type": "number"}, "security": {"type": "object", "properties": {"cusip": {"type": "string"}, "custodianAssignedID": {"type": "string"}, "endOfMonthPrice": {"type": "number"}, "id": {"type": "string"}, "securityDescription": {"type": "string"}, "strikePrice": {"type": "number"}, "symbol": {"type": "string"}, "underlyingSecurityNumber": {"type": "string"}}}, "shortTermRealizedGainOrLoss": {"type": "number"}, "shortTermUnrealizedGainOrLoss": {"type": "number"}, "term": {"type": "string"}, "totalCost": {"type": "number"}, "totalRealizedGainOrLoss": {"type": "number"}, "totalUnrealizedGainOrLoss": {"type": "number"}}}}, "resultVariable": "accountPositionTaxLotsClosedData", "id": "6064cf63-198c-40fc-b8fa-823c7f2d1be0"}], "tables": [{"name": "Account Position Tax Lots", "description": "Account Position Tax Lots Table", "columns": [{"name": "Description", "type": "string", "fieldBinding": "${!isEmpty(accountPositionTaxLotsClosedData[i].security.securityDescription)? accountPositionTaxLotsClosedData[i].security.securityDescription : \" \"}"}, {"name": "Symbol", "type": "string", "fieldBinding": "${!isEmpty(accountPositionTaxLotsClosedData[i].security.symbol)? accountPositionTaxLotsClosedData[i].security.symbol : \" \"}"}, {"name": "Security Id", "type": "string", "fieldBinding": "${!isEmpty(accountPositionTaxLotsClosedData[i].security.custodianAssignedID)? accountPositionTaxLotsClosedData[i].security.custodianAssignedID : \" \"}"}, {"name": "CUSIP", "type": "string", "fieldBinding": "${!isEmpty(accountPositionTaxLotsClosedData[i].security.cusip)? accountPositionTaxLotsClosedData[i].security.cusip : \" \"}"}, {"name": "Quantity", "type": "number", "fieldBinding": "${!isEmpty(accountPositionTaxLotsClosedData[i].quantity)? accountPositionTaxLotsClosedData[i].quantity : \" \"}"}, {"name": "Open Date", "type": "string", "fieldBinding": "${!isEmpty(accountPositionTaxLotsClosedData[i].openDate)? accountPositionTaxLotsClosedData[i].openDate : \" \"}"}, {"name": "Market Price", "type": "number", "fieldBinding": "${!isEmpty(accountPositionTaxLotsClosedData[i].marketPrice)? accountPositionTaxLotsClosedData[i].marketPrice : \" \"}"}, {"name": "Blended Unit Cost", "type": "number", "fieldBinding": "${!isEmpty(accountPositionTaxLotsClosedData[i].blendedUnitCost)? accountPositionTaxLotsClosedData[i].blendedUnitCost : \" \"}"}, {"name": "Cost Amount", "type": "number", "fieldBinding": "${!isEmpty(accountPositionTaxLotsClosedData[i].totalCost)? accountPositionTaxLotsClosedData[i].totalCost : \" \"}"}, {"name": "Unrealized Gain / Loss", "type": "number", "fieldBinding": "${!isEmpty(accountPositionTaxLotsClosedData[i].totalUnrealizedGainOrLoss)? accountPositionTaxLotsClosedData[i].totalUnrealizedGainOrLoss : \" \"}"}, {"name": "Unrealized Gain/Loss (Short)", "type": "number", "fieldBinding": "${!isEmpty(accountPositionTaxLotsClosedData[i].shortTermUnrealizedGainOrLoss)? accountPositionTaxLotsClosedData[i].shortTermUnrealizedGainOrLoss : \" \"}"}, {"name": "Unrealized Gain/Loss (Long)", "type": "number", "fieldBinding": "${!isEmpty(accountPositionTaxLotsClosedData[i].longTermUnrealizedGainOrLoss)? accountPositionTaxLotsClosedData[i].longTermUnrealizedGainOrLoss : \" \"}"}, {"name": "Total Unrealized Gain/Loss", "type": "number", "fieldBinding": "${!isEmpty(accountPositionTaxLotsClosedData[i].totalUnrealizedGainOrLoss)? accountPositionTaxLotsClosedData[i].totalUnrealizedGainOrLoss : \" \"}"}, {"name": "Term", "type": "string", "fieldBinding": "${!isEmpty(accountPositionTaxLotsClosedData[i].term)? accountPositionTaxLotsClosedData[i].term : \" \"}"}, {"name": "Held", "type": "string", "fieldBinding": "${!isEmpty(accountPositionTaxLotsClosedData[i].held)? accountPositionTaxLotsClosedData[i].held : \" \"}"}]}]}, {"id": "SingleAccountPositionFixedIncomeAnalytics", "title": "Single Account Position Fixed Income Analytics", "url": "/single_account_holdings", "parameters": [{"name": "actnum", "value": "${Account Number}"}], "description": "Single Account Position or Holdings - Fixed Income Analytics", "endpoints": [{"description": "Get the list of position fixed income analytics for the account by given account ID.Prerequisite : Get account id first from the endpoint - Get account id and details for a given account number or account name", "url": "/api/domain/wealthdomain/getPositionsByAccountIdSecurityCategoryAndAsOfDate", "base_url": "http://app-data-manager:8002", "method": "get", "query_params": {"securityCategory": "Debt", "_page": "0", "_size": "${10}", "_fields": "purchaseDate,underlyingSymbol,couponRate,security.issuer,security.moodysBondRating,security.couponRate,security.isin,security.sedol,security.securityDescription,security.id,security.symbol,security.custodianAssignedID,marketValue,maturityDate,parValue,duration,id,quantity,asOfDate,marketPrice,purchasePrice,unrealizedGainLoss,yieldToMaturity,security.cusip", "accountid": "${accountId}", "asOfDate": "2025-05-27"}, "path_params": {}, "requestBody": {}, "responseSchema": {"type": "array", "items": {"type": "object", "properties": {"security": {"type": "object", "properties": {"securityDescription": {"type": "string"}, "custodianAssignedID": {"type": "string"}, "cusip": {"type": "string"}, "couponRate": {"type": "number"}, "moodysBondRating": {"type": "string"}, "isin": {"type": "string"}, "sedol": {"type": "string"}, "issuer": {"type": "string"}}}, "maturityDate": {"type": "string"}, "purchasePrice": {"type": "number"}, "unrealizedGainLoss": {"type": "number"}, "parValue": {"type": "number"}, "marketPrice": {"type": "number"}, "duration": {"type": "string"}, "yieldToMaturity": {"type": "number"}}}}, "resultVariable": "accountPositionFixedIncomeAnalyticsData", "id": "034c8b17-2972-46df-be58-c84d1b8212d7"}], "tables": [{"name": "Account Position Fixed Income Analytics", "description": "Account Position Fixed Income Analytics Table", "columns": [{"name": "Description", "type": "string", "fieldBinding": "${!isEmpty(accountPositionFixedIncomeAnalyticsData[i].security.securityDescription)? accountPositionFixedIncomeAnalyticsData[i].security.securityDescription : \" \"}"}, {"name": "Sec ID", "type": "string", "fieldBinding": "${!isEmpty(accountPositionFixedIncomeAnalyticsData[i].security.custodianAssignedID)? accountPositionFixedIncomeAnalyticsData[i].security.custodianAssignedID : \" \"}"}, {"name": "CUSIP", "type": "string", "fieldBinding": "${!isEmpty(accountPositionFixedIncomeAnalyticsData[i].security.cusip)? accountPositionFixedIncomeAnalyticsData[i].security.cusip : \" \"}"}, {"name": "Maturity Date", "type": "string", "fieldBinding": "${!isEmpty(accountPositionFixedIncomeAnalyticsData[i].maturityDate)? accountPositionFixedIncomeAnalyticsData[i].maturityDate : \" \"}"}, {"name": "Purchase Price", "type": "currency", "fieldBinding": "${!isEmpty(accountPositionFixedIncomeAnalyticsData[i].purchasePrice)? accountPositionFixedIncomeAnalyticsData[i].purchasePrice : \" \"}"}, {"name": "Unrealized Gain / Loss", "type": "currency", "fieldBinding": "${!isEmpty(accountPositionFixedIncomeAnalyticsData[i].unrealizedGainLoss)? accountPositionFixedIncomeAnalyticsData[i].unrealizedGainLoss : \" \"}"}, {"name": "Par Value", "type": "currency", "fieldBinding": "${!isEmpty(accountPositionFixedIncomeAnalyticsData[i].parValue)? accountPositionFixedIncomeAnalyticsData[i].parValue : \" \"}"}, {"name": "Market Price", "type": "currency", "fieldBinding": "${!isEmpty(accountPositionFixedIncomeAnalyticsData[i].marketPrice)? accountPositionFixedIncomeAnalyticsData[i].marketPrice : \" \"}"}, {"name": "Coupon Rate", "type": "number", "fieldBinding": "${!isEmpty(accountPositionFixedIncomeAnalyticsData[i].security.couponRate)? accountPositionFixedIncomeAnalyticsData[i].security.couponRate : \" \"}"}, {"name": "Duration", "type": "string", "fieldBinding": "${!isEmpty(accountPositionFixedIncomeAnalyticsData[i].duration)? accountPositionFixedIncomeAnalyticsData[i].duration : \" \"}"}, {"name": "Credit Rating", "type": "string", "fieldBinding": "${!isEmpty(accountPositionFixedIncomeAnalyticsData[i].security.moodysBondRating)? accountPositionFixedIncomeAnalyticsData[i].security.moodysBondRating : \" \"}"}, {"name": "ISIN", "type": "string", "fieldBinding": "${!isEmpty(accountPositionFixedIncomeAnalyticsData[i].security.isin)? accountPositionFixedIncomeAnalyticsData[i].security.isin : \" \"}"}, {"name": "SEDOL", "type": "string", "fieldBinding": "${!isEmpty(accountPositionFixedIncomeAnalyticsData[i].security.sedol)? accountPositionFixedIncomeAnalyticsData[i].security.sedol : \" \"}"}, {"name": "Issuer", "type": "string", "fieldBinding": "${!isEmpty(accountPositionFixedIncomeAnalyticsData[i].security.issuer)? accountPositionFixedIncomeAnalyticsData[i].security.issuer : \" \"}"}, {"name": "Yield to Maturity", "type": "number", "fieldBinding": "${!isEmpty(accountPositionFixedIncomeAnalyticsData[i].yieldToMaturity)? accountPositionFixedIncomeAnalyticsData[i].yieldToMaturity : \" \"}"}]}]}, {"id": "AccountOnboarding", "title": "My Tasks", "url": "/Advisor_Queue", "description": "Advisor queue for account onboarding", "endpoints": [{"description": "Get the list of account onboarding service requests (SR)", "url": "/api/domain/wealthdomain/task/dynamic/query/tasksExcludingAccountStatus", "base_url": "http://app-data-manager:8002", "method": "get", "query_params": {"_page": "0", "_size": "${10}", "excludeList": "Onboarding Completed,Rejected", "accountType": "Wealth", "_sort": "-account.lastModifiedAt"}, "path_params": {}, "requestBody": {}, "responseSchema": {"type": "array", "items": {"type": "object", "properties": {"account": {"type": "object", "properties": {"accountNumber": {"type": "string"}, "accountStatus": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "id": {"type": "string", "format": "uuid"}, "lastModifiedAt": {"type": "string", "format": "date-time"}, "primaryOwner": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "owner": {"type": "object", "properties": {"fullDisplayName": {"type": "string"}, "fullName": {"type": "string"}, "id": {"type": "string", "format": "uuid"}}}}}, "registrationType": {"type": "object", "properties": {"code": {"type": "string"}, "id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}}}, "repCodeLink": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "repCode": {"type": "string"}, "repName": {"type": "string"}}}}}, "assignedTo": {"type": "string"}, "id": {"type": "string", "format": "uuid"}, "priorityText": {"type": "string"}, "requestID": {"type": "string"}, "status": {"type": "string"}, "statusMessage": {"type": "string"}}}}, "resultVariable": "tasksListData", "id": "f2321b00-fa5f-45d0-81ba-f0ac335fad6d"}], "tables": [{"name": "Account Onboarding", "description": "Contains accounts onboarded for the advisor", "type": "table", "columns": [{"name": "Request ID", "type": "string", "fieldBinding": "${!isEmpty(tasksListData[i].requestID)? tasksListData[i].requestID : ''}"}, {"name": "Client Name", "type": "string", "fieldBinding": "${!isEmpty(tasksListData[i].account.primaryOwner.owner.fullName)? tasksListData[i].account.primaryOwner.owner.fullName : ''}"}, {"name": "Advisor", "type": "string", "fieldBinding": "${!isEmpty(tasksListData[i].account.repCodeLink.repName)? tasksListData[i].account.repCodeLink.repName : ''}"}, {"name": "Rep Code", "type": "string", "fieldBinding": "${!isEmpty(tasksListData[i].account.repCodeLink.repCode)? tasksListData[i].account.repCodeLink.repCode : ''}"}, {"name": "Registration Type", "type": "string", "fieldBinding": "${!isEmpty(tasksListData[i].account.registrationType.name)? tasksListData[i].account.registrationType.name : ''}"}, {"name": "Account Status", "type": "string", "fieldBinding": "${!isEmpty(tasksListData[i].account.accountStatus)? tasksListData[i].account.accountStatus : ''}"}, {"name": "Account Number", "type": "string", "fieldBinding": "${!isEmpty(tasksListData[i].account.accountNumber)? tasksListData[i].account.accountNumber : ''}"}, {"name": "Created At", "type": "string", "fieldBinding": "${!isEmpty(tasksListData[i].account.createdAt)? tasksListData[i].account.createdAt : ''}"}, {"name": "Last Modified At", "type": "string", "fieldBinding": "${!isEmpty(tasksListData[i].account.lastModifiedAt)? tasksListData[i].account.lastModifiedAt : ''}"}, {"name": "Assigned To", "type": "string", "fieldBinding": "${!isEmpty(tasksListData[i].assignedTo.firstName)? tasksListData[i].assignedTo.firstName : ''}"}, {"name": "Priority", "type": "string", "fieldBinding": "${!isEmpty(tasksListData[i].priorityText)? tasksListData[i].priorityText : ''}"}]}]}, {"id": "AccountServicing", "title": "Servicing", "url": "/sr_list?sr=ws&&fbs=openAdvisor_Queue", "description": "Account servicing requests", "endpoints": [{"description": "Get servicing requests or SR tickets", "url": "/api/domain/wealthdomain/srInstance/dynamic/query/getSRTickets", "base_url": "http://app-data-manager:8002", "method": "get", "query_params": {"_page": "0", "_size": "${10}", "skipSubCategory": "true", "skipBoInstanceID": "true", "skipIncludeList": "true", "skipExcludeList": "false", "includeList": "NA", "excludeList": "Rejected,Closed,Deleted,Expired", "subCategory": "NA", "boInstanceID": "NA"}, "path_params": {}, "requestBody": {}, "responseSchema": {"type": "array", "items": {"type": "object", "properties": {"accountNumber": {"type": "string"}, "boInstanceID": {"type": "string"}, "clientName": {"type": "string"}, "createdAt": {"type": "string"}, "createdBy": {"type": "string"}, "currentTask": {"type": "object", "properties": {"assignedTo": {"type": "object", "properties": {"fullDisplayName": {"type": "string"}}}, "currentStatus": {"type": "object", "properties": {"status": {"type": "object", "properties": {"description": {"type": "string"}, "name": {"type": "string"}}}}}, "id": {"type": "string"}}}, "id": {"type": "string"}, "lastModifiedAt": {"type": "string"}, "srDef": {"type": "object", "properties": {"category": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}}}, "id": {"type": "string"}, "subCategory": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}}}}}, "srId": {"type": "string"}}}}, "resultVariable": "srData", "id": "20577da9-d168-4523-b89c-e994e6711587"}], "tables": [{"name": "Servicing", "description": "Contains servicing requests", "type": "table", "columns": [{"name": "SR #", "type": "string", "fieldBinding": "${!isEmpty(srData[i].srId)? srData[i].srId : ''}"}, {"name": "Category", "type": "string", "fieldBinding": "${!isEmpty(srData[i].srDef.category.name)? srData[i].srDef.category.name : ''}"}, {"name": "Sub Category", "type": "string", "fieldBinding": "${!isEmpty(srData[i].srDef.subCategory.name)? srData[i].srDef.subCategory.name : ''}"}, {"name": "Account Number", "type": "string", "fieldBinding": "${!isEmpty(srData[i].accountNumber)? srData[i].accountNumber : ''}"}, {"name": "Client", "type": "string", "fieldBinding": "${!isEmpty(srData[i].clientName)? srData[i].clientName : ''}"}, {"name": "Status", "type": "string", "fieldBinding": "${!isEmpty(srData[i].currentTask.currentStatus.status.description)? srData[i].currentTask.currentStatus.status.description : ''}"}, {"name": "Assigned To", "type": "string", "fieldBinding": "${!isEmpty(srData[i].currentTask.assignedTo.fullDisplayName)? srData[i].currentTask.assignedTo.fullDisplayName : ''}"}, {"name": "Created By", "type": "string", "fieldBinding": "${!isEmpty(srData[i].createdBy)? srData[i].createdBy : ''}"}, {"name": "Created At", "type": "string", "fieldBinding": "${!isEmpty(srData[i].createdAt)? srData[i].createdAt : ''}"}, {"name": "Last Modified At", "type": "string", "fieldBinding": "${!isEmpty(srData[i].lastModifiedAt)? srData[i].lastModifiedAt : ''}"}]}]}], "global": {"endpoints": [{"description": "Get the latest execution date for the job name \"All custodian steps\"", "url": "/api/domain/wealthdomain/executionHistory/dynamic/query/latestByJobName", "base_url": "http://app-data-manager:8002", "method": "get", "query_params": {"jobName": "All%20custodian%20steps"}, "path_params": {}, "requestBody": {}, "responseSchema": {"type": "array", "items": {"asOfDate": "Date (YYYY-MM-DD)"}}, "resultVariable": "jobDetailsList"}], "fieldBindings": [{"name": "userId", "description": "The logged in user id.", "type": "string", "value": "${userId}"}, {"name": "gvGlobalAsOfDate", "description": "The data as of date. This is used to get the data as of date for the advisor account details.", "type": "string", "value": "${jobDetailsList[0].asOfDate}"}, {"name": "gorg", "description": "Organization name", "type": "string", "value": "${gorg}"}, {"name": "gvCurrentDate", "description": "Current date in iso format (yyyy-MM-dd)", "type": "string", "value": "${currentDate()}"}]}}