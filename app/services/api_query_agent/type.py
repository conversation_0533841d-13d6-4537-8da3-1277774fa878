from pydantic import BaseModel, Field, ConfigDict
from typing import Any


class CallStep(BaseModel):
    model_config = ConfigDict(extra="allow")

    description: str = Field(description="Description of the API call", default="")
    url: str = Field(description="URL of the API call", default="")
    method: str = Field(description="HTTP method of the API call", default="get")
    query_params: dict | None | str = Field(
        description="Query parameters of the API call. If no query params, leave empty",
        default=None,
    )
    path_params: dict | None | str = Field(
        description="Path parameters of the API call in json string format",
        default=None,
    )
    requestBody: dict | None | str = Field(
        description="Request body of the API call in json string format",
        default=None,
    )
    responseSchema: dict | None = Field(
        description="Response schema of the API call", default=None
    )
    resultVariable: str | None = Field(
        description="Result variable of the API call", default=""
    )
    smallTalk: str | None = Field(
        description="If user is just making smallTalk, then give a suitable response back. Other fields can be empty",
        default="",
    )
    base_url: str | None = Field(
        description="Base URL for the API call (used when run.mode != TEST)",
        default=None,
    )

    def get(self, key: str, default: Any = None) -> Any:
        """Access attributes dictionary-style with default value"""
        return getattr(self, key, default)


# Define the expected JSON structure for the plan generation response
CALL_STEP_SCHEMA = {
    "type": "object",
    "properties": {
        "id": {
            "type": "string",
            "description": "Unique identifier for the step. NOTE: This field is immutable",
        },
        "query_params": {
            "type": "string",
            "description": "Dict with Query params  in json string format. If no query params present , give empty dict. Dont give null as value",
        },
        "path_params": {
            "type": "string",
            "description": "Dict with path params in json string format.  If no path params, give empty dict. Dont give null as value",
        },
        "requestBody": {
            "type": "string",
            "description": "Dict with request body in json string format.  If no request body, give empty dict. Dont give null as value",
        },
    },
    "required": [
        "id",
        "requestBody",
    ],
}

PLAN_SCHEMA = {"type": "array", "items": CALL_STEP_SCHEMA}


class ExpressionEvaluatorResponse(BaseModel):
    result: Any = Field(default=None, description="Result of the expression evaluation")
    error: str | None = Field(
        default=None, description="Error in the expression evaluation"
    )


class ApiAgentPayload(BaseModel):
    query: str = Field(default="", description="Query string to process")
    apiContextPath: str = Field(default="", description="API context path")
    planningPromptPath: str = Field(default="", description="Planning prompt path")
    answerPromptPath: str = Field(default="", description="Answer prompt path")
    contextData: dict = Field(default={}, description="Context data")
