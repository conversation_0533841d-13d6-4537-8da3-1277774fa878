{"tests": [{"query": "Show me the list of top 5 accounts", "expected_output": {"$type": "text", "text": "\"[{\\\"structured_data\\\":{\\\"headers\\\":[\\\"Account Number\\\",\\\"Account Name\\\",\\\"Registration Type\\\",\\\"Account Status\\\",\\\"Market Value\\\",\\\"Cash Balance\\\",\\\"Margin Balance\\\",\\\"MMF Balance\\\"],\\\"types\\\":[\\\"Singlelinetext\\\",\\\"Singlelinetext\\\",\\\"Singlelinetext\\\",\\\"Singlelinetext\\\",\\\"Currency\\\",\\\"Currency\\\",\\\"Currency\\\",\\\"Currency\\\"],\\\"rows\\\":[[\\\"********\\\",\\\"APRIL SHOWERS\\\",null,\\\"Axos Closed (XL)\\\",\\\"*********.05\\\",\\\"-**********.93\\\",\\\"0\\\",\\\"0\\\"],[\\\"********\\\",\\\"DOLORES COOPER\\\",null,\\\"Open\\\",\\\"********\\\",\\\"0\\\",\\\"-********.37\\\",\\\"0\\\"],[\\\"********\\\",\\\"STUART WEBSTER TRUSTEE PLATEAU MANAGERIAL RECRUITERS PSP DTD MM/DD/YY\\\",null,\\\"Open\\\",\\\"********.46\\\",\\\"7000\\\",\\\"0\\\",\\\"7000\\\"],[\\\"********\\\",\\\"AAAA INSURANCE AND INVESTMENTS ATTN BRITTNEY WATSON\\\",null,\\\"Open\\\",\\\"********.87\\\",\\\"-919764.8\\\",\\\"-1277343.97\\\",\\\"0\\\"],[\\\"********\\\",\\\"VINCE  NEIL\\\",null,\\\"Open\\\",\\\"********.24\\\",\\\"11500\\\",\\\"0\\\",\\\"11500\\\"]]},\\\"text\\\":\\\"Top 5 accounts by ending market value\\\",\\\"type\\\":\\\"table\\\"}]\"", "html": null, "attachments": null}, "type": "table", "expected_keywords": ["table", "structured_data", "headers", "types", "rows"]}, {"query": "Show me the list of top 5 accounts with just account number and market value", "expected_output": {}, "skip_test": false}, {"query": "get the list of accounts for the name <PERSON>, pick the first 5", "expected_output": {"$type": "text", "text": "\"[{\\\"structured_data\\\":{\\\"headers\\\":[\\\"Account Number\\\",\\\"Account Name\\\"],\\\"types\\\":[\\\"Singlelinetext\\\",\\\"Singlelinetext\\\"],\\\"rows\\\":[[\\\"********\\\",\\\"EQUITY TRUST COMPANY CUST FBO SIMON & JESSICA LIEGEL TRUST 1-26-2016 SIMON & JESSICA LIEGEL TTEE\\\"],[\\\"********\\\",\\\"THE JESSICA HANSEN LIVING TR DTD 12/18/2012 JESSICA HANSEN TTEE\\\"],[\\\"********\\\",\\\"EQUITY TRUST COMPANY CUST FBO SIMON & JESSICA LIEGEL TRUST 1-26-2016 SIMON & JESSICA LIEGEL TTEE\\\"],[\\\"********\\\",\\\"EQUITY TRUST COMPANY CUST FBO SIMON & JESSICA LIEGEL TRUST 1-26-2016 SIMON & JESSICA LIEGEL TTEE\\\"],[\\\"********\\\",\\\"THE JESSICA HANSEN LIVING TR DTD 12/18/2012 JESSICA HANSEN TTEE\\\"]]},\\\"text\\\":\\\"Here is the list of accounts for the name <PERSON>. The first 5 accounts are shown below.\\\",\\\"type\\\":\\\"table\\\"}]\"", "html": null, "attachments": null}, "type": "table", "expected_keywords": ["table", "structured_data", "headers", "types", "rows"]}, {"query": "How was the asset performance for the last 3 months ? show it as a bar chart", "expected_output": {"$type": "text", "text": "\"[{\\\"structured_data\\\":{\\\"seriesFields\\\":[{\\\"dataType\\\":\\\"Currency\\\",\\\"field\\\":\\\"endingEts\\\",\\\"name\\\":\\\"Ending Assets\\\"}],\\\"categoryField\\\":\\\"periodEndDate\\\",\\\"data\\\":[{\\\"periodEndDate\\\":\\\"2025-02-24\\\",\\\"periodType\\\":\\\"Monthly\\\",\\\"endingEts\\\":********.53},{\\\"periodEndDate\\\":\\\"2025-02-24\\\",\\\"periodType\\\":\\\"Monthly\\\",\\\"endingEts\\\":********.28},{\\\"periodEndDate\\\":\\\"2025-02-24\\\",\\\"periodType\\\":\\\"Monthly\\\",\\\"endingEts\\\":********.53},{\\\"periodEndDate\\\":\\\"2025-02-24\\\",\\\"periodType\\\":\\\"Monthly\\\",\\\"endingEts\\\":*********.29},{\\\"periodEndDate\\\":\\\"2025-04-25\\\",\\\"periodType\\\":\\\"MTD\\\",\\\"endingEts\\\":921246.93}]},\\\"text\\\":\\\"Asset performance for the last three months is shown in the bar chart below.\\\",\\\"type\\\":\\\"barchart\\\"}]\"", "html": null, "attachments": null}, "type": "bar_chart", "expected_keywords": ["barchart", "structured_data", "seriesFields", "categoryField", "data"]}, {"query": "How was the asset performance for the last 12 months ? show it as a bar chart", "expected_output": {"$type": "text", "text": "\"[{\\\"structured_data\\\":{\\\"seriesFields\\\":[{\\\"dataType\\\":\\\"Currency\\\",\\\"field\\\":\\\"endingEts\\\",\\\"name\\\":\\\"Ending Assets\\\"}],\\\"categoryField\\\":\\\"periodEndDate\\\",\\\"data\\\":[{\\\"periodEndDate\\\":\\\"2025-02-24\\\",\\\"periodType\\\":\\\"Monthly\\\",\\\"endingEts\\\":********.53},{\\\"periodEndDate\\\":\\\"2025-02-24\\\",\\\"periodType\\\":\\\"Monthly\\\",\\\"endingEts\\\":********.28},{\\\"periodEndDate\\\":\\\"2025-02-24\\\",\\\"periodType\\\":\\\"Monthly\\\",\\\"endingEts\\\":********.53},{\\\"periodEndDate\\\":\\\"2025-02-24\\\",\\\"periodType\\\":\\\"Monthly\\\",\\\"endingEts\\\":*********.29},{\\\"periodEndDate\\\":\\\"2025-04-25\\\",\\\"periodType\\\":\\\"MTD\\\",\\\"endingEts\\\":921246.93}]},\\\"text\\\":\\\"Bar chart showing asset performance for the last 12 months.\\\",\\\"type\\\":\\\"barchart\\\"}]\"", "html": null, "attachments": null}, "type": "bar_chart", "expected_keywords": ["barchart", "structured_data", "seriesFields", "categoryField", "data"]}, {"query": "show asset allocation for the last 4 months as bar graph", "expected_output": {"$type": "text", "text": "\"[{\\\"structured_data\\\":{\\\"seriesFields\\\":[{\\\"dataType\\\":\\\"Currency\\\",\\\"field\\\":\\\"endingEts\\\",\\\"name\\\":\\\"Ending Assets\\\"}],\\\"categoryField\\\":\\\"periodEndDate\\\",\\\"data\\\":[{\\\"periodEndDate\\\":\\\"2025-02-24\\\",\\\"periodType\\\":\\\"Monthly\\\",\\\"endingEts\\\":********.53},{\\\"periodEndDate\\\":\\\"2025-02-24\\\",\\\"periodType\\\":\\\"Monthly\\\",\\\"endingEts\\\":********.28},{\\\"periodEndDate\\\":\\\"2025-02-24\\\",\\\"periodType\\\":\\\"Monthly\\\",\\\"endingEts\\\":********.53},{\\\"periodEndDate\\\":\\\"2025-02-24\\\",\\\"periodType\\\":\\\"Monthly\\\",\\\"endingEts\\\":*********.29}]},\\\"text\\\":\\\"Bar chart showing asset allocation for the last four months.\\\",\\\"type\\\":\\\"barchart\\\"}]\"", "html": null, "attachments": null}, "type": "bar_chart", "expected_keywords": ["barchart", "structured_data", "seriesFields", "categoryField", "data"]}, {"query": "show asset allocation for the last month as bar graph", "expected_output": {"$type": "text", "text": "\"[{\\\"structured_data\\\":{\\\"seriesFields\\\":[{\\\"dataType\\\":\\\"Currency\\\",\\\"field\\\":\\\"endingEts\\\",\\\"name\\\":\\\"Ending Assets\\\"}],\\\"categoryField\\\":\\\"periodEndDate\\\",\\\"data\\\":[{\\\"periodEndDate\\\":\\\"2025-04-25\\\",\\\"periodType\\\":\\\"Monthly\\\",\\\"endingEts\\\":921246.93}]},\\\"text\\\":\\\"Asset allocation for the last month.\\\",\\\"type\\\":\\\"barchart\\\"}]\"", "html": null, "attachments": null}, "type": "bar_chart", "expected_keywords": ["barchart", "structured_data", "seriesFields", "categoryField", "data"]}, {"query": "Can you show me the current asset allocation ?", "expected_output": {"$type": "text", "text": "\"[{\\\"structured_data\\\":{\\\"headers\\\":[\\\"Security Category\\\",\\\"Total Market Value\\\"],\\\"types\\\":[\\\"Singlelinetext\\\",\\\"Currency\\\"],\\\"rows\\\":[[\\\"ETF\\\",6556.6],[\\\"Common Stock\\\",172753.56]]},\\\"text\\\":\\\"The following table presents the current asset allocation based on the provided data.\\\",\\\"type\\\":\\\"table\\\"}]\"", "html": null, "attachments": null}, "type": "table", "expected_keywords": ["table", "structured_data", "headers", "types", "rows", "6556.6", "172753.56"]}, {"query": "Can you show me the current asset allocation ? as a pie chart", "expected_output": {"$type": "text", "text": "\"[{\\\"structured_data\\\":{\\\"seriesFields\\\":[{\\\"dataType\\\":\\\"Currency\\\",\\\"field\\\":\\\"totalMarketValue\\\",\\\"name\\\":\\\"Total Market Value\\\"}],\\\"categoryField\\\":\\\"securityCategory\\\",\\\"data\\\":[{\\\"totalMarketValue\\\":\\\"6556.6\\\",\\\"securityCategory\\\":\\\"ETF\\\"},{\\\"totalMarketValue\\\":\\\"172753.56\\\",\\\"securityCategory\\\":\\\"Common Stock\\\"}]},\\\"text\\\":\\\"The current asset allocation is shown below.  The pie chart displays the breakdown of total market value across different security categories.\\\",\\\"type\\\":\\\"piechart\\\"}]\"", "html": null, "attachments": null}, "type": "pie_chart", "expected_keywords": ["piechart", "6556.6", "172753.56", "structured_data", "categoryField", "data", "seriesFields"]}, {"query": "Portfolio Allocation", "expected_output": {"$type": "text", "text": "\"[{\\\"structured_data\\\":{\\\"seriesFields\\\":[{\\\"dataType\\\":\\\"Currency\\\",\\\"field\\\":\\\"totalMarketValue\\\",\\\"name\\\":\\\"Total Market Value\\\"}],\\\"categoryField\\\":\\\"securityCategory\\\",\\\"data\\\":[{\\\"totalMarketValue\\\":\\\"6556.6\\\",\\\"securityCategory\\\":\\\"ETF\\\"},{\\\"totalMarketValue\\\":\\\"172753.56\\\",\\\"securityCategory\\\":\\\"Common Stock\\\"}]},\\\"text\\\":\\\"Portfolio Allocation: This pie chart shows the distribution of your portfolio across different security categories.  The data is based on total market value.\\\",\\\"type\\\":\\\"piechart\\\"}]\"", "html": null, "attachments": null}, "type": "pie_chart", "expected_keywords": ["piechart", "6556.6", "172753.56", "structured_data", "categoryField", "data", "seriesFields"]}, {"query": "portfolio allocation as pie chart", "expected_output": {"$type": "text", "text": "\"[{\\\"structured_data\\\":{\\\"seriesFields\\\":[{\\\"dataType\\\":\\\"Currency\\\",\\\"field\\\":\\\"totalMarketValue\\\",\\\"name\\\":\\\"Total Market Value\\\"}],\\\"categoryField\\\":\\\"securityCategory\\\",\\\"data\\\":[{\\\"totalMarketValue\\\":\\\"6556.6\\\",\\\"securityCategory\\\":\\\"ETF\\\"},{\\\"totalMarketValue\\\":\\\"172753.56\\\",\\\"securityCategory\\\":\\\"Common Stock\\\"}]},\\\"text\\\":\\\"Portfolio Allocation\\\",\\\"type\\\":\\\"piechart\\\"}]\"", "html": null, "attachments": null}, "type": "pie_chart", "expected_keywords": ["piechart", "6556.6", "172753.56", "structured_data", "categoryField", "data", "seriesFields"]}, {"query": "what is the registration type for account ********", "expected_output": {"$type": "text", "text": "\"[{\\\"structured_data\\\":{\\\"html\\\":\\\"<body>The registration type for account ******** is <b>Individual</b>.</body>\\\"},\\\"text\\\":\\\"The registration type for account ******** is <b>Individual</b>.\\\",\\\"type\\\":\\\"singlerow\\\"}]\"", "html": null, "attachments": null}, "type": "single_value", "expected_keywords": ["Individual"]}, {"query": "What is the account balance for ********", "expected_output": {"$type": "text", "text": "\"[{\\\"structured_data\\\":{\\\"accountBalance\\\":\\\"$7906.96\\\",\\\"accountNumber\\\":\\\"********\\\"},\\\"text\\\":\\\"The account balance for account number <b>********</b> is <b>$7906.96</b>.\\\",\\\"type\\\":\\\"singlerow\\\"}]\"", "html": null, "attachments": null}, "type": "single_value", "expected_keywords": ["$7906.96"]}, {"query": "What is the cash value for ********", "expected_output": {"$type": "text", "text": "\"[{\\\"structured_data\\\":{\\\"html\\\":\\\"<body>The cash value for account number <b>********</b> is <b>$-52261.03</b>.</body>\\\"},\\\"text\\\":\\\"The cash value for account number <b>********</b> is <b>$-52261.03</b>.\\\",\\\"type\\\":\\\"singlerow\\\"}]\"", "html": null, "attachments": null}, "type": "single_value", "expected_keywords": ["$-52261.03"]}, {"query": "What is the account status for ********", "expected_output": {"$type": "text", "text": "\"[{\\\"structured_data\\\":{\\\"html\\\":\\\"<body>The account status for account number <b>********</b> is <b>Open</b>.</body>\\\"},\\\"text\\\":\\\"The account status for account number <b>********</b> is <b>Open</b>.\\\",\\\"type\\\":\\\"singlerow\\\"}]\"", "html": null, "attachments": null}, "type": "single_value", "expected_keywords": ["Open"]}, {"query": "get cash balance for account ********", "expected_output": {"$type": "text", "text": "\"[{\\\"structured_data\\\":{\\\"html\\\":\\\"<body>The cash balance for account ******** is <b>$-52261.03</b>.</body>\\\"},\\\"text\\\":\\\"The cash balance for account ******** is <b>$-52261.03</b>.\\\",\\\"type\\\":\\\"singlerow\\\"}]\"", "html": null, "attachments": null}, "type": "single_value", "expected_keywords": ["$-52261.03"]}, {"query": "what is the funds available to withdraw for account ********", "expected_output": {"$type": "text", "text": "\"[{\\\"structured_data\\\":\\\"<html><body>For account <b>********</b>, the funds available to withdraw are <b>$0.00</b>.</body></html>\\\",\\\"text\\\":\\\"For account ********, the funds available to withdraw are $0.00.\\\",\\\"type\\\":\\\"singlerow\\\"}]\"", "html": null, "attachments": null}, "type": "single_value", "expected_keywords": ["$0.00"]}, {"query": "can i get ending Money Market Balance for account ********", "expected_output": {"$type": "text", "text": "\"[{\\\"structured_data\\\":\\\"<html><body>The ending Money Market Balance for account <b>********</b> is <b>$0.00</b>.</body></html>\\\",\\\"text\\\":\\\"The ending Money Market Balance for account ******** is $0.00.\\\",\\\"type\\\":\\\"singlerow\\\"}]\"", "html": null, "attachments": null}, "type": "single_value", "expected_keywords": ["$0.00"]}, {"query": "get MMF for account ********", "expected_output": {}}, {"query": "please send trade Date Balance for account ********", "expected_output": {"$type": "text", "text": "\"[{\\\"structured_data\\\":{\\\"html\\\":\\\"<body>The trade date balance for account ******** is <b>$-52261.03</b>.</body>\\\"},\\\"text\\\":\\\"The trade date balance for account ******** is <b>$-52261.03</b>.\\\",\\\"type\\\":\\\"singlerow\\\"}]\"", "html": null, "attachments": null}, "type": "single_value", "expected_keywords": ["$-52261.03"]}, {"query": "please send settlement date balance for account ********", "expected_output": {"$type": "text", "text": "\"[{\\\"structured_data\\\":\\\"<html><body>The settlement date balance for account <b>********</b> is <b>$-52261.03</b>.</body></html>\\\",\\\"text\\\":\\\"The settlement date balance for account ******** is $-52261.03.\\\",\\\"type\\\":\\\"singlerow\\\"}]\"", "html": null, "attachments": null}, "type": "single_value", "expected_keywords": ["$-52261.03"]}, {"query": "what is the long position value for the account ********", "expected_output": {"$type": "text", "text": "\"[{\\\"structured_data\\\":\\\"<html><body>The <strong>long position value</strong> for account <strong>********</strong> is <strong>$60167.99</strong>.</body></html>\\\",\\\"text\\\":\\\"The long position value for account ******** is $60167.99.\\\",\\\"type\\\":\\\"singlerow\\\"}]\"", "html": null, "attachments": null}, "type": "single_value", "expected_keywords": ["$60167.99"]}, {"query": "what is the long market value for the account ********", "expected_output": {}, "skip_test": true}, {"query": "please send settlement Fee Balance for account ********", "expected_output": {"$type": "text", "text": "\"[{\\\"structured_data\\\":\\\"<html><body>The settlement fee balance for account <b>********</b> is <b>$-52261.03</b>.</body></html>\\\",\\\"text\\\":\\\"The settlement fee balance for account ******** is $-52261.03.\\\",\\\"type\\\":\\\"singlerow\\\"}]\"", "html": null, "attachments": null}, "type": "single_value", "expected_keywords": ["$-52261.03"]}, {"query": "can i get SMA balance for account ********", "expected_output": {"$type": "text", "text": "\"[{\\\"structured_data\\\":\\\"The SMA balance for account ******** is $0.00\\\",\\\"text\\\":\\\"<html><body>The SMA balance for account <strong>********</strong> is <strong>$0.00</strong>.</body></html>\\\",\\\"type\\\":\\\"singlerow\\\"}]\"", "html": null, "attachments": null}, "type": "single_value", "expected_keywords": ["$0.00"]}, {"query": "please send Margin balance for account ********", "expected_output": {"$type": "text", "text": "\"[{\\\"structured_data\\\":\\\"The margin balance for account ******** is $0.00\\\",\\\"text\\\":\\\"<html><body>The margin balance for account <b>********</b> is <b>$0.00</b>.</body></html>\\\",\\\"type\\\":\\\"singlerow\\\"}]\"", "html": null, "attachments": null}, "type": "single_value", "expected_keywords": ["$0.00"]}, {"query": "please send ending Margin balance for account ********", "expected_output": {}, "skip_test": true}, {"query": "please send Open Current Fed call for account ********", "expected_output": {"$type": "text", "text": "\"[{\\\"structured_data\\\":\\\"<html><body>The fed call for account <b>********</b> is <b>$0.00</b>.</body></html>\\\",\\\"text\\\":\\\"The fed call for account ******** is $0.00.\\\",\\\"type\\\":\\\"singlerow\\\"}]\"", "html": null, "attachments": null}, "type": "single_value", "expected_keywords": ["$0.00"]}, {"query": "get today fed call value for the account ********", "expected_output": {"$type": "text", "text": "\"[{\\\"structured_data\\\":\\\"<html><body>The <b>fed call</b> value for account <b>********</b> is <b>$0.00</b>.</body></html>\\\",\\\"text\\\":\\\"Today's fed call value for account ******** is $0.00.\\\",\\\"type\\\":\\\"singlerow\\\"}]\"", "html": null, "attachments": null}, "type": "single_value", "expected_keywords": ["$0.00"]}, {"query": "get accumulated fed call value for the account ********", "expected_output": {"$type": "text", "text": "\"[{\\\"structured_data\\\":\\\"<html><body>The accumulated fed call value for account <b>********</b> is <b>$0.00</b>.</body></html>\\\",\\\"text\\\":\\\"<html><body>The accumulated fed call value for account <b>********</b> is <b>$0.00</b>.</body></html>\\\",\\\"type\\\":\\\"singlerow\\\"}]\"", "html": null, "attachments": null}, "type": "single_value", "expected_keywords": ["$0.00"]}, {"query": "get Maintenance call for the account ********", "expected_output": {"$type": "text", "text": "\"[{\\\"structured_data\\\":\\\"The maintenance call for account ******** is $0.00\\\",\\\"text\\\":\\\"<html><body>The maintenance call for account <b>********</b> is <b>$0.00</b>.</body></html>\\\",\\\"type\\\":\\\"singlerow\\\"}]\"", "html": null, "attachments": null}, "type": "single_value", "expected_keywords": ["$0.00"]}, {"query": "get Market Value Type 1 for account ********", "expected_output": {"$type": "text", "text": "\"[{\\\"structured_data\\\":\\\"The Market Value Type 1 for account ******** is $60,167.99\\\",\\\"text\\\":\\\"<html><body>The <b>Market Value Type 1</b> for account <b>********</b> is <b>$60,167.99</b>.</body></html>\\\",\\\"type\\\":\\\"singlerow\\\"}]\"", "html": null, "attachments": null}, "type": "single_value", "expected_keywords": ["$60,167.99"]}, {"query": "get cash Account Market Value for account ********", "expected_output": {"$type": "text", "text": "\"[{\\\"structured_data\\\":\\\"<html><body>The cash account market value for account <b>********</b> is <b>$60,167.99</b>.</body></html>\\\",\\\"text\\\":\\\"The cash account market value for account ******** is $60,167.99.\\\",\\\"type\\\":\\\"singlerow\\\"}]\"", "html": null, "attachments": null}, "type": "single_value", "expected_keywords": ["$60,167.99"]}, {"query": "get cash available type 1 for account ********", "expected_output": {"$type": "text", "text": "\"[{\\\"structured_data\\\":\\\"The cash available for account ******** is $30083.99\\\",\\\"text\\\":\\\"<html><body>The cash available for account <b>********</b> is <b>$30083.99</b>.</body></html>\\\",\\\"type\\\":\\\"singlerow\\\"}]\"", "html": null, "attachments": null}, "type": "single_value", "expected_keywords": ["$30083.99"]}, {"query": "Hi", "expected_output": {"$type": "text", "text": "\"[{\\\"structured_data\\\":{\\\"html\\\":\\\"<body> <p>Hi there! How can I help you with your wealth management questions today?</p> </body>\\\"},\\\"text\\\":\\\"Hi there! How can I help you with your wealth management questions today?\\\",\\\"type\\\":\\\"singlerow\\\"}]\"", "html": null, "attachments": null}, "type": "small_talk"}, {"query": "How is weather today", "expected_output": {"$type": "text", "text": "\"[{\\\"structured_data\\\":{\\\"html\\\":\\\"<body> <p>I am designed to answer questions related to wealth management. How can I help you with your accounts or investments?</p> </body>\\\"},\\\"text\\\":\\\"I am designed to answer questions related to wealth management. How can I help you with your accounts or investments?\\\",\\\"type\\\":\\\"singlerow\\\"}]\"", "html": null, "attachments": null}, "type": "small_talk"}, {"query": "show trade data balance", "expected_output": {"$type": "text", "text": "\"[{\\\"structured_data\\\":{\\\"html\\\":\\\"<body> <p>I can help you with wealth management related queries. Please let me know what you would like to know about your accounts or investments.</p> </body>\\\"},\\\"text\\\":\\\"I can help you with wealth management related queries. Please let me know what you would like to know about your accounts or investments.\\\",\\\"type\\\":\\\"singlerow\\\"}]\"", "html": null, "attachments": null}, "type": "out_of_scope"}, {"query": "get the list of accounts for the name 1458963", "expected_output": {"$type": "text", "text": "\"[{\\\"structured_data\\\":{},\\\"text\\\":\\\"No account information found for the name 1458963.\\\",\\\"type\\\":\\\"singlerow\\\"}]\"", "html": null, "attachments": null}, "type": "out_of_scope"}, {"query": "get all accounts with cash value < 100", "expected_output": {"$type": "text", "text": "\"[{\\\"structured_data\\\":{\\\"headers\\\":[\\\"Account Number\\\",\\\"Account Name\\\",\\\"Cash Value\\\"],\\\"types\\\":[\\\"Singlelinetext\\\",\\\"Singlelinetext\\\",\\\"Currency\\\"],\\\"rows\\\":[[\\\"********\\\",\\\"ENTITYTEST NORBERTO M TRUST TTE DTD 08/19/2020\\\",\\\"0\\\"],[\\\"********\\\",\\\"CRISTINA M IRA SEP AXOS CLEARING CUST\\\",\\\"0\\\"],[\\\"********\\\",\\\"NORBERTO M ALBA TERE ALBA TEN COM\\\",\\\"0\\\"],[\\\"********\\\",\\\"ENTITYTEST ATTN COHOLDERLONNIE M TRUST\\\",\\\"0\\\"],[\\\"********\\\",\\\"NORBERTO M ALBA TERE ALBA TEN COM\\\",\\\"0\\\"],[\\\"********\\\",\\\"SYNTHETIC T TESTER\\\",\\\"0\\\"],[\\\"********\\\",\\\"ENTITYTEST NORBERTO M TRUST TTE DTD 08/19/2020\\\",\\\"0\\\"],[\\\"********\\\",\\\"AUTOMATION J CUJJVLSVILUL\\\",\\\"0\\\"],[\\\"********\\\",\\\"SWISS AMERICAN SECURITIES INC\\\",\\\"0\\\"],[\\\"********\\\",\\\"NORBERTO M INDIVIDUAL\\\",\\\"38\\\"]]},\\\"text\\\":\\\"The following table presents all accounts with a cash value less than $100.\\\",\\\"type\\\":\\\"table\\\"}]\"", "html": null, "attachments": null}, "type": "dynamic_query", "expected_keywords": ["table", "structured_data", "headers", "types", "rows"]}, {"query": "give me details of account where primary owner is jessica and cash value is less than 100", "expected_output": {"$type": "text", "text": "\"[{\\\"structured_data\\\":{\\\"headers\\\":[\\\"Account Number\\\",\\\"Account Name\\\",\\\"Primary Owner Name\\\",\\\"Cash Value\\\"],\\\"types\\\":[\\\"Singlelinetext\\\",\\\"Singlelinetext\\\",\\\"Singlelinetext\\\",\\\"Currency\\\"],\\\"rows\\\":[[\\\"********\\\",\\\"NATHAN GARDNER\\\",\\\"GOMEZ JESSICA\\\",\\\"0\\\"],[\\\"********\\\",\\\"JESSICA P MILLER\\\",\\\"MILLER JESSICA P\\\",\\\"0\\\"],[\\\"********\\\",\\\"JESSICA OLIVER\\\",\\\"OLIVER JESSICA\\\",\\\"0\\\"],[\\\"********\\\",\\\"JESSICA P MILLER\\\",\\\"MILLER JESSICA P\\\",\\\"0\\\"],[\\\"********\\\",\\\"JESSICA P MILLER\\\",\\\"MILLER JESSICA P\\\",\\\"0\\\"],[\\\"********\\\",\\\"JESSICA P MILLER\\\",\\\"MILLER JESSICA P\\\",\\\"0\\\"],[\\\"********\\\",\\\"JESSICA COOPER\\\",\\\"COOPER JESSICA\\\",\\\"0\\\"],[\\\"********\\\",\\\"JESSICA CARTER\\\",\\\"CARTER JESSICA\\\",\\\"0\\\"],[\\\"********\\\",\\\"JESSICA P MILLER\\\",\\\"MILLER JESSICA P\\\",\\\"0\\\"],[\\\"********\\\",\\\"JESSICA P MILLER\\\",\\\"MILLER JESSICA P\\\",\\\"0\\\"]]},\\\"text\\\":\\\"Details of accounts where the primary owner is Jessica and the cash value is less than $100.\\\",\\\"type\\\":\\\"table\\\"}]\"", "html": null, "attachments": null}, "type": "dynamic_query", "expected_keywords": ["table", "structured_data", "headers", "types", "rows"]}]}