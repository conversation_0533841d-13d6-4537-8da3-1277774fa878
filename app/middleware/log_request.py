import json
import logging

from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware

logger = logging.getLogger(__name__)

class LogRequestMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        # Get the HTTP method and URL path
        method = request.method
        url = str(request.url)

        body = await request.body()
        
        # Try to decode the body if it's JSON
        try:
            payload = json.loads(body.decode())
        except json.JSONDecodeError:
            payload = None  # If it's not JSON or an empty body
        
        # Print the HTTP method, URL, and payload (if any)
        if payload is not None:
            logger.info(f"Request: {method} {url} | Payload: {payload}")
        else:
            logger.info(f"Request: {method} {url} | No payload")

        # Continue processing the request
        response = await call_next(request)
        return response