import json
import os
import uuid
import shutil
from typing import Any, Optional, Union
from starlette.requests import HTTPConnection, Request
from starlette.responses import Response
from starlette.types import Message
from starlette_context.plugins import Plugin
from starlette_context import context


from core.config import SETTINGS


class HeaderPlugin(Plugin):
    key = "headers"

    async def process_request(
        self, request: Union[Request, HTTPConnection]
    ) -> Optional[Any]:
        headers = {
            "X-Jiffy-Tenant-Id": request.headers.get("x-jiffy-tenant-id", ""),
            "X-Jiffy-App-Id": request.headers.get("x-jiffy-app-id", ""),
            "X-Jiffy-User-Id": request.headers.get("x-jiffy-user-id", ""),
        }
        return headers

class WorkspacePlugin(Plugin):
    key = "workspace_folder"

    async def process_request(
        self, request: Union[Request, HTTPConnection]
    ) -> Optional[Any]:
        workspace_folder = os.path.join(SETTINGS.WORKSPACE_PATH, str(uuid.uuid4()))
        os.makedirs(workspace_folder, exist_ok=True)
        return workspace_folder

    async def enrich_response(self, arg: Union[Response, Message]) -> None:
        workspace_folder = context.get(self.key)
        if workspace_folder and os.path.exists(workspace_folder):
            shutil.rmtree(workspace_folder)
