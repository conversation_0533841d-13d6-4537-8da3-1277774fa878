import os
import sys

from pathlib import Path

sys.path.append(str(Path(__file__).parent.parent.parent))

from core.config import AUTH_PROPERTIES

os.environ["GOOGLE_CLOUD_PROJECT"] = AUTH_PROPERTIES.GOOGLE_CLOUD_PROJECT
os.environ["GOOGLE_CLOUD_LOCATION"] = AUTH_PROPERTIES.GOOGLE_CLOUD_LOCATION
os.environ["GOOGLE_GENAI_USE_VERTEXAI"] = "TRUE"


from google.adk.agents import Agent

from agents.advisor_agent.dynamic_query import call_dynamic_query, DynamicQueryGenerator

BASE_PATH = os.path.dirname(os.path.abspath(__file__))

async def dynamic_query_tool(query:str):
    '''
    Executes a natural language query and returns the response.
    
    This function takes a user query for retrieving data from the database in plain language and returns the data in the form of a JSON object.
    
    Args:
        query (str): The query to be executed.
    Returns:
        dict: The response from the query execution.
    '''
    try:
        generator = DynamicQueryGenerator()
        payload = await generator.generate(query)
        if payload and isinstance(payload, dict):
            result = await call_dynamic_query(payload)
            return result
        else:
            return payload
    except Exception as e:
        print("Error while executing query:", e)
    
    return "Unable to execute the query"


with open(os.path.join(BASE_PATH, "./prompts/advisor_prompt.md"), "r") as f:
    advisor_instruction = f.read()

root_agent = Agent(
    name="financial_advisor_agent",
    model="gemini-2.0-flash",
    description=(
        "wealth advisor agent to answer questions about wealth management"
    ),
    instruction=advisor_instruction,
    tools=[dynamic_query_tool],
)