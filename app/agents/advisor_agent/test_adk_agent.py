#!/usr/bin/env python3
"""
Test script for the Dynamic Query ADK Agent.

This script provides comprehensive testing of the agent's functionality
including unit tests and integration tests.
"""

import asyncio
import json
import sys
import os
import unittest
from pathlib import Path
from unittest.mock import AsyncMock, patch, MagicMock

# Add the project root to the path
sys.path.append(str(Path(__file__).parent.parent.parent))

from agents.advisor_agent.dynamic_query_adk_agent import (
    DynamicQueryADKAgent,
    execute_dynamic_query_with_adk,
    call_dynamic_query
)


class TestDynamicQueryADKAgent(unittest.IsolatedAsyncioTestCase):
    """Test cases for the Dynamic Query ADK Agent."""
    
    async def asyncSetUp(self):
        """Set up test fixtures."""
        self.agent = DynamicQueryADKAgent()
        
        # Mock data for testing
        self.sample_query_payload = {
            "Account": {
                "select": {
                    "accountNumber": True,
                    "balance": True,
                    "state": True
                },
                "filter": "state == 'CA'",
                "limit": 10
            }
        }
        
        self.sample_api_response = {
            "data": [
                {
                    "accountNumber": "ACC001",
                    "balance": 15000.00,
                    "state": "CA"
                },
                {
                    "accountNumber": "ACC002", 
                    "balance": 25000.00,
                    "state": "CA"
                }
            ],
            "total": 2
        }
    
    def test_agent_initialization(self):
        """Test that the agent initializes correctly."""
        self.assertIsNotNone(self.agent)
        self.assertIsNotNone(self.agent.agent)
        self.assertEqual(self.agent.agent.name, "dynamic_query_agent")
        self.assertEqual(self.agent.agent.model, "gemini-2.0-flash")
    
    def test_configuration_loading(self):
        """Test that configuration files are loaded correctly."""
        self.assertIsNotNone(self.agent.schema)
        self.assertIsNotNone(self.agent.functions)
        self.assertIsNotNone(self.agent.examples)
        self.assertIsNotNone(self.agent.additional_instructions)
        self.assertIsNotNone(self.agent.system_instruction)
    
    def test_system_instruction_preparation(self):
        """Test that system instruction is prepared correctly."""
        instruction = self.agent.system_instruction
        
        # Check that placeholders are replaced
        self.assertNotIn("{{ schema }}", instruction)
        self.assertNotIn("{{ functions }}", instruction)
        self.assertNotIn("{{ examples }}", instruction)
        self.assertNotIn("{{ instructions }}", instruction)
        
        # Check that actual content is present
        self.assertIn("JSON Query Generation Engine", instruction)
    
    @patch('agents.advisor_agent.dynamic_query_adk_agent.call_dynamic_query')
    async def test_execute_dynamic_query_tool_success(self, mock_call_query):
        """Test successful query execution."""
        mock_call_query.return_value = self.sample_api_response
        
        # Test with string payload
        query_payload_str = json.dumps(self.sample_query_payload)
        result = await self.agent._execute_dynamic_query(query_payload_str)
        
        result_data = json.loads(result)
        self.assertTrue(result_data["success"])
        self.assertEqual(result_data["results"], self.sample_api_response)
        self.assertEqual(result_data["query"], self.sample_query_payload)
    
    @patch('agents.advisor_agent.dynamic_query_adk_agent.call_dynamic_query')
    async def test_execute_dynamic_query_tool_dict_payload(self, mock_call_query):
        """Test query execution with dict payload."""
        mock_call_query.return_value = self.sample_api_response
        
        # Test with dict payload
        result = await self.agent._execute_dynamic_query(self.sample_query_payload)
        
        result_data = json.loads(result)
        self.assertTrue(result_data["success"])
        self.assertEqual(result_data["results"], self.sample_api_response)
    
    async def test_execute_dynamic_query_tool_invalid_json(self):
        """Test query execution with invalid JSON."""
        invalid_json = "{ invalid json }"
        result = await self.agent._execute_dynamic_query(invalid_json)
        
        result_data = json.loads(result)
        self.assertIn("error", result_data)
        self.assertEqual(result_data["error"], "Invalid JSON format")
    
    @patch('agents.advisor_agent.dynamic_query_adk_agent.QueryValidator')
    async def test_execute_dynamic_query_tool_validation_error(self, mock_validator_class):
        """Test query execution with validation errors."""
        # Mock validator to return validation errors
        mock_validator = MagicMock()
        mock_validator.validate.return_value = (False, ["Invalid field: xyz"])
        mock_validator_class.return_value = mock_validator
        
        query_payload_str = json.dumps(self.sample_query_payload)
        result = await self.agent._execute_dynamic_query(query_payload_str)
        
        result_data = json.loads(result)
        self.assertIn("error", result_data)
        self.assertEqual(result_data["error"], "Invalid query structure")
        self.assertIn("validation_errors", result_data)
    
    def test_agent_with_previous_messages(self):
        """Test agent initialization with previous messages."""
        previous_messages = [
            {"role": "user", "content": "Hello"},
            {"role": "assistant", "content": "Hi there!"}
        ]
        
        agent_with_context = DynamicQueryADKAgent(previous_messages=previous_messages)
        
        # Check that context is included in system instruction
        self.assertIn("Previous conversation context", agent_with_context.system_instruction)
    
    @patch('agents.advisor_agent.dynamic_query_adk_agent.DynamicQueryADKAgent.chat')
    async def test_convenience_function(self, mock_chat):
        """Test the convenience function."""
        mock_chat.return_value = "Test response"
        
        result = await execute_dynamic_query_with_adk("Test query")
        
        self.assertEqual(result, "Test response")
        mock_chat.assert_called_once_with("Test query")


class TestCallDynamicQuery(unittest.IsolatedAsyncioTestCase):
    """Test cases for the call_dynamic_query function."""
    
    @patch('agents.advisor_agent.dynamic_query_adk_agent.get_headers')
    @patch('agents.advisor_agent.dynamic_query_adk_agent.AsyncClient')
    async def test_call_dynamic_query_success(self, mock_client_class, mock_get_headers):
        """Test successful API call."""
        # Mock headers
        mock_get_headers.return_value = {"Authorization": "Bearer token"}
        
        # Mock HTTP client
        mock_response = MagicMock()
        mock_response.json.return_value = {"data": "test"}
        mock_response.raise_for_status.return_value = None
        
        mock_client = MagicMock()
        mock_client.post.return_value = mock_response
        mock_client.__aenter__.return_value = mock_client
        mock_client.__aexit__.return_value = None
        mock_client_class.return_value = mock_client
        
        # Test payload
        payload = {
            "Account": {
                "select": {"accountNumber": True}
            }
        }
        
        result = await call_dynamic_query(payload)
        
        self.assertEqual(result, {"data": "test"})
        mock_client.post.assert_called_once()
    
    @patch('agents.advisor_agent.dynamic_query_adk_agent.get_headers')
    @patch('agents.advisor_agent.dynamic_query_adk_agent.AsyncClient')
    async def test_call_dynamic_query_adds_limit(self, mock_client_class, mock_get_headers):
        """Test that limit is added when select is present but limit is not."""
        # Mock headers
        mock_get_headers.return_value = {"Authorization": "Bearer token"}
        
        # Mock HTTP client
        mock_response = MagicMock()
        mock_response.json.return_value = {"data": "test"}
        mock_response.raise_for_status.return_value = None
        
        mock_client = MagicMock()
        mock_client.post.return_value = mock_response
        mock_client.__aenter__.return_value = mock_client
        mock_client.__aexit__.return_value = None
        mock_client_class.return_value = mock_client
        
        # Test payload without limit
        payload = {
            "Account": {
                "select": {"accountNumber": True}
            }
        }
        
        await call_dynamic_query(payload)
        
        # Check that limit was added
        call_args = mock_client.post.call_args
        sent_payload = call_args[1]['json']
        self.assertEqual(sent_payload["Account"]["limit"], 10)


async def run_integration_tests():
    """Run integration tests with actual agent (requires proper setup)."""
    print("Running integration tests...")
    
    try:
        # Test basic agent creation
        agent = DynamicQueryADKAgent()
        print("✓ Agent created successfully")
        
        # Test configuration loading
        assert agent.schema is not None
        assert agent.functions is not None
        assert agent.examples is not None
        print("✓ Configuration loaded successfully")
        
        # Test system instruction preparation
        assert "JSON Query Generation Engine" in agent.system_instruction
        print("✓ System instruction prepared successfully")
        
        print("All integration tests passed!")
        
    except Exception as e:
        print(f"✗ Integration test failed: {e}")
        import traceback
        traceback.print_exc()


async def main():
    """Run all tests."""
    print("Dynamic Query ADK Agent Test Suite")
    print("=" * 50)
    
    # Run unit tests
    print("Running unit tests...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    print("\n" + "=" * 50)
    
    # Run integration tests
    await run_integration_tests()
    
    print("\nTest suite completed!")


if __name__ == "__main__":
    asyncio.run(main())
