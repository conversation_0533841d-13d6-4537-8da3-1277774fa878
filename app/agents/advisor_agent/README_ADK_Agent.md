# Dynamic Query ADK Agent

A custom Google ADK (Agent Development Kit) agent that specializes in converting natural language queries into structured JSON queries and executing them against the wealth management database.

## Overview

The `DynamicQueryADKAgent` is a sophisticated AI agent built using Google's ADK framework that:

- Converts natural language questions into structured JSON queries
- Validates query syntax and structure
- Executes queries against the wealth management API
- Provides intelligent error handling and suggestions
- Maintains conversation context for multi-turn interactions

## Features

### Core Capabilities

- **Natural Language Processing**: Understands complex financial queries in plain English
- **JSON Query Generation**: Converts queries to structured JSON format following the API schema
- **Query Validation**: Validates generated queries against the database schema
- **Query Execution**: Executes validated queries against the wealth management API
- **Error Handling**: Provides meaningful error messages and suggestions
- **Context Awareness**: Maintains conversation history for better understanding

### Supported Query Types

- Simple data retrieval (accounts, transactions, portfolios)
- Complex filtering with multiple conditions
- Aggregations (count, sum, average, min, max)
- Sorting and pagination
- Joins across related tables
- Full-text search capabilities
- Date range queries
- Grouping and statistical analysis

## Installation and Setup

### Prerequisites

- Python 3.12+
- Google Cloud Project with Vertex AI enabled
- Required dependencies (see `pyproject.toml`)

### Environment Variables

Ensure the following environment variables are set:

```bash
GOOGLE_CLOUD_PROJECT=your-project-id
GOOGLE_CLOUD_LOCATION=your-region
GOOGLE_GENAI_USE_VERTEXAI=TRUE
```

### Dependencies

The agent requires the following key dependencies:

```python
google-adk>=1.8.0
google-genai>=1.27.0
httpx>=0.28.1
jsonschema>=4.25.0
```

## Usage

### Basic Usage

```python
import asyncio
from agents.advisor_agent.dynamic_query_adk_agent import DynamicQueryADKAgent

async def main():
    # Create an agent instance
    agent = DynamicQueryADKAgent()
    
    # Execute a query
    response = await agent.chat("Get all accounts from California with balance over 5000")
    print(response)

asyncio.run(main())
```

### With Conversation Context

```python
# Previous conversation context
previous_messages = [
    {
        "role": "user",
        "content": "I'm looking for high-value accounts"
    },
    {
        "role": "assistant", 
        "content": "I can help you find high-value accounts. What criteria would you like to use?"
    }
]

# Create agent with context
agent = DynamicQueryADKAgent(previous_messages=previous_messages)
response = await agent.chat("Show me accounts with balance over 100000 in New York")
```

### Convenience Function

```python
from agents.advisor_agent.dynamic_query_adk_agent import execute_dynamic_query_with_adk

# Quick query execution
response = await execute_dynamic_query_with_adk("Get accounts owned by John Smith")
```

## API Reference

### DynamicQueryADKAgent Class

#### Constructor

```python
DynamicQueryADKAgent(previous_messages: Union[Dict, List[Dict]] = None)
```

- `previous_messages`: Optional conversation context for multi-turn interactions

#### Methods

##### `chat(user_query: str) -> str`

Execute a natural language query and return the response.

- **Parameters**: `user_query` - Natural language query string
- **Returns**: Agent's response as a string
- **Example**: `await agent.chat("Show me all accounts in Texas")`

##### `generate_and_execute_query(user_query: str) -> Dict`

Generate and execute a query, returning detailed results.

- **Parameters**: `user_query` - Natural language query string
- **Returns**: Dictionary with success status, response, and metadata
- **Example**: `result = await agent.generate_and_execute_query("Get top 10 accounts")`

### Convenience Functions

##### `execute_dynamic_query_with_adk(query: str, previous_messages: List[Dict] = None) -> str`

Quick execution of a dynamic query without creating an agent instance.

- **Parameters**: 
  - `query` - Natural language query string
  - `previous_messages` - Optional conversation context
- **Returns**: Query response as a string

## Example Queries

### Simple Queries

```python
# Basic data retrieval
"Get all accounts from California"
"Show me transactions for account 12345"
"Find portfolios with value over 1 million"

# Filtering
"Get accounts with balance between 10000 and 50000"
"Show me accounts created in the last month"
"Find clients older than 65"
```

### Complex Queries

```python
# Aggregations
"What's the total balance of all accounts in New York?"
"How many transactions were made last quarter?"
"Show me the average portfolio value by client age group"

# Joins and relationships
"Get accounts with their associated transactions"
"Show me clients with their portfolio performance"
"Find accounts and their recent transaction history"

# Advanced filtering
"Get high-value accounts (>100k) with recent activity"
"Show me underperforming portfolios in the tech sector"
"Find accounts with suspicious transaction patterns"
```

## Error Handling

The agent provides intelligent error handling:

```python
# Invalid query structure
response = await agent.chat("Delete all accounts")  # Not supported
# Returns: Error message explaining the limitation

# Ambiguous query
response = await agent.chat("Show me some data")
# Returns: Request for clarification

# Validation errors
# Returns: Specific validation errors and suggestions
```

## Configuration Files

The agent uses several configuration files:

- `prompts/dynamic_query_prompt.md` - Main system instruction template
- `prompts/additional_instructions.txt` - Additional behavioral instructions
- `data/schema.json` - Database schema definition
- `data/functions.json` - Available query functions
- `data/examples.json` - Example queries and responses

## Architecture

### Components

1. **Agent Core**: Google ADK Agent with custom tools
2. **Query Generator**: Converts natural language to JSON
3. **Validator**: Validates query structure against schema
4. **Executor**: Executes queries against the API
5. **Context Manager**: Handles conversation history

### Flow

1. User provides natural language query
2. Agent processes query using system instruction
3. Generated JSON query is validated
4. Valid queries are executed against the API
5. Results are formatted and returned to user

## Comparison with Original Implementation

### Original DynamicQueryGenerator

- Uses raw Google GenAI client
- Manual prompt management
- Basic retry logic
- Limited error handling

### New ADK Agent

- Uses Google ADK framework
- Integrated tool system
- Advanced conversation management
- Rich error handling and validation
- Better context awareness
- Structured response handling

## Testing

Run the example script to test the agent:

```bash
python app/agents/advisor_agent/examples/adk_agent_example.py
```

This will run various test scenarios including:
- Simple queries
- Context-aware conversations
- Batch processing
- Error handling
- Interactive mode

## Best Practices

1. **Query Clarity**: Provide specific, clear queries for best results
2. **Context Usage**: Use conversation context for multi-turn interactions
3. **Error Handling**: Always handle potential errors in production code
4. **Validation**: The agent validates queries, but verify results as needed
5. **Rate Limiting**: Be mindful of API rate limits for batch operations

## Troubleshooting

### Common Issues

1. **Authentication Errors**: Ensure Google Cloud credentials are properly configured
2. **Schema Validation Failures**: Check that queries match the expected schema
3. **API Timeouts**: Increase timeout values for complex queries
4. **Memory Issues**: For large result sets, consider pagination

### Debug Mode

Enable debug logging to troubleshoot issues:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Contributing

When extending the agent:

1. Update schema files if adding new data structures
2. Add examples for new query patterns
3. Update validation rules as needed
4. Test thoroughly with various query types
5. Update documentation

## License

This project follows the same license as the parent project.
