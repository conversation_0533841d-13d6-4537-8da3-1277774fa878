{"functions": {"endsWith": {"arguments": [{"name": "text", "dataType": "string", "isRequired": true, "isMultipleArgType": false, "description": "The string to inspect."}, {"name": "target", "dataType": "string", "isRequired": true, "isMultipleArgType": false, "description": "The string to search for"}, {"name": "position", "dataType": "number", "isRequired": false, "isMultipleArgType": false, "description": "The position to search up to."}], "name": "endsWith", "returnType": "boolean", "description": "Check if string ends with the given target string", "examples": [{"title": "Function usage", "summary": "Check if the text ends with 'cd'", "expression": "endsWith('abcd', 'cd')", "result": "true"}, {"title": "Function usage", "summary": "Check if the text ends with 'b'", "expression": "endsWith('abcd', 'b')", "result": "false"}, {"title": "Function usage", "summary": "Check if the text ends with 'b' for search window of 2 characters", "expression": "endsWith('abcd', 'b', 2)", "result": "true"}]}, "padEnd": {"arguments": [{"name": "text", "dataType": "string", "isRequired": true, "isMultipleArgType": false, "description": "The string to pad."}, {"name": "length", "dataType": "number", "isRequired": true, "isMultipleArgType": false, "description": "The padding length."}, {"name": "chars", "dataType": "string", "isRequired": true, "isMultipleArgType": false, "description": "The string used as padding."}], "name": "padEnd", "returnType": "string", "description": "Pad string on the right side if it's shorter than length. Padding characters are truncated if they exceed length", "examples": [{"title": "Function usage", "summary": "Pad three spaces at the end", "expression": "padEnd('Sam', 6, ' ')", "result": "'Sam   '"}, {"title": "Function usage", "summary": "Add characters to the end of the input text", "expression": "padEnd('Sam', 6, '-+')", "result": "'Sam-+-'"}]}, "padStart": {"arguments": [{"name": "text", "dataType": "string", "isRequired": true, "isMultipleArgType": false, "description": "The string to pad."}, {"name": "length", "dataType": "number", "isRequired": true, "isMultipleArgType": false, "description": "The padding length."}, {"name": "chars", "dataType": "string", "isRequired": true, "isMultipleArgType": false, "description": "The string used as padding."}], "name": "padStart", "returnType": "string", "description": "Pad string on the left side if it's shorter than length. Padding characters are truncated if they exceed length", "examples": [{"title": "Function usage", "summary": "Prepend three spaces", "expression": "padStart('Sam', 6, ' ')", "result": "'   Sam'"}, {"title": "Function usage", "summary": "Prepend given characters to the input text.", "expression": "padStart('Sam', 6, '-+')", "result": "'-+-Sam'"}]}, "replace": {"arguments": [{"name": "input", "dataType": "string", "isRequired": true, "isMultipleArgType": false, "description": "The string to modify."}, {"name": "find", "dataType": "string", "isRequired": true, "isMultipleArgType": false, "description": "The pattern to replace."}, {"name": "replacement", "dataType": "string", "isRequired": true, "isMultipleArgType": false, "description": "The match replacement."}], "name": "replace", "returnType": "string", "description": "Replace matches for pattern in string with replacement", "examples": [{"title": "Function usage", "summary": "Replace '<PERSON>' with '<PERSON>'", "expression": "replace('<PERSON> <PERSON>', '<PERSON>', '<PERSON>')", "result": "'Hi <PERSON>'"}, {"title": "Replace all matched words", "summary": "Replace all occurrences of'<PERSON>' with '<PERSON>'", "expression": "replace('<PERSON> and <PERSON> !', '<PERSON>', '<PERSON>')", "result": "'Hi <PERSON> and <PERSON> !'"}]}, "split": {"arguments": [{"name": "text", "dataType": "string", "isRequired": true, "isMultipleArgType": false, "description": "The string to split."}, {"name": "seperator", "dataType": "string", "isRequired": true, "isMultipleArgType": false, "description": "The separator pattern to split by."}, {"name": "limit", "dataType": "number", "isRequired": false, "isMultipleArgType": false, "description": "The length to truncate results to."}], "name": "split", "returnType": "Array<string>", "description": "Split string by separator", "examples": [{"title": "split function usage", "summary": "split by ',' ", "expression": "split('<PERSON>,<PERSON>,<PERSON>', ',')", "result": "['<PERSON>', '<PERSON>' , '<PERSON>']"}, {"title": "split function usage", "summary": "split by '--' ", "expression": "split('John--<PERSON>--Jr', '--')", "result": "['<PERSON>', '<PERSON>' , '<PERSON>']"}, {"title": "split function usage", "summary": "split for 2 results", "expression": "split('<PERSON>,<PERSON>,<PERSON>', ',', 2)", "result": "['<PERSON>', '<PERSON>']"}]}, "startsWith": {"arguments": [{"name": "text", "dataType": "string", "isRequired": true, "isMultipleArgType": false, "description": "The string to inspect."}, {"name": "target", "dataType": "string", "isRequired": true, "isMultipleArgType": false, "description": "The string to search for."}, {"name": "position", "dataType": "number", "isRequired": false, "isMultipleArgType": false, "description": "The position to search from."}], "name": "startsWith", "returnType": "boolean", "description": "Check if string starts with the given target string", "examples": [{"title": "Function usage", "summary": "Check if the text starts with 'ab'", "expression": "startsWith('abcd', 'ab')", "result": "true"}, {"title": "Function usage", "summary": "Check if the text starts with 'b'", "expression": "startsWith('abcd', 'b')", "result": "false"}, {"title": "Function usage", "summary": "Check if the text starts with 'b' from a given starting position", "expression": "startsWith('abcd', 'b', 1)", "result": "true"}]}, "toLower": {"arguments": [{"name": "text", "dataType": "string", "isRequired": true, "isMultipleArgType": false, "description": "The string to convert."}], "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "Lowercase<T>", "description": "Convert string, as a whole, to lower case", "examples": [{"title": "Function usage", "summary": "Convert string to lower case", "expression": "toLower('<PERSON><PERSON><PERSON> WorlD')", "result": "'hello world'"}, {"title": "Function usage", "summary": "Convert string to lower case", "expression": "toLower('--HeLL<PERSON>-WorlD--')", "result": "'--hello-world--'"}]}, "toUpper": {"arguments": [{"name": "text", "dataType": "string", "isRequired": true, "isMultipleArgType": false, "description": "The string to convert"}], "name": "toUpper", "returnType": "Uppercase<T>", "description": "Convert string, as a whole, to upper case", "examples": [{"title": "Function usage", "summary": "Convert string to upper case", "expression": "toUpper('<PERSON><PERSON><PERSON> Worl<PERSON>')", "result": "'HELLO WORLD'"}, {"title": "Function usage", "summary": "Convert string to upper case", "expression": "toUpper('--He<PERSON><PERSON>-WorlD--')", "result": "'--HELLO-WORLD--'"}]}, "trim": {"arguments": [{"name": "text", "dataType": "string", "isRequired": true, "isMultipleArgType": false, "description": "The string to trim."}, {"name": "characters", "dataType": "string", "isRequired": false, "isMultipleArgType": false, "description": "The characters to trim."}], "name": "trim", "returnType": "string", "description": "Remove leading and trailing whitespace or specified characters from string", "examples": [{"title": "Function usage", "summary": "Reomve spaces from both the ends", "expression": "trim('  abc  ')", "result": "'abc'"}, {"title": "Function usage", "summary": "Reomve all the mached characters from both the ends", "expression": "trim('123abc321', '123')", "result": "'abc'"}, {"title": "Function usage", "summary": "Reomve all the mached characters from both the ends", "expression": "trim('123abc321', '12')", "result": "'3abc3'"}]}, "trimEnd": {"arguments": [{"name": "text", "dataType": "string", "isRequired": true, "isMultipleArgType": false, "description": "The string to trim."}, {"name": "characters", "dataType": "string", "isRequired": false, "isMultipleArgType": false, "description": "The characters to trim."}], "name": "trimEnd", "returnType": "string", "description": "Remove trailing whitespace or specified characters from string", "examples": [{"title": "Function usage", "summary": "Reomve spaces from end of the string", "expression": "trimEnd('  abc  ')", "result": "'  abc'"}, {"title": "Function usage", "summary": "Reomve all the mached characters from end of the string", "expression": "trimEnd('123abc321', '123')", "result": "'123abc'"}, {"title": "Function usage", "summary": "Reomve all the mached characters from end of the string", "expression": "trimEnd('123abc321', '12')", "result": "'123abc3'"}]}, "trimStart": {"arguments": [{"name": "text", "dataType": "string", "isRequired": true, "isMultipleArgType": false, "description": "The string to trim."}, {"name": "characters", "dataType": "string", "isRequired": false, "isMultipleArgType": false, "description": "The characters to trim."}], "name": "trimStart", "returnType": "string", "description": "Remove leading whitespace or specified characters from string", "examples": [{"title": "Function usage", "summary": "Reomve spaces from the beginning of a string", "expression": "trimStart('  abc  ')", "result": "'abc  '"}, {"title": "Function usage", "summary": "Reomve all the mached characters from the beginning of a string", "expression": "trimStart('123abc321', '123')", "result": "'abc123'"}, {"title": "Function usage", "summary": "Reomve all the mached characters from the beginning of a string", "expression": "trimStart('123abc321', '12')", "result": "'3abc321'"}]}, "truncate": {"arguments": [{"name": "text", "dataType": "string", "isRequired": true, "isMultipleArgType": false, "description": "The string to truncate."}, {"name": "options", "dataType": "object", "isRequired": false, "isMultipleArgType": false, "description": "The options object. It can be used to configure length (the maximum string length), omission (the string to indicate text is omitted) and the separator (the separator pattern to truncate to) settings. EX: {'length' : 30, 'omission' = '...', separator = '--'}"}], "name": "truncate", "returnType": "string", "description": "Truncate string if it's longer than the given maximum string length. The last characters of the truncated string are replaced with the omission string which defaults to '...'", "examples": [{"title": "Function with default parameters", "summary": "Truncate string with default settings : maximum length 30, omission string '...'", "expression": "truncate('abcd1234abcd1234abcd1234abcd1234')", "result": "'abcd1234abcd1234abcd1234abc...'"}, {"title": "Function with maximum length and omission optioins", "summary": "Truncate string to maxiumum length 10 with omission string as '**'", "expression": "truncate('abcd1234abcd1234', {'length' : 10, 'omission' : '**'})", "result": "'abcd1234**'"}, {"title": "Function seperator option", "summary": "Truncate string to maxiumum length 10 with omission string as '**' and with seperator '1'", "expression": "truncate('abcd1234abcd1234', {'length' : 10, 'omission' : '**', 'separator' : '1'})", "result": "'abcd**'"}]}, "upperFirst": {"arguments": [{"name": "text", "dataType": "string", "isRequired": true, "isMultipleArgType": false, "description": "The string to convert."}], "name": "upperFirst", "returnType": "Capitalize<T>", "description": "Convert the first character of string to upper case", "examples": [{"title": "Function usage", "summary": "Convert the first character of string to upper case", "expression": "upperFirst('fred')", "result": "'<PERSON>'"}]}, "toString": {"arguments": [{"name": "value", "dataType": "any", "isRequired": true, "isMultipleArgType": true, "description": "The value to convert."}], "name": "toString", "returnType": "string", "description": "Convert `value` to a string. An empty string is returned for `null` and `undefined` values. The sign of `-0` is preserved.", "examples": [{"title": "Function usage", "summary": "Number to string", "expression": "to<PERSON><PERSON>(22)", "result": "'22'"}, {"title": "Function usage", "summary": "Negative number to string", "expression": "toString(-0)", "result": "'-0'"}, {"title": "Function usage", "summary": "Negative number to string", "expression": "toString(-1.23)", "result": "'-1.23'"}, {"title": "Function usage", "summary": "List to string", "expression": "toString([1, 2, 3])", "result": "'1,2,3'"}, {"title": "Function usage", "summary": "Convert null value empty string", "expression": "toString(null)", "result": "''"}]}, "isNull": {"arguments": [{"name": "value", "dataType": "any", "isRequired": true, "isMultipleArgType": true, "description": ""}], "name": "isNull", "returnType": "boolean", "description": "Check if value is null"}, "add": {"arguments": [{"name": "first", "dataType": "number", "isRequired": true, "isMultipleArgType": false, "description": ""}, {"name": "second", "dataType": "number", "isRequired": true, "isMultipleArgType": false, "description": ""}], "name": "add", "returnType": "number", "description": "Add two numbers"}, "divide": {"arguments": [{"name": "dividend", "dataType": "number", "isRequired": true, "isMultipleArgType": false, "description": ""}, {"name": "divisor", "dataType": "number", "isRequired": true, "isMultipleArgType": false, "description": ""}], "name": "divide", "returnType": "number", "description": "Divide two numbers"}, "max": {"arguments": [{"name": "values", "dataType": "Array<number>", "isRequired": true, "isMultipleArgType": false, "description": "List of values"}], "name": "max", "returnType": "number", "description": "Return the maximum value from a list of values"}, "min": {"arguments": [{"name": "values", "dataType": "Array<number>", "isRequired": true, "isMultipleArgType": false, "description": "List of values"}], "name": "min", "returnType": "number", "description": "Return the minimum value from a list of values"}, "multiply": {"arguments": [{"name": "multiplier", "dataType": "number", "isRequired": true, "isMultipleArgType": false, "description": ""}, {"name": "multiplicand", "dataType": "number", "isRequired": true, "isMultipleArgType": false, "description": ""}], "name": "multiply", "returnType": "number", "description": "Multiply two numbers."}, "subtract": {"arguments": [{"name": "first", "dataType": "number", "isRequired": true, "isMultipleArgType": false, "description": ""}, {"name": "second", "dataType": "number", "isRequired": true, "isMultipleArgType": false, "description": ""}], "name": "subtract", "returnType": "number", "description": "Subtract two numbers"}, "sum": {"arguments": [{"name": "value1", "dataType": "number | Array<number>", "isRequired": true, "isMultipleArgType": true, "description": "The first numeric value or a list of numeric values to be summed."}, {"name": "value2", "dataType": "number", "isRequired": false, "isMultipleArgType": false, "description": "The second numeric value to be added when value1 is a single number. Optional if value1 is a list."}], "name": "sum", "returnType": "number", "description": "Returns the sum of numeric values. If `value1` is a single number, `value2` is required. If `value1` is a list of numbers, it returns the sum of the list and `value2` is ignored.", "examples": [{"title": "Function usage", "summary": "Sum two numbers", "expression": "sum(2, 4)", "result": "6"}, {"title": "Function usage", "summary": "sum array of numbers", "expression": "sum([4, 2, 8, 6])", "result": "20"}]}, "isPresent": {"arguments": [{"name": "value", "dataType": "any", "isRequired": true, "isMultipleArgType": true, "description": ""}], "name": "isPresent", "returnType": "boolean", "description": "Check if the value is present (not undefined and not an empty string)"}, "parseDate": {"arguments": [{"name": "dateString", "dataType": "string", "isRequired": true, "isMultipleArgType": false, "description": ""}, {"name": "format", "dataType": "string", "isRequired": true, "isMultipleArgType": false, "description": ""}], "name": "parseDate", "returnType": "Date", "description": "Convert string to date"}, "addDays": {"arguments": [{"name": "date", "dataType": "Date", "isRequired": true, "isMultipleArgType": false, "description": ""}, {"name": "amount", "dataType": "number", "isRequired": true, "isMultipleArgType": false, "description": ""}], "name": "addDays", "returnType": "Date", "description": "Add the specified number of days to the given date"}, "addMonths": {"arguments": [{"name": "date", "dataType": "Date", "isRequired": true, "isMultipleArgType": false, "description": ""}, {"name": "amount", "dataType": "number", "isRequired": true, "isMultipleArgType": false, "description": ""}], "name": "addMonths", "returnType": "Date", "description": "Add the specified number of months to the given date"}, "addYears": {"arguments": [{"name": "date", "dataType": "Date", "isRequired": true, "isMultipleArgType": false, "description": ""}, {"name": "amount", "dataType": "number", "isRequired": true, "isMultipleArgType": false, "description": ""}], "name": "addYears", "returnType": "Date", "description": "Add the specified number of years to the given date"}, "getDay": {"arguments": [{"name": "date", "dataType": "Date", "isRequired": true, "isMultipleArgType": true, "description": ""}], "name": "getDay", "returnType": "", "description": "Get the day of the week of the given date"}, "getWeek": {"arguments": [{"name": "date", "dataType": "Date", "isRequired": true, "isMultipleArgType": false, "description": ""}, {"name": "options", "isRequired": false, "isMultipleArgType": false, "description": ""}], "name": "getWeek", "returnType": "number", "description": "Get the local week index of the given date"}, "getMonth": {"arguments": [{"name": "date", "dataType": "Date", "isRequired": true, "isMultipleArgType": false, "description": ""}], "name": "getMonth", "returnType": "number", "description": "Get the month of the given date"}, "getYear": {"arguments": [{"name": "date", "dataType": "Date", "isRequired": true, "isMultipleArgType": false, "description": ""}], "name": "getYear", "returnType": "number", "description": "Get the year of the given date"}, "formatDate": {"arguments": [{"name": "date", "dataType": "Date", "isRequired": true, "isMultipleArgType": true, "description": ""}, {"name": "format", "dataType": "string", "isRequired": true, "isMultipleArgType": false, "description": ""}], "name": "formatDate", "returnType": "string", "description": "Return the formatted date or datetime string in the given format."}, "today": {"arguments": [], "name": "today", "returnType": "Date", "description": "Get current date"}, "differenceInDays": {"arguments": [{"name": "date1", "dataType": "Date", "isRequired": true, "isMultipleArgType": false, "description": ""}, {"name": "date2", "dataType": "Date", "isRequired": true, "isMultipleArgType": false, "description": ""}], "name": "differenceInDays", "returnType": "number", "description": "Get the number of days between the given dates"}, "differenceInMonths": {"arguments": [{"name": "date1", "dataType": "Date", "isRequired": true, "isMultipleArgType": false, "description": ""}, {"name": "date2", "dataType": "Date", "isRequired": true, "isMultipleArgType": false, "description": ""}], "name": "differenceInMonths", "returnType": "number", "description": "Get the number of months between the given dates"}, "differenceInYears": {"arguments": [{"name": "date1", "dataType": "Date", "isRequired": true, "isMultipleArgType": true, "description": ""}, {"name": "date2", "dataType": "Date", "isRequired": true, "isMultipleArgType": true, "description": ""}], "name": "differenceInYears", "returnType": "number", "description": "Get the number of full years between the given dates"}, "differenceInCalendarYears": {"arguments": [{"name": "date1", "dataType": "Date", "isRequired": true, "isMultipleArgType": false, "description": ""}, {"name": "date2", "dataType": "Date", "isRequired": true, "isMultipleArgType": false, "description": ""}], "name": "differenceInCalendarYears", "returnType": "number", "description": "Get the number of calendar years between the given dates"}, "isToday": {"arguments": [{"name": "date", "dataType": "Date | number", "isRequired": true, "isMultipleArgType": true, "description": ""}], "name": "isToday", "returnType": "", "description": "Is the given date today?"}, "isFirstDayOfMonth": {"arguments": [{"name": "date", "dataType": "Date | number", "isRequired": true, "isMultipleArgType": true, "description": ""}], "name": "isFirstDayOfMonth", "returnType": "", "description": "Is the given date the first day of a month?"}, "isLastDayOfMonth": {"arguments": [{"name": "date", "dataType": "Date", "isRequired": true, "isMultipleArgType": false, "description": ""}], "name": "isLastDayOfMonth", "returnType": "boolean", "description": "Is the given date the last day of a month?"}, "first": {"arguments": [{"name": "array", "dataType": "Array<any>", "isRequired": true, "isMultipleArgType": false, "description": "The array to query"}], "examples": [{"title": "Function usage", "summary": "Returns the first element", "expression": "first([1, 2, 3])", "result": "1"}], "name": "first", "returnType": "any", "description": "Get the first element of an array."}, "join": {"arguments": [{"name": "array", "dataType": "Array<string | number>", "isRequired": true, "isMultipleArgType": true, "description": "The array to convert."}, {"name": "separator", "dataType": "string", "isRequired": true, "isMultipleArgType": false, "description": "The element separator."}], "name": "join", "returnType": "string", "description": "Convert all elements in array into a string separated by separator.", "examples": [{"title": "Function usage", "summary": "Join the elements by '~'", "expression": "join(['a', 'b', 'c'], '~');", "result": "'a~b~c'"}]}, "last": {"arguments": [{"name": "array", "dataType": "Array<any>", "isRequired": true, "isMultipleArgType": false, "description": "The array to query."}], "name": "last", "returnType": "any", "description": "Get the last element of array.", "examples": [{"title": "Function usage", "summary": "Returns the last element.", "expression": "last([1, 2, 3])", "result": "3"}]}, "size": {"arguments": [{"name": "collection", "dataType": "Array<any> | object | string", "isRequired": true, "isMultipleArgType": true, "description": "The collection to inspect"}], "name": "size", "returnType": "number", "description": "Get the size of a collection. For arrays and strings, the size is determined by the number of elements, while for objects, it is determined by the count of their own enumerable properties.", "examples": [{"title": "Function usage", "summary": "Size of an array", "expression": "size([1,2,3])", "result": "3"}, {"title": "Function usage", "summary": "Size of a user object", "expression": "size({ \"name\" : \"<PERSON>\" , \"age\" : 24 })", "result": "2"}, {"title": "Function usage", "summary": "Size of a string", "expression": "size(\"How are you ?\")", "result": "13"}]}, "includes": {"arguments": [{"name": "collection", "dataType": "Array<any>", "isRequired": true, "isMultipleArgType": true, "description": ""}, {"name": "target", "dataType": "any", "isRequired": true, "isMultipleArgType": true, "description": ""}, {"name": "fromIndex", "dataType": "number", "isRequired": false, "isMultipleArgType": false, "description": ""}], "name": "includes", "returnType": "boolean", "description": "Check if `value` is in `collection`. If `collection` is a string, it's checked for a substring of `value`, otherwise is used for equality comparisons."}, "strContains": {"arguments": [{"name": "text", "dataType": "string", "isRequired": true, "isMultipleArgType": false, "description": "Source string"}, {"name": "search", "dataType": "string", "isRequired": true, "isMultipleArgType": false, "description": "The string to search for"}], "name": "strContains", "returnType": "boolean", "description": "Check whether a string contains a search string.", "examples": [{"title": "Function usage", "summary": "Check if the input string contains search string 'date'", "expression": "strContains('invoice date : 2020-10-10', 'date')", "result": "true"}]}, "substring": {"arguments": [{"name": "text", "dataType": "string", "isRequired": true, "isMultipleArgType": false, "description": "Source string"}, {"name": "start", "dataType": "Number", "isRequired": true, "isMultipleArgType": false, "description": "The index of the first character to include in the returned substring"}, {"name": "end", "dataType": "Number", "isRequired": false, "isMultipleArgType": false, "description": "The index of the first character to exclude from the returned substring"}], "name": "substring", "returnType": "string", "description": "Extract a substring from the given string by using the index values.", "examples": [{"title": "Function usage", "summary": "Extract string from index 2 to 4", "expression": "substring('abcdef', 2, 4)", "result": "'cd'"}, {"title": "Function usage", "summary": "Extract string from index 2", "expression": "substring('abcdef', 2)", "result": "'cdef'"}]}, "count": {"arguments": [{"name": "items", "dataType": "Array<any>", "isRequired": true, "isMultipleArgType": false, "description": "List of items"}], "name": "count", "returnType": "number", "description": "Get the count of items in a list. For example, obtain the count of linked objects for an input object.", "examples": [{"title": "Function usage", "summary": "Count the number of owners on the account object, e.g., if the account object is {\"id\" : \"1234\", \"owners\" : [{\"name\" : \"<PERSON>\", \"id\" : \"234\"}, {\"name\" : \"<PERSON>\", \"id\" : \"345\"}]}, then count(owners) should return 2, count(owners.id) should return 2", "expression": "count(owners)", "result": "2"}, {"title": "Function usage", "summary": "Count the number of owners on the account object, e.g., if the account object is {\"id\" : \"1234\", \"owners\" : [{\"name\" : \"<PERSON>\", \"id\" : \"234\"}, {\"name\" : \"<PERSON>\", \"id\" : \"345\"}]}, then count(owners) should return 2, count(owners.id) should return 2", "expression": "count(owners.id)", "result": "2"}]}, "avg": {"arguments": [{"name": "values", "dataType": "Array<number>", "isRequired": true, "isMultipleArgType": false, "description": "List of values"}], "name": "avg", "returnType": "number", "description": "Return the average value from a list of values"}, "absolute": {"arguments": [{"name": "value", "dataType": "number", "isRequired": true, "isMultipleArgType": false, "description": "The numeric to get the absolute value of"}], "name": "absolute", "returnType": "number", "description": "Return the absolute (non-negative) value of a number", "examples": [{"title": "Function usage", "summary": "Absolute value for a negative number", "expression": "absolute(-1.23)", "result": "1.23"}, {"title": "Function usage", "summary": "Absolute value for a postive number", "expression": "absolute(1.23)", "result": "1.23"}]}, "currentDate": {"arguments": [], "name": "currentDate", "returnType": "string", "description": "Return the current ISO date string."}, "currentDatetime": {"arguments": [], "name": "currentDatetime", "returnType": "string", "description": "Return the current ISO date time string."}, "formatDateString": {"arguments": [{"name": "date", "dataType": "string", "isRequired": true, "isMultipleArgType": false, "description": ""}, {"name": "format", "dataType": "string", "isRequired": true, "isMultipleArgType": false, "description": ""}], "name": "formatDateString", "returnType": "string", "description": "Format ISO date string as per given format string."}, "stringLength": {"arguments": [{"name": "text", "dataType": "string", "isRequired": true, "isMultipleArgType": false, "description": "The input string to calculate the length of"}], "name": "stringLength", "returnType": "number", "description": "Calculate the length of a string", "examples": [{"title": "Function usage", "summary": "The length of the input string", "expression": "stringLength(\"abcd\")", "result": "4"}, {"title": "Function usage", "summary": "The length of empty string", "expression": "stringLength(\"\")", "result": "0"}, {"title": "Function usage", "summary": "The length of non string input", "expression": "string<PERSON><PERSON>th(123)", "result": "0"}]}, "now": {"arguments": [], "name": "now", "returnType": "Date", "examples": [{"title": "Function usage", "summary": "Check difference in date values", "expression": "differenceInDays(addDays(now(), 1), now())", "result": "1"}], "description": "Return current date time object"}, "parseDateTime": {"arguments": [{"name": "datetimeString", "dataType": "string", "isRequired": true, "isMultipleArgType": false, "description": "Datetime string"}, {"name": "format", "dataType": "string", "isRequired": false, "isMultipleArgType": false, "description": "If format is not specified, defaults to ISO format"}], "name": "parseDateTime", "returnType": "Date", "description": "Convert string to datetime. If format is not specified, defaults to ISO format", "examples": [{"title": "Function usage", "summary": "Returns datatime in the given format", "expression": "formatDate(parseDateTime('2020-12-30 01:01:01 +05:30', 'yyyy-MM-dd hh:mm:ss TZH:TZM'), 'yyyy/MM/dd hh:mm:ss')", "result": "'2020/12/30 01:01:01'"}, {"title": "Function usage", "summary": "Returns datatime in ISO if format is not given", "expression": "formatDate(parseDateTime('2020-12-30T01:01:01+05:30'), 'yyyy/MM/dd hh:mm:ss')", "result": "'2020/12/30 01:01:01'"}]}, "strContainsIgnoreCase": {"arguments": [{"name": "text", "dataType": "string", "isRequired": true, "isMultipleArgType": false, "description": "The input text to search"}, {"name": "search", "dataType": "string", "isRequired": true, "isMultipleArgType": false, "description": "The search text"}], "name": "strContainsIgnoreCase", "returnType": "boolean", "description": "Check if a string contains a search string, ignoring case sensitivity", "examples": [{"title": "Function usage", "summary": "Check if the search string 'abc' present in the input string 'xyzAbc123'", "expression": "strContains('xyzAbc123', 'abc')", "result": "true"}]}, "coalesce": {"arguments": [{"name": "value1", "dataType": "any", "isRequired": true, "isMultipleArgType": false, "description": "Value1"}, {"name": "value2", "dataType": "any", "isRequired": true, "isMultipleArgType": false, "description": "Value2"}], "name": "coalesce", "returnType": "any", "description": "Returns value1 if there is some data, else return value2", "examples": [{"title": "Function usage", "summary": "Returns value1 as it contains some data, person object { fisrstname : '<PERSON>', lastname : '<PERSON>' }", "expression": "coalesce(person.fisrstname , person.lastname)", "result": "'<PERSON>'"}, {"title": "Function usage", "summary": "Returns value1 as it contains some data, person object without firstname { lastname : '<PERSON>' }", "expression": "coalesce(person.fisrstname , person.lastname)", "result": "'Brown'"}]}, "startsWithIgnoreCase": {"arguments": [{"name": "text", "dataType": "string", "isRequired": true, "isMultipleArgType": false, "description": "The string to inspect."}, {"name": "target", "dataType": "string", "isRequired": true, "isMultipleArgType": false, "description": "The string to search for."}, {"name": "position", "dataType": "number", "isRequired": false, "isMultipleArgType": false, "description": "The position to search from."}], "examples": [{"title": "Function usage", "summary": "Check if the text starts with 'ab'", "expression": "startsWithIgnoreCase('ABCD', 'ab')", "result": "true"}, {"title": "Function usage", "summary": "Check if the text starts with 'b'", "expression": "startsWithIgnoreCase('ABCD', 'b')", "result": "false"}, {"title": "Function usage", "summary": "Check if the text starts with 'b' from a given starting position", "expression": "startsWithIgnoreCase('ABCD', 'b', 1)", "result": "true"}], "returnType": "boolean", "description": "Check if string starts with the given target string, ignoring case sensitivity"}, "endsWithIgnoreCase": {"arguments": [{"name": "text", "dataType": "string", "isRequired": true, "isMultipleArgType": false, "description": "The string to inspect."}, {"name": "target", "dataType": "string", "isRequired": true, "isMultipleArgType": false, "description": "The string to search for."}, {"name": "position", "dataType": "number", "isRequired": false, "isMultipleArgType": false, "description": "The position to search up to."}], "examples": [{"title": "Function usage", "summary": "Check if the text ends with 'cd'", "expression": "endsWithIgnoreCase('ABCD', 'cd')", "result": "true"}, {"title": "Function usage", "summary": "Check if the text ends with 'b'", "expression": "endsWithIgnoreCase('ABCD', 'b')", "result": "false"}, {"title": "Function usage", "summary": "Check if the text ends with 'b' from a given starting position", "expression": "endsWithIgnoreCase('ABCD', 'b', 1)", "result": "true"}, {"title": "Function usage", "summary": "Check if the text ends with 'b' for search window of 2 characters", "expression": "endsWith('ABCD', 'b', 2)", "result": "true"}], "returnType": "boolean", "description": "Check if string ends with the given target string, ignoring case sensitivity"}}}