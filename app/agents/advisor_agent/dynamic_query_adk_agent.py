import os
import sys
import json
import asyncio
from pathlib import Path
from typing import Dict, List, Optional, Union

sys.path.append(str(Path(__file__).parent.parent.parent))

from core.config import AUTH_PROPERTIES, SERVICES_PROPERTIES
from agents.advisor_agent.utils.dynamic_query_validator import QueryValidator
from core.auth import get_headers
from httpx import AsyncClient, Timeout

# Set up Google Cloud environment variables
os.environ["GOOGLE_CLOUD_PROJECT"] = AUTH_PROPERTIES.GOOGLE_CLOUD_PROJECT
os.environ["GOOGLE_CLOUD_LOCATION"] = AUTH_PROPERTIES.GOOGLE_CLOUD_LOCATION
os.environ["GOOGLE_GENAI_USE_VERTEXAI"] = "TRUE"

from google.adk.agents import Agent
from google.adk.agents.types import Message, MessageContent, TextContent

BASE_PATH = os.path.dirname(os.path.abspath(__file__))


async def call_dynamic_query(payload: Dict):
    """Execute a dynamic query against the API."""
    base_url = SERVICES_PROPERTIES.BASE_URL
    bo_name = next(iter(payload.keys()))
    url = base_url + f"/api/domain/wealthdomain/{bo_name}/dynamic/query"
    headers = await get_headers()

    if "select" in payload[bo_name] and "limit" not in payload[bo_name]:
        payload[bo_name]["limit"] = 10

    async with AsyncClient(timeout=Timeout(timeout=10)) as client:
        response = await client.post(url, headers=headers, json=payload)
        response.raise_for_status()
        return response.json()


class DynamicQueryADKAgent:
    """
    A custom Google ADK agent that specializes in dynamic query generation and execution.
    
    This agent converts natural language queries into structured JSON queries,
    validates them, and executes them against the wealth management API.
    """
    
    def __init__(self, previous_messages: Union[Dict, List[Dict]] = None):
        """
        Initialize the Dynamic Query ADK Agent.
        
        Args:
            previous_messages: Optional previous conversation context
        """
        self.previous_messages = previous_messages or []
        self._load_configuration()
        self._create_agent()
    
    def _load_configuration(self):
        """Load all configuration files and prepare the system instruction."""
        # Load prompt template
        prompt_file_path = os.path.join(BASE_PATH, "./prompts/dynamic_query_prompt.md")
        with open(prompt_file_path, "r") as fp:
            self.prompt_template = fp.read()
        
        # Load schema
        self.schema_file_path = os.path.join(BASE_PATH, "./data/schema.json")
        with open(self.schema_file_path, "r") as fp:
            self.schema = json.load(fp)
        
        # Load functions
        functions_file_path = os.path.join(BASE_PATH, "./data/functions.json")
        with open(functions_file_path, "r") as fp:
            self.functions = json.load(fp)
        
        # Load examples
        examples_file_path = os.path.join(BASE_PATH, "./data/examples.json")
        with open(examples_file_path, "r") as fp:
            self.examples = json.load(fp)
        
        # Load additional instructions
        instructions_file_path = os.path.join(BASE_PATH, "./prompts/additional_instructions.txt")
        with open(instructions_file_path, "r") as fp:
            self.additional_instructions = fp.read()
        
        # Prepare the complete system instruction
        self.system_instruction = self._prepare_system_instruction()
    
    def _prepare_system_instruction(self) -> str:
        """Prepare the complete system instruction by replacing placeholders."""
        instruction = self.prompt_template
        instruction = instruction.replace("{{ schema }}", json.dumps(self.schema, indent=2))
        instruction = instruction.replace("{{ functions }}", json.dumps(self.functions, indent=2))
        instruction = instruction.replace("{{ examples }}", json.dumps(self.examples, indent=2))
        instruction = instruction.replace("{{ instructions }}", self.additional_instructions)
        
        # Add context about previous messages if available
        if self.previous_messages:
            context = f"\n\nPrevious conversation context:\n{json.dumps(self.previous_messages, indent=2)}"
            instruction += context
        
        return instruction
    
    def _create_agent(self):
        """Create the Google ADK agent with dynamic query capabilities."""
        self.agent = Agent(
            name="dynamic_query_agent",
            model="gemini-2.0-flash",
            description=(
                "A specialized agent for converting natural language queries into structured JSON queries "
                "and executing them against the wealth management database. This agent understands complex "
                "financial data relationships and can generate efficient queries for accounts, transactions, "
                "portfolios, and other wealth management entities."
            ),
            instruction=self.system_instruction,
            tools=[self._execute_dynamic_query],
            temperature=0.0
        )
    
    async def _execute_dynamic_query(self, query_payload: str) -> str:
        """
        Execute a dynamic query against the wealth management API.
        
        This tool takes a JSON query payload and executes it against the database,
        returning the results in a structured format.
        
        Args:
            query_payload (str): JSON string containing the query structure
            
        Returns:
            str: JSON string containing the query results or error message
        """
        try:
            # Parse the query payload
            if isinstance(query_payload, str):
                payload = json.loads(query_payload)
            else:
                payload = query_payload
            
            # Validate the query
            validator = QueryValidator(self.schema_file_path)
            is_valid, errors = validator.validate(payload)
            
            if not is_valid:
                return json.dumps({
                    "error": "Invalid query structure",
                    "validation_errors": errors,
                    "suggestion": "Please check the query structure against the schema and try again."
                }, indent=2)
            
            # Execute the query
            result = await call_dynamic_query(payload)
            
            return json.dumps({
                "success": True,
                "query": payload,
                "results": result
            }, indent=2)
            
        except json.JSONDecodeError as e:
            return json.dumps({
                "error": "Invalid JSON format",
                "details": str(e),
                "suggestion": "Please provide a valid JSON query structure."
            }, indent=2)
        except Exception as e:
            return json.dumps({
                "error": "Query execution failed",
                "details": str(e),
                "suggestion": "Please check your query and try again."
            }, indent=2)
    
    async def generate_and_execute_query(self, user_query: str) -> Dict:
        """
        Generate a JSON query from natural language and execute it.
        
        Args:
            user_query (str): Natural language query from the user
            
        Returns:
            Dict: Query results or error information
        """
        try:
            # Create message for the agent
            message = Message(
                content=[TextContent(text=user_query)],
                role="user"
            )
            
            # Generate response using the ADK agent
            response = await self.agent.agenerate([message])
            
            # Extract the response content
            if response and response.content:
                response_text = ""
                for content in response.content:
                    if hasattr(content, 'text'):
                        response_text += content.text
                
                return {
                    "success": True,
                    "response": response_text,
                    "agent_name": self.agent.name
                }
            else:
                return {
                    "success": False,
                    "error": "No response generated",
                    "suggestion": "Please try rephrasing your query."
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": f"Agent execution failed: {str(e)}",
                "suggestion": "Please check your query and try again."
            }
    
    async def chat(self, user_query: str) -> str:
        """
        Simple chat interface for the dynamic query agent.
        
        Args:
            user_query (str): User's natural language query
            
        Returns:
            str: Agent's response
        """
        result = await self.generate_and_execute_query(user_query)
        
        if result["success"]:
            return result["response"]
        else:
            return f"Error: {result['error']}\nSuggestion: {result.get('suggestion', 'Please try again.')}"


# Convenience function for direct usage
async def execute_dynamic_query_with_adk(query: str, previous_messages: List[Dict] = None) -> str:
    """
    Execute a dynamic query using the ADK agent.
    
    Args:
        query (str): Natural language query
        previous_messages (List[Dict]): Optional conversation context
        
    Returns:
        str: Query results or response
    """
    agent = DynamicQueryADKAgent(previous_messages=previous_messages)
    return await agent.chat(query)


# Example usage and testing
if __name__ == "__main__":
    async def test_agent():
        """Test the dynamic query ADK agent."""
        agent = DynamicQueryADKAgent()
        
        test_queries = [
            "Get all accounts from California",
            "Show me accounts with balance over 1000 owned by Alice",
            "How many accounts are associated with rep code 8257985?",
            "What is the total balance of all accounts associated with rep code 8257985?"
        ]
        
        for query in test_queries:
            print(f"\n{'='*50}")
            print(f"Query: {query}")
            print(f"{'='*50}")
            
            response = await agent.chat(query)
            print(f"Response: {response}")
    
    # Run the test
    asyncio.run(test_agent())
