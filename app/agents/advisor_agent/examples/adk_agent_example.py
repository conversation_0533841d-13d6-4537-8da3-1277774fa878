#!/usr/bin/env python3
"""
Example script demonstrating how to use the Dynamic Query ADK Agent.

This script shows various ways to interact with the agent:
1. Simple query execution
2. Conversation with context
3. Batch query processing
4. Error handling
"""

import asyncio
import json
import sys
import os
from pathlib import Path

# Add the project root to the path
sys.path.append(str(Path(__file__).parent.parent.parent.parent))

from agents.advisor_agent.dynamic_query_adk_agent import (
    DynamicQueryADKAgent,
    execute_dynamic_query_with_adk
)


async def simple_query_example():
    """Example 1: Simple query execution."""
    print("=" * 60)
    print("EXAMPLE 1: Simple Query Execution")
    print("=" * 60)
    
    # Create an agent instance
    agent = DynamicQueryADKAgent()
    
    # Execute a simple query
    query = "Get all accounts from California with balance over 5000"
    response = await agent.chat(query)
    
    print(f"Query: {query}")
    print(f"Response: {response}")
    print()


async def conversation_with_context_example():
    """Example 2: Conversation with context."""
    print("=" * 60)
    print("EXAMPLE 2: Conversation with Context")
    print("=" * 60)
    
    # Previous conversation context
    previous_messages = [
        {
            "role": "user",
            "content": "I'm looking for high-value accounts"
        },
        {
            "role": "assistant", 
            "content": "I can help you find high-value accounts. What criteria would you like to use?"
        }
    ]
    
    # Create agent with context
    agent = DynamicQueryADKAgent(previous_messages=previous_messages)
    
    # Continue the conversation
    query = "Show me accounts with balance over 100000 in New York"
    response = await agent.chat(query)
    
    print(f"Previous context: {json.dumps(previous_messages, indent=2)}")
    print(f"Current query: {query}")
    print(f"Response: {response}")
    print()


async def batch_query_example():
    """Example 3: Batch query processing."""
    print("=" * 60)
    print("EXAMPLE 3: Batch Query Processing")
    print("=" * 60)
    
    agent = DynamicQueryADKAgent()
    
    queries = [
        "How many accounts do we have in total?",
        "What's the average account balance?",
        "Show me the top 5 accounts by balance",
        "Get all accounts created in the last month",
        "Find accounts with zero balance"
    ]
    
    for i, query in enumerate(queries, 1):
        print(f"Query {i}: {query}")
        response = await agent.chat(query)
        print(f"Response: {response}")
        print("-" * 40)


async def error_handling_example():
    """Example 4: Error handling."""
    print("=" * 60)
    print("EXAMPLE 4: Error Handling")
    print("=" * 60)
    
    agent = DynamicQueryADKAgent()
    
    # Test with an ambiguous query
    ambiguous_query = "Show me some data"
    response = await agent.chat(ambiguous_query)
    
    print(f"Ambiguous query: {ambiguous_query}")
    print(f"Response: {response}")
    print()
    
    # Test with an invalid request
    invalid_query = "Delete all accounts"  # This should not be supported
    response = await agent.chat(invalid_query)
    
    print(f"Invalid query: {invalid_query}")
    print(f"Response: {response}")
    print()


async def convenience_function_example():
    """Example 5: Using the convenience function."""
    print("=" * 60)
    print("EXAMPLE 5: Convenience Function")
    print("=" * 60)
    
    # Use the convenience function for quick queries
    query = "Get accounts owned by John Smith"
    response = await execute_dynamic_query_with_adk(query)
    
    print(f"Query: {query}")
    print(f"Response: {response}")
    print()


async def advanced_query_example():
    """Example 6: Advanced queries with joins and aggregations."""
    print("=" * 60)
    print("EXAMPLE 6: Advanced Queries")
    print("=" * 60)
    
    agent = DynamicQueryADKAgent()
    
    advanced_queries = [
        "Show me accounts with their associated transactions for the last quarter",
        "Get the total portfolio value for each client",
        "Find accounts with more than 10 transactions this month",
        "Show me the average transaction amount by account type",
        "Get clients with multiple accounts and their total balance"
    ]
    
    for query in advanced_queries:
        print(f"Advanced Query: {query}")
        response = await agent.chat(query)
        print(f"Response: {response}")
        print("-" * 40)


async def interactive_mode():
    """Interactive mode for testing queries."""
    print("=" * 60)
    print("INTERACTIVE MODE")
    print("=" * 60)
    print("Enter your queries (type 'quit' to exit):")
    
    agent = DynamicQueryADKAgent()
    
    while True:
        try:
            query = input("\nQuery: ").strip()
            
            if query.lower() in ['quit', 'exit', 'q']:
                print("Goodbye!")
                break
            
            if not query:
                continue
            
            response = await agent.chat(query)
            print(f"Response: {response}")
            
        except KeyboardInterrupt:
            print("\nGoodbye!")
            break
        except Exception as e:
            print(f"Error: {e}")


async def main():
    """Run all examples."""
    print("Dynamic Query ADK Agent Examples")
    print("=" * 60)
    
    try:
        # Run all examples
        await simple_query_example()
        await conversation_with_context_example()
        await batch_query_example()
        await error_handling_example()
        await convenience_function_example()
        await advanced_query_example()
        
        # Ask if user wants interactive mode
        print("\nWould you like to try interactive mode? (y/n): ", end="")
        choice = input().strip().lower()
        
        if choice in ['y', 'yes']:
            await interactive_mode()
        
    except Exception as e:
        print(f"Error running examples: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # Run the examples
    asyncio.run(main())
