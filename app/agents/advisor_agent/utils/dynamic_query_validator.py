import json
import os
from typing import Dict, List, Any, Optional, Set, Union
from dataclasses import dataclass
from enum import StrEnum



class OperationType(StrEnum):
    """Enum for valid operation types"""
    INSERT = "insert"
    SELECT = "select"
    SELECT_SINGLE = "selectsingle"
    UPDATE = "update"
    DELETE = "delete"
    AGGREGATE = "aggregate"
    COUNT = "count"
    DISTINCT = "distinct"
    FILTER = "filter"
    ORDER_BY = "orderBy"
    GROUP_BY = "groupBy"
    LIMIT = "limit"
    OFFSET = "offset"
    JOIN = "join"


@dataclass
class ValidationError:
    """Class to hold validation error details"""
    path: str
    error_type: str
    message: str
    
    def __str__(self):
        return f"[{self.error_type}] at '{self.path}': {self.message}"


class QueryValidator:
    """Main validator class for JSON query structures"""

    def __init__(self, schema_path: Optional[str] = None):
        """
        Initialize validator

        Args:
            schema_path: Path to the JSON schema file. If None, uses default path.
        """
        self.errors: List[ValidationError] = []

        # Define valid operations
        self.valid_operations = {op.value for op in OperationType}

        # Define operations that can contain nested structures
        self.nested_operations = {OperationType.SELECT, OperationType.INSERT, OperationType.UPDATE, OperationType.JOIN}

        # Define operations that expect specific value types
        self.operation_value_types = {
            "filter": str,
            "orderBy": str,
            "groupBy": list,
            "limit": int,
            "offset": int,
            "count": bool,
            "distinct": (dict)
        }

        # Load schema for table and column validation
        self.schema = None
        self.valid_tables: Set[str] = set()
        self.table_columns: Dict[str, Set[str]] = {}
        self._load_schema(schema_path)

    def _load_schema(self, schema_path: Optional[str] = None):
        """
        Load JSON schema and extract table and column information

        Args:
            schema_path: Path to the JSON schema file
        """
        if schema_path is None:
            # Default path relative to this file
            current_dir = os.path.dirname(os.path.abspath(__file__))
            schema_path = os.path.join(current_dir, "..", "prompts", "schema.json")

        try:
            with open(schema_path, 'r') as f:
                self.schema = json.load(f)

            # Extract table and column information from schema definitions
            if 'definitions' in self.schema:
                for table_name, table_def in self.schema['definitions'].items():
                    if isinstance(table_def, dict) and 'properties' in table_def:
                        self.valid_tables.add(table_name)
                        self.table_columns[table_name] = set(table_def['properties'].keys())

                        # Also extract nested object properties for relationship validation
                        for prop_name, prop_def in table_def['properties'].items():
                            if isinstance(prop_def, dict) and '$ref' in prop_def:
                                # This is a reference to another table/object
                                ref_name = prop_def['$ref'].split('/')[-1]
                                if ref_name in self.schema['definitions']:
                                    ref_def = self.schema['definitions'][ref_name]
                                    if isinstance(ref_def, dict) and 'properties' in ref_def:
                                        # Add nested properties with dot notation
                                        for nested_prop in ref_def['properties'].keys():
                                            self.table_columns[table_name].add(f"{prop_name}.{nested_prop}")

        except FileNotFoundError:
            # Schema file not found - validation will continue without schema validation
            pass
        except json.JSONDecodeError:
            # Invalid JSON in schema file - validation will continue without schema validation
            pass
        except Exception:
            # Any other error - validation will continue without schema validation
            pass

    def validate(self, query_json: Union[str, Dict]) -> tuple[bool, List[ValidationError]]:
        """
        Validate the JSON query structure
        
        Args:
            query_json: JSON string or dictionary to validate
            
        Returns:
            Tuple of (is_valid, list_of_errors)
        """
        self.errors = []
        
        # Parse JSON if string
        if isinstance(query_json, str):
            try:
                query_dict = json.loads(query_json)
            except json.JSONDecodeError as e:
                self.errors.append(ValidationError(
                    path="root",
                    error_type="JSON_PARSE_ERROR",
                    message=f"Invalid JSON: {str(e)}"
                ))
                return False, self.errors
        else:
            query_dict = query_json
            
        # Validate structure
        self._validate_structure(query_dict, "root")
        
        return len(self.errors) == 0, self.errors
    
    def _validate_structure(self, obj: Any, path: str):
        """Recursively validate the query structure"""
        
        if not isinstance(obj, dict):
            self.errors.append(ValidationError(
                path=path,
                error_type="TYPE_ERROR",
                message=f"Expected dictionary but got {type(obj).__name__}"
            ))
            return
            
        # Check if this is a table-level object
        if path == "root":
            # Root should contain table names
            if not obj:
                self.errors.append(ValidationError(
                    path=path,
                    error_type="EMPTY_QUERY",
                    message="Query cannot be empty"
                ))
                return
                
            for table_name, table_ops in obj.items():
                # Validate table name against schema
                self._validate_table_name(table_name, f"{path}.{table_name}")
                self._validate_table_operations(table_ops, f"{path}.{table_name}", table_name)
        else:
            # This might be a nested structure
            self._validate_nested_structure(obj, path)
    
    def _validate_table_name(self, table_name: str, path: str):
        """
        Validate table name against schema

        Args:
            table_name: Name of the table to validate
            path: Current validation path for error reporting
        """
        if self.schema and self.valid_tables and table_name not in self.valid_tables:
            self.errors.append(ValidationError(
                path=path,
                error_type="INVALID_TABLE",
                message=f"Table '{table_name}' is not defined in the schema. Valid tables are: {', '.join(sorted(self.valid_tables))}"
            ))

    def _validate_table_operations(self, table_ops: Any, path: str, table_name: str = None):
        """
        Validate operations for a specific table

        Args:
            table_ops: Operations dictionary for the table
            path: Current validation path for error reporting
            table_name: Name of the table being validated (for column validation)
        """

        if not isinstance(table_ops, dict):
            self.errors.append(ValidationError(
                path=path,
                error_type="TYPE_ERROR",
                message=f"Table operations must be a dictionary, got {type(table_ops).__name__}"
            ))
            return

        # Check if distinct operation is present
        has_distinct = OperationType.DISTINCT in table_ops
        if has_distinct and len(table_ops) > 1:
            other_operations = [op for op in table_ops.keys() if op != OperationType.DISTINCT]
            self.errors.append(ValidationError(
                path=path,
                error_type="OPERATION_CONFLICT",
                message=f"When 'distinct' operation is present, no other operations are allowed. Found: {', '.join(other_operations)}"
            ))
            return

        # Check for invalid operations
        for op_name, op_value in table_ops.items():
            if op_name not in self.valid_operations:
                self.errors.append(ValidationError(
                    path=f"{path}.{op_name}",
                    error_type="INVALID_OPERATION",
                    message=f"'{op_name}' is not a valid operation. Valid operations are: {', '.join(sorted(self.valid_operations))}"
                ))
                continue

            # Validate operation values
            self._validate_operation_value(op_name, op_value, f"{path}.{op_name}", table_name)
    
    def _validate_operation_value(self, op_name: str, op_value: Any, path: str, table_name: str = None):
        """
        Validate the value for a specific operation

        Args:
            op_name: Name of the operation
            op_value: Value of the operation
            path: Current validation path for error reporting
            table_name: Name of the table being validated (for column validation)
        """

        # Check nested operations
        if op_name in self.nested_operations:
            if op_name == OperationType.SELECT:
                self._validate_select_structure(op_value, path, table_name)
            elif op_name == OperationType.INSERT:
                self._validate_insert_structure(op_value, path, table_name)
            elif op_name == OperationType.UPDATE:
                self._validate_update_structure(op_value, path, table_name)
            elif op_name == OperationType.JOIN:
                self._validate_join_structure(op_value, path, table_name)

        # Check value type operations
        elif op_name in self.operation_value_types:
            expected_type = self.operation_value_types[op_name]
            if not isinstance(op_value, expected_type):
                if isinstance(expected_type, tuple):
                    type_names = " or ".join(t.__name__ for t in expected_type)
                else:
                    type_names = expected_type.__name__

                self.errors.append(ValidationError(
                    path=path,
                    error_type="TYPE_ERROR",
                    message=f"Operation '{op_name}' expects {type_names}, got {type(op_value).__name__}"
                ))
            elif op_name == OperationType.GROUP_BY:
                self._validate_group_by_fields(op_value, path, table_name)
    
    def _validate_select_structure(self, select_obj: Any, path: str, table_name: str = None):
        """
        Validate SELECT operation structure

        Args:
            select_obj: The select operation object
            path: Current validation path for error reporting
            table_name: Name of the table being validated (for column validation)
        """

        if not isinstance(select_obj, dict):
            self.errors.append(ValidationError(
                path=path,
                error_type="TYPE_ERROR",
                message=f"SELECT operation must be a dictionary, got {type(select_obj).__name__}"
            ))
            return

        for field_name, field_value in select_obj.items():
            field_path = f"{path}.{field_name}"

            # Validate column name against schema
            self._validate_column_name(field_name, field_path, table_name)

            # Field value can be:
            # 1. Boolean (true/false for simple selection)
            # 2. Dictionary (for nested selection with possible filter)

            if isinstance(field_value, bool):
                # Simple field selection
                pass
            elif isinstance(field_value, dict):
                # Nested selection - determine the related table for nested validation
                nested_table = self._get_nested_table_name(field_name, table_name)

                if OperationType.SELECT in field_value:
                    self._validate_select_structure(field_value["select"], f"{field_path}.select", nested_table)

                if "filter" in field_value:
                    if not isinstance(field_value["filter"], str):
                        self.errors.append(ValidationError(
                            path=f"{field_path}.filter",
                            error_type="TYPE_ERROR",
                            message=f"Filter must be a string, got {type(field_value['filter']).__name__}"
                        ))

                # Check for invalid keys in nested selection
                valid_nested_keys = {"select", "filter"}
                invalid_keys = set(field_value.keys()) - valid_nested_keys
                if invalid_keys:
                    self.errors.append(ValidationError(
                        path=field_path,
                        error_type="INVALID_KEYS",
                        message=f"Invalid keys in nested selection: {', '.join(invalid_keys)}. Valid keys are: {', '.join(valid_nested_keys)}"
                    ))
            else:
                self.errors.append(ValidationError(
                    path=field_path,
                    error_type="TYPE_ERROR",
                    message=f"Field value must be boolean or dictionary, got {type(field_value).__name__}"
                ))
    
    def _validate_column_name(self, column_name: str, path: str, table_name: str = None):
        """
        Validate column name against schema

        Args:
            column_name: Name of the column to validate
            path: Current validation path for error reporting
            table_name: Name of the table being validated
        """
        if (self.schema and self.table_columns and table_name and
            table_name in self.table_columns and
            column_name not in self.table_columns[table_name]):

            # Check if it's a nested field (contains dot)
            if '.' not in column_name:
                # Filter out nested column names (those containing dots) for error message
                direct_columns = [col for col in self.table_columns[table_name] if '.' not in col]
                self.errors.append(ValidationError(
                    path=path,
                    error_type="INVALID_COLUMN",
                    message=f"Column '{column_name}' is not defined for table '{table_name}'. Valid columns are: {', '.join(sorted(direct_columns))}"
                ))

    def _get_nested_table_name(self, field_name: str, table_name: str = None) -> str:
        """
        Get the table name for a nested field based on schema references

        Args:
            field_name: Name of the field that might reference another table
            table_name: Current table name

        Returns:
            Name of the referenced table or None if not found
        """
        if not self.schema or not table_name or table_name not in self.schema.get('definitions', {}):
            return None

        table_def = self.schema['definitions'][table_name]
        if 'properties' not in table_def or field_name not in table_def['properties']:
            return None

        field_def = table_def['properties'][field_name]
        if isinstance(field_def, dict) and '$ref' in field_def:
            return field_def['$ref'].split('/')[-1]

        return None

    def _validate_insert_structure(self, insert_obj: Any, path: str, table_name: str = None):
        """Validate INSERT operation structure"""

        if not isinstance(insert_obj, (dict, list)):
            self.errors.append(ValidationError(
                path=path,
                error_type="TYPE_ERROR",
                message=f"INSERT operation must be a dictionary or list, got {type(insert_obj).__name__}"
            ))
            return

        # Validate column names in INSERT operations
        if isinstance(insert_obj, dict):
            for column_name in insert_obj.keys():
                self._validate_column_name(column_name, f"{path}.{column_name}", table_name)
        elif isinstance(insert_obj, list):
            for i, item in enumerate(insert_obj):
                if isinstance(item, dict):
                    for column_name in item.keys():
                        self._validate_column_name(column_name, f"{path}[{i}].{column_name}", table_name)

    def _validate_update_structure(self, update_obj: Any, path: str, table_name: str = None):
        """Validate UPDATE operation structure"""

        if not isinstance(update_obj, dict):
            self.errors.append(ValidationError(
                path=path,
                error_type="TYPE_ERROR",
                message=f"UPDATE operation must be a dictionary, got {type(update_obj).__name__}"
            ))
            return

        # Validate column names in UPDATE operations
        for column_name in update_obj.keys():
            self._validate_column_name(column_name, f"{path}.{column_name}", table_name)

    def _validate_join_structure(self, join_obj: Any, path: str, table_name: str = None):
        """Validate JOIN operation structure"""

        if not isinstance(join_obj, (dict, list)):
            self.errors.append(ValidationError(
                path=path,
                error_type="TYPE_ERROR",
                message=f"JOIN operation must be a dictionary or list, got {type(join_obj).__name__}"
            ))
            return

        # Additional JOIN validation logic can be added here
    
    def _validate_group_by_fields(self, group_by_value: Any, path: str, table_name: str = None):
        """
        Validate groupBy fields to ensure only first-level fields are used and validate against schema

        Args:
            group_by_value: The groupBy value to validate
            path: Current validation path for error reporting
            table_name: Name of the table being validated (for column validation)
        """

        def check_field_for_dots(field: str, field_path: str):
            """Check if a field contains dots (nested field paths)"""
            if '.' in field:
                self.errors.append(ValidationError(
                    path=field_path,
                    error_type="NESTED_FIELD_ERROR",
                    message=f"groupBy only supports first-level fields. Nested field paths like '{field}' are not allowed. Use only field names without dots."
                ))

        # groupBy must be a list of strings
        if isinstance(group_by_value, list):
            for i, field in enumerate(group_by_value):
                field_path = f"{path}[{i}]"
                if isinstance(field, str):
                    check_field_for_dots(field, field_path)
                    # Validate column name against schema (only for first-level fields)
                    if '.' not in field:
                        self._validate_column_name(field, field_path, table_name)
                else:
                    self.errors.append(ValidationError(
                        path=field_path,
                        error_type="TYPE_ERROR",
                        message=f"groupBy field must be a string, got {type(field).__name__}"
                    ))

    def _validate_nested_structure(self, obj: Dict, path: str):
        """Validate nested structures within operations"""

        # This method can be extended for specific nested structure validation
        pass


def validate_query(query_json: Union[str, Dict],
                  print_errors: bool = True,
                  schema_path: Optional[str] = None) -> bool:
    """
    Convenience function to validate a query

    Args:
        query_json: JSON string or dictionary to validate
        print_errors: Whether to print errors to console
        schema_path: Path to the JSON schema file for table/column validation

    Returns:
        Boolean indicating if query is valid
    """
    validator = QueryValidator(schema_path=schema_path)
    is_valid, errors = validator.validate(query_json)
    
    if print_errors and errors:
        print("Validation Errors Found:")
        print("-" * 50)
        for error in errors:
            print(f"  {error}")
        print("-" * 50)
        print(f"Total errors: {len(errors)}")
    elif print_errors and is_valid:
        print("✓ Query structure is valid!")
        
    return is_valid


# Example usage and testing
if __name__ == "__main__":
    # Your example query
    example_query = {
        "Account": {
            "select": {
                "id": True,
                "accountNumber": True,
                "primaryOwner": {
                    "select": {
                        "firstName": True,
                        "lastName": True
                    },
                    "filter": "firstName == 'Alice' "
                }
            },
            "filter": "(accountNumber == '1234' || accountNumber == '5678') && balance > 1000",
            "orderBy": "desc(accountNumber)"
        }
    }
    
    # Valid query test
    print("Testing valid query:")
    validate_query(example_query)