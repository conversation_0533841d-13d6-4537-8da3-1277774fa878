import logging
from typing import TypeVar, Optional, Callable, Awaitable, Generic, Any
from abc import ABC, abstractmethod
from dataclasses import dataclass
from pathlib import Path
import diskcache
from datetime import datetime
import os
from core.config import SETTINGS

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Define a generic type for cached values
T = TypeVar("T")
# Define a type for the function that fetches data
AsyncFetchFunc = Callable[[], Awaitable[T]]

DEFAULT_CACHE_DIR = "default"
DEFAULT_TTL = 7 * 24 * 60 * 60


class CacheProvider(ABC, Generic[T]):
    """Abstract base class for cache providers"""

    @abstractmethod
    async def get(self, key: str) -> Optional[T]:
        """
        Get value from cache

        Args:
            key: Cache key
        Returns:
            Cached value or None if not found
        """
        pass

    @abstractmethod
    async def set(self, key: str, value: T, ttl: int) -> bool:
        """
        Set value in cache with TTL

        Args:
            key: Cache key
            value: Value to cache
            ttl: Time to live in seconds
        Returns:
            True if successful
        """
        pass

    @abstractmethod
    async def fetch_or_cached(
        self, key: str, fetch_func: AsyncFetchFunc[T], ttl: int
    ) -> T:
        """
        Fetch fresh data with cache fallback

        Args:
            key: Cache key
            fetch_func: Async function to fetch data
            ttl: Time to live in seconds
        Returns:
            Freshly fetched data or cached data on fetch failure
        Raises:
            RuntimeError: If fetch fails and no cached data available for fallback

        Notes:
            - First attempts to fetch fresh data
            - If fetch fails, attempts to return cached data as fallback
            - If both fetch and fallback fail, raises RuntimeError
        """
        pass

    @abstractmethod
    async def delete(self, key: str) -> bool:
        """
        Delete specific cache entry

        Args:
            key: Cache key
        Returns:
            True if deleted, False if not found
        """
        pass

    @abstractmethod
    async def clear(self) -> None:
        """Clear entire cache"""
        pass


class DiskCacheProvider(CacheProvider[T]):
    """
    Initialize disk cache

    Args:
        cache_dir: Directory to store cache files
    """

    def __init__(self, cache_dir: str = DEFAULT_CACHE_DIR):
        """
        Initialize disk cache

        Args:
            cache_dir: Directory to store cache files
        """
        cache_dir_path = os.path.join(SETTINGS.WORKSPACE_PATH, "cache", cache_dir)
        if not os.path.exists(cache_dir_path):
            os.makedirs(cache_dir_path)

        self.cache_dir = cache_dir_path  # Store the path
        self.cache = diskcache.Cache(
            cache_dir_path,
            timeout=1,  # 1 sec timeout for locks
            disk_min_file_size=0,  # Store all values on disk
        )
        logger.info(f"Initialized disk cache at {cache_dir_path}")

    async def get(self, key: str) -> Optional[T]:
        try:
            value = self.cache.get(key)
            if value is not None:
                logger.debug(f"Cache hit for key: {key}")
            else:
                logger.debug(f"Cache miss for key: {key}")
            return value
        except Exception as e:
            logger.error(f"Error getting from cache for key {key}: {str(e)}")
            return None

    async def set(self, key: str, value: T, ttl: int) -> bool:
        try:
            success = self.cache.set(key, value, expire=ttl)
            logger.debug(f"Cache set for key: {key}, TTL: {ttl}s")
            return success
        except Exception as e:
            logger.error(f"Error setting cache for key {key}: {str(e)}")
            return False

    async def fetch_or_cached(
        self, key: str, fetch_func: AsyncFetchFunc[T], ttl: int
    ) -> T:
        with self.cache.transact():  # Ensure thread safety
            try:
                # Attempt to fetch fresh data
                start_time = datetime.now()
                data = await fetch_func(key)
                fetch_time = (datetime.now() - start_time).total_seconds()

                # Cache the fresh data
                self.cache.set(key, data, expire=ttl)
                logger.info(
                    f"Successfully fetched and cached data for {key} in {fetch_time:.2f}s"
                )
                return data
            except Exception as e:
                # Attempt to get from cache as fallback
                logger.warning(f"Fetch failed for key {key}, attempting cache fallback")
                cached = self.cache.get(key)
                if cached is not None:
                    logger.info(
                        f"Successfully retrieved fallback data from cache for {key}"
                    )
                    return cached

                # If both fetch and cache fallback fail, raise enhanced error
                raise RuntimeError(
                    f"Cache fetch failed for key '{key}' and no cached data available. "
                    f"Original error: {str(e)}"
                ) from e

    async def delete(self, key: str) -> bool:
        try:
            success = self.cache.delete(key)
            if success:
                logger.info(f"Successfully deleted cache for key: {key}")
            return success
        except Exception as e:
            logger.error(f"Error deleting cache for key {key}: {str(e)}")
            return False

    async def clear(self) -> None:
        try:
            self.cache.clear()
            logger.info("Successfully cleared entire cache")
        except Exception as e:
            logger.error(f"Error clearing cache: {str(e)}")
            raise


class CacheService(Generic[T]):
    """Cache service that works with any cache provider"""

    def __init__(self, provider: CacheProvider[T]):
        """
        provider: Cache provider like diskcache/redis..
        """
        self.provider = provider

    async def get(
        self,
        cache_key: str,
        fetch_func: AsyncFetchFunc[T],
        ttl: int = DEFAULT_TTL,
        force_refresh: bool = False,
    ):
        """
        Get cached data or fetch if needed

        Args:
            cache_key: Unique cache identifier

        Returns:
            Cached or freshly fetched data
        """

        if not force_refresh:
            cached = await self.provider.get(cache_key)
            if cached is not None:
                return cached

        return await self.provider.fetch_or_cached(cache_key, fetch_func, ttl)

    async def invalidate(self, key: str) -> bool:
        """Invalidate specific cache entry"""
        return await self.provider.delete(key)

    async def clear(self) -> None:
        """Clear entire cache"""
        await self.provider.clear()


async def main():

    # User cache
    CacheType = dict[str, Any]

    async def fetch_data() -> dict[str, Any]:
        return {"id": "123", "name": "Name1"}

    async def fetch_data_v2() -> dict[str, Any]:
        return {"id": "123", "name": "Name2"}

    user_cache = CacheService[CacheType](
        DiskCacheProvider[CacheType]("../cache/tmp/user")
    )

    # Example 1: Basic caching
    print("Example 1: Basic caching")
    data = await user_cache.get("user_123", fetch_data, force_refresh=True, ttl=1000)
    # Fetch and cache the value
    assert data == {"id": "123", "name": "Name1"}
    # Get result from cache if available
    data = await user_cache.get("user_123", fetch_data_v2)
    assert data == {"id": "123", "name": "Name1"}

    # Example 2: Force refresh a cache value
    print("Example 2: Force refresh a cache value")
    data = await user_cache.get("user_234", fetch_data)
    assert data == {"id": "123", "name": "Name1"}
    data = await user_cache.get("user_234", fetch_data_v2, force_refresh=True)
    assert data == {"id": "123", "name": "Name2"}

    data = await user_cache.get("user_123", fetch_data_v2, force_refresh=True)
    assert data == {"id": "123", "name": "Name2"}

    # Example 3: Cache invalidation
    print("Example 3: Cache invalidation")
    data = await user_cache.get("user_123", fetch_data, force_refresh=True)
    assert data == {"id": "123", "name": "Name1"}
    await user_cache.invalidate("user_123")
    # Get value from fetch function as cache value will not be present
    data = await user_cache.get("user_123", fetch_data_v2)
    assert data == {"id": "123", "name": "Name2"}

    # Example 4: TTL
    print("Example 5: TTL")
    data = await user_cache.get("user_123", fetch_data, force_refresh=True, ttl=1)
    assert data == {"id": "123", "name": "Name1"}
    data = await user_cache.get("user_123", fetch_data_v2)
    assert data == {"id": "123", "name": "Name1"}
    await asyncio.sleep(2)
    data = await user_cache.get("user_123", fetch_data_v2)
    assert data == {"id": "123", "name": "Name2"}

    # Example 5: Clear cache
    print("Example 5: Clear cache")
    data = await user_cache.get("user_123", fetch_data, force_refresh=True)
    assert data == {"id": "123", "name": "Name1"}
    await user_cache.clear()
    data = await user_cache.get("user_123", fetch_data_v2)
    assert data == {"id": "123", "name": "Name2"}
    await user_cache.clear()


if __name__ == "__main__":
    import asyncio

    asyncio.run(main())
