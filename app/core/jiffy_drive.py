import os

from urllib.parse import urljoin
from httpx import AsyncClient

from .config import SERVICES_PROPERTIES
from .auth import get_headers
from .context import global_context


class JiffyDrive:
    def __init__(self):
        self.base_url = urljoin(
            SERVICES_PROPERTIES.JIFFY_DRIVE_URL, "drive/v1/objects/"
        )
        self.output_folder = global_context.get("workspace_folder")

    async def download_file_from_jiffydrive(self, file_path: str, output_path=""):
        url = urljoin(self.base_url, file_path)
        headers = await get_headers()

        if not output_path:
            output_path = os.path.join(self.output_folder, file_path.split("/")[-1])

        async with AsyncClient() as client:
            response = await client.get(url, headers=headers)
            response.raise_for_status()
            with open(output_path, "wb") as f:
                f.write(response.content)
            return output_path
