import os

CONTEXT_WS_PE_FP_DEV = {
    "headers": {
        "X-Jiffy-Tenant-Id": "25b1a56a-3063-48f7-b250-cb19ba3241a8",
        "X-Jiffy-App-Id": "48948481-a706-4124-b8df-67d7a5154eb2",
        "X-Jiffy-User-Id": "47beb676-0943-4d61-8936-6b8ea9114897",
    },
}

CONTEXT_WS_PE_FP_UAT = {
    "headers": {
        "X-Jiffy-App-Id": "4c6f40a6-ba54-41a5-b491-59c210bad519",
        "X-Jiffy-Tenant-Id": "25b1a56a-3063-48f7-b250-cb19ba3241a8",
        "X-Jiffy-User-Id": "47beb676-0943-4d61-8936-6b8ea9114897",
    },
}


CONTEXT_WS_PE_TRIAD_DEV = {
    "headers": {
        "X-Jiffy-App-Id": "335a92d3-d71a-40cb-ade8-5b2fbdb6e0fc",
        "X-Jiffy-Tenant-Id": "f586e710-c5a0-47a3-a501-f0003c813315",
        "X-Jiffy-User-Id": "9800f5b7-3f85-4666-931f-fe467d392671",
    },
}

CONTEXT_WS_TRIAD_UAT = {
    "headers": {
        "X-Jiffy-App-Id": "c780f770-53e5-4ba0-82e9-98a2a51d3e31",
        "X-Jiffy-Tenant-Id": "f1aae9fb-7b95-4445-8aae-a9272f2a170b",
        "X-Jiffy-User-Id": "763d4eb2-9749-4914-845d-266694c73c0a",
    },
}


if os.getenv("API_QUERY_AGENT_ENV", "") == "WS_PE_TRIAD_DEV":
    CONTEXT_DEFAULT = CONTEXT_WS_PE_TRIAD_DEV
elif os.getenv("API_QUERY_AGENT_ENV", "") == "WS_TRIAD_UAT":
    CONTEXT_DEFAULT = CONTEXT_WS_TRIAD_UAT
else:
    CONTEXT_DEFAULT = CONTEXT_WS_PE_FP_UAT

print("LOADING config == ", CONTEXT_DEFAULT)
