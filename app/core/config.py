import os

from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict

BASE_PATH = os.path.dirname(os.path.abspath(__file__))


class Settings(BaseSettings):
    APP_NAME: str = "FastAPI Project"
    DEBUG: bool = True
    WORKSPACE_PATH: str = os.path.join(BASE_PATH, "../../workspace")
    GOOGLE_KEY_FILE_PATH: str = os.path.join(BASE_PATH, "./vertex_ai_keyfile.json")
    
    # HTTP Client timeout settings (in seconds)
    HTTP_CONNECT_TIMEOUT: float = 30.0
    HTTP_READ_TIMEOUT: float = 30.0
    HTTP_WRITE_TIMEOUT: float = 30.0
    HTTP_POOL_TIMEOUT: float = 30.0

    model_config = SettingsConfigDict(extra="allow", env_file=".env")

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

        if not os.path.exists(self.WORKSPACE_PATH):
            os.makedirs(self.WORKSPACE_PATH)


class ServicesProperties(BaseSettings):
    RUN_MODE: str = Field(alias="run.mode", default="PRODUCTION")
    MODEL_REPO_URL: str = Field(alias="model-repository.internal.url")
    IAM_URL: str = Field(alias="iam.url")
    IAM_TENANT_NAME: str = Field(alias="iam.tenantName")
    IAM_SCOPE: str = Field(alias="iam.scope")
    IAM_GRANT_TYPE: str = Field(alias="iam.grantType")
    JIFFY_DRIVE_URL: str = Field(alias="jiffydrive.url")
    BASE_URL: str = Field(alias="baseenv.url")
    PAM_URL: str = Field(alias="pam.url")
    APP_DATA_MANAGER_URL: str = Field(alias="app-data-manager.url")

    model_config = SettingsConfigDict(extra="allow")


class AuthProperties(BaseSettings):
    GOOGLE_CLOUD_PROJECT: str = Field(default="apex-dev-377304")
    GOOGLE_CLOUD_LOCATION: str = Field(default="us-central1")

    SERVICEACCOUNT_NAME: str = Field()
    SERVICEACCOUNT_PASSWORD: str = Field()

    model_config = SettingsConfigDict(
        extra="allow",
        env_file=[
            os.path.join(BASE_PATH, "../../secrets-store/license.properties"),
            os.path.join(BASE_PATH, "../../secrets-store/principal.properties"),
        ],
    )


def load_properties(file_path: str) -> ServicesProperties:
    """
    Function to load the properties from the file
    """
    separator = ":"
    properties = {}
    with open(file_path) as f:
        for line in f:
            if separator in line:
                name, value = line.split(separator, 1)
                properties[name.strip()] = value.strip().strip('"')

    services_properties = ServicesProperties(**properties)
    return services_properties


AUTH_PROPERTIES = AuthProperties()
SETTINGS = Settings()
SERVICES_PROPERTIES = load_properties(
    os.path.join(BASE_PATH, "../../config/services.properties")
)

os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = SETTINGS.GOOGLE_KEY_FILE_PATH
