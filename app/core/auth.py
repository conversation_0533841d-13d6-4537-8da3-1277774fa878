import logging
from httpx import AsyncClient, ConnectTimeout, ReadTimeout

from .context import global_context
from .config import SERVICES_PROPERTIES, AUTH_PROPERTIES, SETTINGS
from .type import Header

logger = logging.getLogger(__name__)


async def generate_token(tenant_id: str, app_id: str, user_id="") -> str:
    headers = {
        "Content-Type": "application/x-www-form-urlencoded",
        "X-Jiffy-Tenant-Id": tenant_id,
        "X-Jiffy-App-Id": app_id,
    }
    if user_id:
        headers["X-Jiffy-User-Id"] = user_id

    params = {"tenantId": SERVICES_PROPERTIES.IAM_TENANT_NAME}

    data = {
        "grant_type": SERVICES_PROPERTIES.IAM_GRANT_TYPE,
        "scope": SERVICES_PROPERTIES.IAM_SCOPE,
        "client_id": AUTH_PROPERTIES.SERVICEACCOUNT_NAME,
        "client_secret": AUTH_PROPERTIES.SERVICEACCOUNT_PASSWORD,
    }

    # Add timeout configuration from settings
    # httpx.Timeout expects a Timeout object or a single float value
    from httpx import Timeout
    timeout_config = Timeout(
        connect=SETTINGS.HTTP_CONNECT_TIMEOUT,
        read=SETTINGS.HTTP_READ_TIMEOUT,
        write=SETTINGS.HTTP_WRITE_TIMEOUT,
        pool=SETTINGS.HTTP_POOL_TIMEOUT
    )
    
    try:
        async with AsyncClient(timeout=timeout_config) as client:
            response = await client.post(
                SERVICES_PROPERTIES.IAM_URL, headers=headers, params=params, data=data
            )
            response.raise_for_status()
            access_token = response.json()["access_token"]
            if not access_token:
                logger.error(f"Failed to generate token: {response.json()}")
                raise Exception("Failed to generate token")

            return "Bearer " + access_token
    except (ConnectTimeout, ReadTimeout) as e:
        logger.error(f"Timeout error when generating token: {e}")
        raise Exception(f"Authentication service timeout: {e}")
    except Exception as e:
        logger.error(f"Error generating token: {e}")
        raise


async def get_headers() -> Header:
    headers = global_context.get("headers")
    if not headers:
        raise ValueError("Headers not found in context")

    headers = headers.copy()
    if "Authorization" not in headers:
        if (
            not headers.get("X-Jiffy-Tenant-Id")
            or not headers.get("X-Jiffy-App-Id")
            or not headers.get("X-Jiffy-User-Id")
        ):
            raise ValueError("Tenant ID, App ID and User ID are required in headers")

        headers["Authorization"] = await generate_token(
            headers["X-Jiffy-Tenant-Id"],
            headers["X-Jiffy-App-Id"],
            headers["X-Jiffy-User-Id"],
        )
        global_context["headers"] = headers

    return headers
