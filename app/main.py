import os
import sys

BASE_PATH = os.path.dirname(os.path.abspath(__file__))
sys.path.append(BASE_PATH)
sys.path.append(os.path.join(BASE_PATH, "../"))

import uvicorn

from fastapi import FastAPI
from fastapi.responses import JSONResponse
from starlette_context.middleware import ContextMiddleware

from middleware.log_request import LogRequestMiddleware
from middleware.plugins import HeaderPlugin, WorkspacePlugin
from services.data_extraction.router import router as data_extraction_router
from services.data_classification.router import router as data_classification_router
from services.audio_transcription.router import router as audio_transcription_router
from services.summarization.router import router as summarization_router
from services.gen_ai_service.router import router as gen_ai_router
from services.dynamic_query.router import router as dynamic_query_router
from services.chat_response.router import router as chat_response_router
# from services.api_query_agent.router import router as api_query_agent_router
from services.new_query_agent.router import router as api_query_agent_router
from services.icon_generation.router import router as icon_generation_router

URL_PREFIX = os.environ.get("URL_PREFIX", "")

app = FastAPI(root_path=f"{URL_PREFIX}/api/v1")

app.add_middleware(
    ContextMiddleware,
    plugins=(
        HeaderPlugin(),
        WorkspacePlugin(),
    ),
)
app.add_middleware(LogRequestMiddleware)

app.include_router(chat_response_router)
app.include_router(data_extraction_router)
app.include_router(data_classification_router)
app.include_router(audio_transcription_router)
app.include_router(summarization_router)
app.include_router(gen_ai_router)
app.include_router(dynamic_query_router)
app.include_router(api_query_agent_router)
app.include_router(icon_generation_router)


@app.get("/health")
async def health_check():
    return JSONResponse(content={"status": "healthy"})


if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
