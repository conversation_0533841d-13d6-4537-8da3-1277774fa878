# FastAPI Project Template

A modern FastAPI project template with SQLAlchemy integration, environment configuration, and best practices.

## Features

- FastAPI framework with async support
- SQLAlchemy ORM
- Pydantic data validation
- Environment configuration
- CORS middleware
- Database migrations support (Alembic)
- Basic project structure
- Authentication ready
- Testing setup with pytest

## Project Structure

```
├── app
│   ├── models/
│   ├── routes/
│   └── database.py
├── main.py
├── config.py
├── requirements.txt
└── README.md
```

## Getting Started

1. Clone the repository
2. Create a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```
3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```
4. Create a `.env` file in the root directory (optional):
   ```
   DEBUG=True
   DATABASE_URL=sqlite:///./app.db
   SECRET_KEY=your-secret-key
   ```
5. Run the application:
   ```bash
   python main.py
   ```

The API will be available at `http://localhost:8000`

## API Documentation

Once the application is running, you can access:
- Interactive API documentation: `http://localhost:8000/docs`
- Alternative API documentation: `http://localhost:8000/redoc`

## Development

### Running Tests
```bash
pytest
```

### Database Migrations
```bash
alembic revision --autogenerate -m "Description"
alembic upgrade head
```

## License

This project is licensed under the MIT License. 