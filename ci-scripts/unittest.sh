#!/bin/bash
set -e

# Create a virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv venv
fi

# Activate the virtual environment
echo "Activating virtual environment..."
source venv/bin/activate

# Install dependencies
echo "Installing dependencies..."
pip install -r requirements.txt
pip install -r requirements-dev.txt

# Set PYTHONPATH to properly locate modules
echo "Setting Python path to make modules accessible..."
BASEPATH=$(pwd)
export PYTHONPATH="${BASEPATH}/app"


echo "Running tests..."
coverage run -m pytest tests/
coverage report -i
coverage xml -i

# Deactivate virtual environment
deactivate