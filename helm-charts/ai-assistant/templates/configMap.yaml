{{- if .Values.enabled }}
{{- $ChartName := .Chart.Name -}}
kind: ConfigMap
apiVersion: v1
metadata:
  name: services-properties-{{ $ChartName }}
  labels:
    apex-app: {{ $ChartName }}
    {{- range $key, $value := .Values.labels }}
    {{- printf "%s: %s" $key (tpl $value $ ) | nindent 4 }}
    {{- end }}
    {{- range $key, $value := .Values.global.extraLabels }}
    {{- printf "%s: %s" $key (tpl $value $ ) | nindent 4 }}
    {{- end }}
data:
  services.properties: |
    jiffydrive.url:{{ .Values.internalUrls.jiffydriveUrl }}
    messenger.url:{{ .Values.internalUrls.messengerUrl }}
    workhorse.url:{{ .Values.internalUrls.workhorseUrl }}
    baseenv.url:{{ .Values.global.jiffyDriveUrl }}

    # ********************* I am properties ******************** #
    iam.url:{{ .Values.internalUrls.iamUrl }}
    iam.grantType:{{ .Values.iamConfigs.grantType }}
    iam.scope:{{ .Values.iamConfigs.scope }}
    iam.tenantName:{{ .Values.global.tenantName }}
    namespace:{{ tpl .Values.global.platformNs . }}
    # ********************* Internal Urls ******************** #
    externalserviceapi.internal.url: {{ .Values.externalServiceApiInternalUrl }}
    model-repository.internal.url: {{ .Values.modelRepositoryInternalUrl }}
    http2:{{ .Values.service.http2 }}
    pam.url: {{ .Values.pamInternalURL }}
    app-data-manager.url:{{ .Values.internalUrls.appDataManagerUrl }}
{{- end }}
