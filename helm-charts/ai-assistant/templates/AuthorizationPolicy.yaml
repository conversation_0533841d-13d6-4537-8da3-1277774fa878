{{- if and (.Values.authorizationPolicy.enabled) (.Values.enabled) }}
{{- $ChartName := .Chart.Name -}}
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: ext-authz-ai-assistant
spec:
  action: CUSTOM
  provider:
    name: {{ tpl .Values.authorizationPolicy.spec.provider.name . }}
  rules:
    - to:
        - operation:
            notMethods:
              - OPTIONS
            notPaths:
              - /ai-assistant/health
              - /ai-assistant/api/v1/health
              - /ai-assistant/docs
              - /ai-assistant/api/v1/docs
              - /ai-assistant/openapi.json
              - /ai-assistant/api/v1/openapi.json

  selector:
    matchLabels:
      apex-app: {{ $ChartName }}

{{- end }}
