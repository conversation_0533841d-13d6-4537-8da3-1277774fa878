{{- if .Values.enabled -}}
{{- $ChartName := .Chart.Name -}}
apiVersion: secrets-store.csi.x-k8s.io/v1
kind: SecretProviderClass
metadata:
  name: {{ $ChartName }}
spec:
  provider: {{ (.Values.global.SecretsCSI.providerName) | default "vault" }}
  parameters:
    vaultAddress: {{ (.Values.global.SecretsCSI.providerURL) | default "http://vault.ops.svc.cluster.local:8200" }}
    roleName: "{{ .Release.Namespace }}-{{ $ChartName }}"
    combinedObjects: |
      - objectName: "principal.properties"
        secretPath: "vault/data/{{ .Release.Namespace }}/platform/{{$ChartName}}/principal/property"
        filePermission: 0777
        format: "property"
      - objectName: "license.properties"
        secretPath: "vault/data/{{ .Release.Namespace }}/platform/{{ $ChartName }}/extra/property"
        filePermission: 0777
        format: "property"
{{- end }}
