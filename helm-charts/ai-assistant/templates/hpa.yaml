{{- if and (.Values.autoscaling.enabled) (.Values.enabled) }}
{{- $ChartName := .Chart.Name -}}
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {{ $ChartName }}
  labels:
    apex-app : {{ $ChartName }}
    {{- range $key, $value := .Values.labels }}
    {{- printf "%s: %s" $key (tpl $value $ ) | nindent 4 }}
    {{- end }}
    {{- range $key, $value := .Values.global.extraLabels }}
    {{- printf "%s: %s" $key (tpl $value $ ) | nindent 4 }}
    {{- end }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ $ChartName }}
  minReplicas: {{ .Values.autoscaling.minReplicas }}
  maxReplicas: {{ .Values.autoscaling.maxReplicas }}
  metrics:
    {{- if .Values.autoscaling.targetCPUUtilizationPercentage }}
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: {{ .Values.autoscaling.targetCPUUtilizationPercentage }}
    {{- end }}
    {{- if .Values.autoscaling.targetMemoryUtilizationPercentage }}
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: {{ .Values.autoscaling.targetMemoryUtilizationPercentage }}
    {{- end }}
    {{- if .Values.autoscaling.targetAverageValue }}
    - type: Object
      object:
        describedObject:
          apiVersion: /v1
          kind: Pod
          name: {{ $ChartName }}_{{ tpl .Values.global.platformNs . }}
        metric:
          name: {{ .Values.autoscaling.scalingParameterName }}
        target:
          averageValue: {{ .Values.autoscaling.targetAverageValue }}
          type: AverageValue
          value: 1
    {{- end }}
{{- end }}
