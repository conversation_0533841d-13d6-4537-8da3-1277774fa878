---
{{- if and (not .Values.ingress.enabled) (.Values.istio.enabled) }}
{{- $ChartName := .Chart.Name -}}
{{- $istio := .Values.istio -}}
{{- $svcPort := .Values.service.port -}}
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: {{ $ChartName }}
spec:
  gateways:
  - {{ $istio.gateway }}
  hosts:
  - {{ .Values.global.ingressHost }}
  {{- range .Values.ingress.rules }}
  http:
  - match:
    {{- range .paths }}
    - uri:
        prefix: {{ .path }}
    route:
    - destination:
        host: {{ $ChartName }}
        port:
          number: {{ $svcPort }}
      headers:
        request:
          set:
            x-forwarded-port: "443"
            x-forwarded-proto: https  
    {{- with $istio.corsPolicy }}
    corsPolicy:
      {{- tpl . $ | nindent 6 }}
    {{- end }}
    {{- end }}
  {{- end }}
{{- end }}