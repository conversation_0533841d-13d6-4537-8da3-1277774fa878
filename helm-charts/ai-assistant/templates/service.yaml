{{- if .Values.enabled }}
{{- $ChartName := .Chart.Name -}}
apiVersion: v1
kind: Service
metadata:
  name: {{ $ChartName }}
  labels:
    apex-app : {{ $ChartName }}
    {{- range $key, $value := .Values.labels }}
    {{- printf "%s: %s" $key (tpl $value $ ) | nindent 4 }}
    {{- end }}
    {{- range $key, $value := .Values.global.extraLabels }}
    {{- printf "%s: %s" $key (tpl $value $ ) | nindent 4 }}
    {{- end }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    apex-app : {{ $ChartName }}

{{- end }}
