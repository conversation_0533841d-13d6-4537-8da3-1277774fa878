{{- if and (.Values.enabled) (.Values.podDisruptionBudget.enabled) (or (gt (int .Values.replicaCount) 1) (and .Values.autoscaling.enabled (ge (int .Values.autoscaling.minReplicas) 2))) }}
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: {{ .Chart.Name }}-pdb
  labels:
    apex-app: {{ .Chart.Name }}
spec:
  {{- if .Values.podDisruptionBudget.minAvailable }}
  minAvailable: {{ .Values.podDisruptionBudget.minAvailable }}
  {{- end }}
  {{- if .Values.podDisruptionBudget.maxUnavailable }}
  maxUnavailable: {{ .Values.podDisruptionBudget.maxUnavailable }}
  {{- end }}
  selector:
    matchLabels:
      apex-app: {{ .Chart.Name }}
{{- end }}