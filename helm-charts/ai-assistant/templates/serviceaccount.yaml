{{- if .Values.enabled -}}
{{- $ChartName := .Chart.Name -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ $ChartName }}
  labels:
    apex-app : {{ $ChartName }}
    {{- range $key, $value := .Values.labels }}
    {{- printf "%s: %s" $key (tpl $value $ ) | nindent 4 }}
    {{- end }}
    {{- range $key, $value := .Values.global.extraLabels }}
    {{- printf "%s: %s" $key (tpl $value $ ) | nindent 4 }}
    {{- end }}
  {{- with .Values.serviceAccount.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
{{- end }}
