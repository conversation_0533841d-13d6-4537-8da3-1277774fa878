{{- if .Values.enabled }}
{{- $ChartName := .Chart.Name -}}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ $ChartName }}
  labels:
    apex-app : {{ $ChartName }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      apex-app : {{ $ChartName }}
  {{- if .Values.updateStrategy.enabled }}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: {{ .Values.updateStrategy.rollingUpdate.maxSurge | default "50%" }}
      maxUnavailable: {{ .Values.updateStrategy.rollingUpdate.maxUnavailable | default "0" }}
  {{- end }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        apex-app : {{ $ChartName }}
        {{- range $key, $value := .Values.labels }}
        {{- printf "%s: %s" $key (tpl $value $ ) | nindent 8 }}
        {{- end }}
        {{- range $key, $value := .Values.global.extraLabels }}
        {{- printf "%s: %s" $key (tpl $value $ ) | nindent 8 }}
        {{- end }}
    spec:
      {{- if .Values.hostAliases }}
      hostAliases:
      {{- range .Values.hostAliases }}
      - ip: {{ .ip }}
        hostnames: {{ .hostnames }}
      {{- end }}
      {{- end }}
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ $ChartName }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            readOnlyRootFilesystem: true
          image: "{{ .Values.global.image.repository }}:{{ .Values.global.image.tag | default .Chart.Version }}"
          imagePullPolicy: {{ .Values.global.image.pullPolicy }}
          {{- if .Values.global.image.env }}
          env: 
          {{- range .Values.global.image.env }}
          - name: {{ .name }}
            value: {{ .value | quote }}
          {{- end }}
          - name: POD_NAME
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: metadata.name
          {{- end }}
          ports:
            - name: http
              containerPort: {{ .Values.service.port }}
              protocol: TCP
          livenessProbe:
            exec:
              command:
              - cat
              - /home/<USER>/app/main.py
            initialDelaySeconds: 3
            periodSeconds: 10
          readinessProbe:
            tcpSocket:
              port: {{ .Values.service.port }}
            periodSeconds: {{ .Values.readinessProbe.periodSeconds }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          volumeMounts: 
            - mountPath: "/home/<USER>/config"
              name: "config"
              readOnly: true
            - mountPath: "/tmp"
              name: "temp"
              subPathExpr: $(POD_NAME)/tmp
            - mountPath: "/home/<USER>/logs"
              name: "temp"
              subPathExpr: $(POD_NAME)/logs
            - mountPath: "/home/<USER>/workspace"
              name: "temp"
              subPathExpr: $(POD_NAME)/tmp-workspace
            - mountPath: "/home/<USER>/DEBUG"
              name: "temp"
              subPathExpr: $(POD_NAME)/tmp-debug
            - mountPath: "/home/<USER>/.config"
              name: "temp"
              subPathExpr: $(POD_NAME)/tmp-config
            {{- toYaml .Values.extraVolumeMounts | nindent 12 }}
          {{- if .Values.prestop }}
          lifecycle:
            preStop:
              exec:
                command: ["python3", "{{ .Values.prestop.script }}"]
          {{- end }}
      volumes:
        - name: "config"
          configMap:
            name: services-properties-{{ $ChartName }}
        - name: "temp"
          emptyDir: {}
        {{- tpl (toYaml .Values.extraVolumes ) . | nindent 8 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.topologySpreadConstraints }}
      topologySpreadConstraints:
        {{- tpl (toYaml .) $ | nindent 8 }}
      {{- end }}
      terminationGracePeriodSeconds: {{ .Values.terminationGracePeriodSeconds | default 30 }}
{{- end }}
