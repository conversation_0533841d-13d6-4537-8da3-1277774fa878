{{- $ChartName := .Chart.Name -}}
apiVersion: v1
kind: Pod
metadata:
  name: {{ $ChartName }}
  labels:
    apex-app : {{ $ChartName }}
    {{- range $key, $value := .Values.labels }}
    {{- printf "%s: %s" $key (tpl $value $ ) | nindent 4 }}
    {{- end }}
    {{- range $key, $value := .Values.global.extraLabels }}
    {{- printf "%s: %s" $key (tpl $value $ ) | nindent 4 }}
    {{- end }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ $ChartName }}:{{ .Values.service.port }}']
  restartPolicy: Never
