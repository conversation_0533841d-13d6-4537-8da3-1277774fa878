# Default values for ai-assistant.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

enabled: false
replicaCount: 1

global:
  image:
    repository: 339876445741.dkr.ecr.ap-south-1.amazonaws.com/ai-assistant
    pullPolicy: Always
    #tag: APEX-DEV-latest
    env:
    - name: PYTHONIOENCODING
      value: "UTF-8"
    - name: PORT
      value: "8000"
    - name: WORKERS
      value: "2"
    - name: URL_PREFIX
      value: "/ai-assistant"
  extraLabels:
    app.kubernetes.io/part-of: apex
  ingressHost: 'dev-minnal.cluster.jiffy.ai'
  jiffyDriveUrl: https://dev-minnal.cluster.jiffy.ai
  iamUrl: https://dev-minnal.cluster.jiffy.ai/apexiam/v1/auth/token
  tenantName: jiffy
  SecretsCSI: {}
  platformNs: "{{ .Release.Namespace }}"
  # providerName: "vault"
  # providerURL: http://vault.ops.svc.cluster.local:8200
  
internalUrls:
  jiffydriveUrl: http://jiffydrive:8080
  messengerUrl: http://workhorse-messenger:5000
  workhorseUrl: http://workhorse:8080
  iamUrl: http://apex-iam:8084/apexiam/v1/auth/token
  appDataManagerUrl: http://app-data-manager:8002

extraVolumeMounts:
  - name: secrets-csi
    mountPath: "/home/<USER>/secrets-store"
    readOnly: true

extraVolumes:
  - name: secrets-csi
    csi:
      driver: secrets-store.csi.k8s.io
      readOnly: true
      volumeAttributes:
        secretProviderClass: "{{ .Chart.Name }}"

hostAliases: []

imagePullSecrets: 
  - name: jiffyairegistry
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: false
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""
podAnnotations: {}


service:
  type: ClusterIP
  port: 8000
  http2: true

istio:
  enabled: true
  gateway: default
  corsPolicy: |
    allowCredentials: true
    allowOrigins:
      - exact: "*"
    allowHeaders:
    - DNT
    - X-CustomHeader
    - Keep-Alive
    - User-Agent
    - X-Requested-With
    - If-Modified-Since
    - Cache-Control
    - Content-Type
    - Authorization
    - X-Jiffy-App-ID
    - X-Jiffy-Tenant-ID
    - X-Jiffy-User-ID
    allowMethods:
    - PUT
    - GET
    - POST
    - PATCH
    - DELETE
    - OPTIONS
    maxAge: 24h

authorizationPolicy:
  enabled: true
  namespace: "{{ tpl .Values.global.platformNs . }}"
  spec:
    provider:
      name: "default-{{ tpl .Values.global.platformNs . }}"

ingress:
  enabled: false
  annotations:
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "1000"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "1000"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "1000"
  rules:
  - paths:
      - path: /ai-assistant
        pathType: Prefix
  className: "nginx"
  tls: []

resources: 
  limits:
    cpu: 1500m
    memory: 2Gi
    ephemeral-storage: 768Mi
  requests:
    cpu: 800m
    memory: 1500Mi
    ephemeral-storage: 512Mi

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 10
  # targetCPUUtilizationPercentage: 70
  targetAverageValue: 1
  scalingParameterName: queue_depth_total

labels:
  app.kubernetes.io/name: ai-assistant
  app.kubernetes.io/version: "{{ .Chart.Version }}"
  app.kubernetes.io/component: backend
  app.kubernetes.io/managed-by: helm
  app.kubernetes.io/created-by: controller-manager

iamConfigs:
  grantType: client_credentials
  scope: "openid email profile"

externalServiceApiInternalUrl: http://externalserviceapi:8657/externalserviceapi/predict/
modelRepositoryInternalUrl: http://model-repository:8383/model-repo/rest/v1
pamInternalURL: http://app-manager:8080

readinessProbe:
  periodSeconds: 10

nodeSelector: {}
affinity:
  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
      - matchExpressions:
        - key: dedicated
          operator: In
          values:
          - tier2-platform
tolerations:
- key: "dedicated"
  operator: "Equal"
  effect: "NoSchedule"
  value: "tier2-platform"
topologySpreadConstraints:
  - maxSkew: 1
    topologyKey: "kubernetes.io/hostname"
    whenUnsatisfiable: DoNotSchedule
    minDomains: 2
    labelSelector:
      matchLabels:
        apex-app: "{{ .Chart.Name }}"

updateStrategy:
  enabled: true
  rollingUpdate:
    maxSurge: 50%
    maxUnavailable: 0

podDisruptionBudget:
  enabled: true
  minAvailable: 1

terminationGracePeriodSeconds: 30