#Custom base image
FROM registry.jiffy.ai/jiffy/jiffybase:24.10.01
ENV DEBIAN_FRONTEND=noninteractive

USER root
RUN   apt-get update &&\
    apt-get install python3-pip=24.0+dfsg-1ubuntu1 -y --no-install-recommends &&\
    rm -rf /var/lib/apt/lists/* &&\
    rm -rf /usr/lib/python3.12/EXTERNALLY-MANAGED

USER jiffy

ENV PATH=/home/<USER>/.local/bin:$PATH
ENV JIFFY_HOME=/home/<USER>
# Set working directory
WORKDIR /home/<USER>

# Copy requirements first to leverage Docker cache
COPY requirements.txt .

SHELL ["/bin/bash", "-o", "pipefail", "-c"]

# Install dependencies
RUN   pip3 install -r /home/<USER>/requirements.txt --no-cache-dir &&\
    find /home/<USER>/ | grep __pyca | xargs rm -rf


# Copy the rest of the application
COPY app /home/<USER>/app
COPY scripts /home/<USER>/scripts

#Setting entry point in docker
ENTRYPOINT ["/bin/bash", "/home/<USER>/scripts/start_server.sh"]