import { describe } from "https://jslib.k6.io/expect/0.0.5/index.js";
import { getServiceSession } from "../../utils/util.js";
import { check } from "k6";

export function aiAssistant(data) {
	let session = getServiceSession();
	describe(" 01 => ai-assistant health check", (t) => {
		let resp = session.get(`/ai-assistant/api/v1/health`);
		t.expect(resp.status).toEqual(200).and(resp).toHaveValidJson();
		check(resp, {
			"verify output  json is correctly returned": (r) =>
				r.body.includes("healthy"),
		});
	});
	return data;
}

/* **************** */
export function smokeTest(data) {
	aiAssistant(data);
}

export function regression(data) {
	aiAssistant(data);
}
