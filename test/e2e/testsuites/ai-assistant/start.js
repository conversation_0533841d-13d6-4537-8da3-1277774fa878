import http from "k6/http";
import { sleep } from "k6";

export { regression } from "./ai-assistant.js";
import { describe } from "https://jslib.k6.io/expect/0.0.4/index.js";
export { handleSummary } from "../../../api-test-harness/k6/utils/result.js";
import { getToken, getTokenJiffyDrive } from "../../utils/util.js";
import { check } from "k6";
import { FormData } from "https://jslib.k6.io/formdata/0.0.2/index.js";

export let options = getOptions(`${__ENV.TEST_NAME}`);

export function getOptions(name) {
	return {
		httpDebug: "full",
		thresholds: {
			checks: [{ threshold: "rate == 1", abortOnFail: false }],
		},
		tags: {
			suite: `${__ENV.SUITE_NAME}`,
		},
		scenarios: {
			integrationTest: {
				executor: "per-vu-iterations",
				exec: name,
				vus: 1,
				iterations: 1,
			},
		},
	};
}

export function setup() {
	return {
		aiAssistant: {},
	};
}
export function teardown(data) {}
