import {Httpx} from 'https://jslib.k6.io/httpx/0.0.4/index.js';

const USERNAME = "<EMAIL>";
const PASSWORD = "password";
const JIFFY_TENANT = "jiffy"
const APP_NAME = 'api-client'
/*
// Changes done to get token from IAM
// parameter 'username' replaced with parameter 'client_id'
// parameter 'password' replaced with parameter 'client_secret'
// added new parameter 'scope' for IAM and commented 'client_id'  as its not required
// changes in auth URL
*/
export function refreshAuthToken(t, session, auth_url, username = USERNAME, password = PASSWORD,
                                tenant = JIFFY_TENANT) {
        let auth_session = new Httpx(
            {
                baseURL: auth_url,
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                }
            }
        );
        let resp = auth_session.post(`/auth/realms/${tenant}/protocol/openid-connect/token`, {
            username: username,
            password: password,
            grant_type: 'password',
            client_id: `${__ENV.platformClientID}`,
            scope: 'openid'
        });

        t.expect(resp.status).as("Auth status").toBeBetween(200, 204)
            .and(resp).toHaveValidJson()
            .and(resp.json('access_token')).as("auth token").toBeTruthy();
        let authToken = resp.json('access_token');
        // set the authorization header on the session for the subsequent requests.
        session.addHeader('Authorization', `Bearer ${authToken}`);
}



export function refreshJiffyAuthToken(t, session, auth_url) {
        let auth_session = new Httpx(
            {
                baseURL: auth_url,
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                }
            }
        );
        let resp = auth_session.post(`/apexiam/v1/auth/token?tenantId=jiffy`, {
            grant_type: 'client_credentials',
            client_id: 'apex-ai-assistant',
            client_secret: '39d898f8-cec8-427b-9d3b-9a0c3bda5009',
            scope: 'openid email profile'
        });

        t.expect(resp.status).as("Auth status").toBeBetween(200, 204)
            .and(resp).toHaveValidJson()
            .and(resp.json('access_token')).as("auth token").toBeTruthy();
        let authToken = resp.json('access_token');
        // set the authorization header on the session for the subsequent requests.
        session.addHeader('Authorization', `Bearer ${authToken}`);
}