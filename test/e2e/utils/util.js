import { describe } from 'https://jslib.k6.io/expect/0.0.5/index.js';

import { Httpx, Request, Get, Post } from 'https://jslib.k6.io/httpx/0.0.4/index.js';

import { refreshAuthToken, refreshJiffyAuthToken } from "./auth.js"




export function getToken() {

    let session = new Httpx({
        baseURL: `${__ENV.APP_URL}`,
        headers: {
            'Content-Type': 'application/json'
        }

    });

    describe('=========== 01 => Refresh auth token', (t) => {
        refreshAuthToken(t, session, `${__ENV.APP_URL}`, `${__ENV.USERNAME}`,
            `${__ENV.PASSWORD}`, `${__ENV.TENANT}`)
    })
    return session

}


/*

Get session for calling internal services

*/

export function getServiceSession() {

    let session = new Httpx({
        baseURL: `${__ENV.AUTH_URL}`,
        headers: {
            'Content-Type': 'application/json'
        }
    });

    describe('=========== 01 => Refresh auth token', (t) => {
        refreshJiffyAuthToken(t, session, `${__ENV.AUTH_URL}`)
    })
    return session
}

/*

Upendra' changes for test data creation in  model repo for application publish

*/

export function getTokenIntegrateApp() {
    let session = new Httpx({
        baseURL: `${__ENV.APP_URL}`,
        headers: {
            'Content-Type': 'application/json',
        }
    });
    return session

}

export function getTokenJiffyDrive() {
    let session = new Httpx({
        baseURL: `${__ENV.APP_URL}`,
        headers: {
            'Content-Type': 'application/json'
        }
    });

    describe('=========== 01 => Refresh auth token', (t) => {
    refreshAuthToken(t, session, `${__ENV.APP_URL}`, `${__ENV.USERNAME}`,
        `${__ENV.PASSWORD}`, `${__ENV.TENANT}`)
    })
    return session

}

