[project]
name = "ai-assistant"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "aiofiles>=24.1.0",
    "chromadb>=1.0.15",
    "diskcache>=5.6.3",
    "fastapi[standard]>=0.116.1",
    "google-adk>=1.8.0",
    "google-genai>=1.27.0",
    "httpx>=0.28.1",
    "jsonschema>=4.25.0",
    "langchain>=0.3.27",
    "langchain-google-vertexai>=2.0.27",
    "langchain-openai>=0.3.28",
    "pillow>=11.3.0",
    "pydantic>=2.11.7",
    "pydantic-settings>=2.10.1",
    "pydash>=8.0.5",
    "starlette-context>=0.4.0",
]

[project.optional-dependencies]
dev = [
    "deepeval>=3.3.2",
]
